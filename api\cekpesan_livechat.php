<?php
session_start();

// Turn off error reporting for clean JSON output
error_reporting(0);
ini_set('display_errors', 0);

// Clean any output that might have been sent
if (ob_get_level()) {
    ob_clean();
}

include '../database/database.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['unread' => 0]);
    exit;
}

$email = $_SESSION['email'];

// Mengambil idchat yang dimiliki oleh pengguna saat ini
$stmt = $conn->prepare("SELECT DISTINCT idchat FROM chats WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

$idchats = [];
while ($row = $result->fetch_assoc()) {
    $idchats[] = $row['idchat'];
}

// Menghitung pesan yang belum dibaca dari pengguna lain dalam idchat yang sama
$unread = 0;
if (!empty($idchats)) {
    $idchats_str = implode(',', array_fill(0, count($idchats), '?'));
    $stmt = $conn->prepare("SELECT COUNT(*) as unread FROM chats WHERE idchat IN ($idchats_str) AND email != ? AND dibaca = 0");
    $params = array_merge($idchats, [$email]);
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $unread = $row['unread'];
}

echo json_encode(['unread' => $unread]);
?>
