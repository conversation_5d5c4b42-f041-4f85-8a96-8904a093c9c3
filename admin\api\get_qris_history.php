<?php
// Start session and include database
session_start();
include '../../database/database.php';

// Set content type
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check admin privileges
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

try {
    // Get pagination parameters - default to 4 items per page for better UX
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 4;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $type_filter = isset($_GET['type']) ? $_GET['type'] : '';
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
    
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    $param_types = '';
    
    if (!empty($search)) {
        $where_conditions[] = "(payment_id LIKE ? OR brand_name LIKE ? OR buyer_reff LIKE ? OR issuer_reff LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
        $param_types .= 'ssss';
    }
    
    if (!empty($type_filter)) {
        $where_conditions[] = "type = ?";
        $params[] = $type_filter;
        $param_types .= 's';
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "transaction_date >= ?";
        $params[] = $date_from . ' 00:00:00';
        $param_types .= 's';
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "transaction_date <= ?";
        $params[] = $date_to . ' 23:59:59';
        $param_types .= 's';
    }
    
    $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM log_qris $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];
    
    // Get paginated data
    $data_sql = "SELECT id, payment_id, amount, type, qris_type, brand_name, buyer_reff, issuer_reff, balance, transaction_date, webhook_sent, created_at 
                 FROM log_qris 
                 $where_clause 
                 ORDER BY transaction_date DESC, id DESC 
                 LIMIT ? OFFSET ?";
    
    $data_stmt = $conn->prepare($data_sql);
    
    // Add limit and offset parameters
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= 'ii';
    
    if (!empty($params)) {
        $data_stmt->bind_param($param_types, ...$params);
    }
    
    $data_stmt->execute();
    $data_result = $data_stmt->get_result();
    
    $qris_logs = [];
    while ($row = $data_result->fetch_assoc()) {
        $qris_logs[] = $row;
    }
    
    // Get summary statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN type = 'CR' THEN amount ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'DR' THEN amount ELSE 0 END) as total_expense,
                    COUNT(CASE WHEN type = 'CR' THEN 1 END) as income_count,
                    COUNT(CASE WHEN type = 'DR' THEN 1 END) as expense_count
                  FROM log_qris $where_clause";
    
    // Get latest balance (saldo terakhir)
    $balance_sql = "SELECT balance FROM log_qris ORDER BY transaction_date DESC, id DESC LIMIT 1";
    $balance_stmt = $conn->prepare($balance_sql);
    $balance_stmt->execute();
    $balance_result = $balance_stmt->get_result();
    $latest_balance = $balance_result->fetch_assoc();
    $current_balance = $latest_balance ? $latest_balance['balance'] : 0;
    
    $stats_stmt = $conn->prepare($stats_sql);
    
    if (!empty($where_conditions)) {
        // Remove limit and offset params for stats
        $stats_params = array_slice($params, 0, -2);
        $stats_param_types = substr($param_types, 0, -2);
        if (!empty($stats_params)) {
            $stats_stmt->bind_param($stats_param_types, ...$stats_params);
        }
    }
    
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    // Add current balance to statistics
    $stats['current_balance'] = $current_balance;
    
    // Return response
    echo json_encode([
        'success' => true,
        'data' => $qris_logs,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total_records,
            'last_page' => ceil($total_records / $limit)
        ],
        'statistics' => $stats
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?> 