<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$targetDir = __DIR__ . "/"; // Path absolut ke direktori saat ini
$image = isset($_POST['image']) ? $_POST['image'] : '';
$type = isset($_POST['type']) ? $_POST['type'] : '';

if ($image && $type) {
    $imagePath = '';
    if ($type === 'local') {
        $imagePath = $targetDir . basename($image);
    } elseif ($type === 'remote') {
        $imagePath = str_replace('https://dopminer.com/Gambar/', $targetDir, $image);
    }

    if (file_exists($imagePath)) {
        if (unlink($imagePath)) {
            echo 'Gambar berhasil dihapus.';
        } else {
            echo 'Gagal menghapus gambar.';
        }
    } else {
        echo 'File tidak ditemukan.';
    }
} else {
    echo 'Parameter tidak valid.';
}
?>
