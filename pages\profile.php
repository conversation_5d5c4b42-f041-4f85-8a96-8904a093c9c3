<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Include database with proper error handling
try {
    include '../database/database.php';
    
    // Check if database connection exists and is active
    if (!isset($conn) || !$conn->ping()) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    $_SESSION['profile_error'] = "Terjadi kesalahan koneksi database. Silakan coba lagi nanti.";
    // Don't exit here, let the page load with error message
}

// Function to get real user IP
if (!function_exists('getUserIP')) {
function getUserIP() {
    // Check for IP from shared internet
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    }
    // Check for IP passed from proxy
    elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    }
    // Check for IP from remote address
    elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    } else {
        $ip = 'Unknown IP';
    }
    
    // If localhost (::1 or 127.0.0.1), try to get public IP
    if ($ip === '::1' || $ip === '127.0.0.1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        $publicIP = getPublicIP();
        if ($publicIP && $publicIP !== 'Unknown') {
            return $publicIP; // Return only public IP
        }
    }
    
    return $ip;
}

// Function to get public IP from external service
function getPublicIP() {
    $services = [
        'https://api.ipify.org',
        'https://ifconfig.me/ip',
        'https://icanhazip.com',
        'https://ident.me'
    ];
    
    foreach ($services as $service) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (compatible; IP Detection)'
            ]
        ]);
        
        $ip = @file_get_contents($service, false, $context);
        if ($ip && filter_var(trim($ip), FILTER_VALIDATE_IP)) {
            return trim($ip);
        }
    }
    
    return 'Unknown';
}
}

$email = $_SESSION['email'];
$success_message = '';
$error_message = '';

// Check for session messages
if (isset($_SESSION['profile_success'])) {
    $success_message = $_SESSION['profile_success'];
    unset($_SESSION['profile_success']); // Remove after displaying
}

if (isset($_SESSION['profile_error'])) {
    $error_message = $_SESSION['profile_error'];
    unset($_SESSION['profile_error']); // Remove after displaying
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['change_email'])) {
        $new_email = trim($_POST['new_email']);
        $reason = trim($_POST['reason']);
        
        if (empty($new_email)) {
            $_SESSION['profile_error'] = "Email baru tidak boleh kosong!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            $_SESSION['profile_error'] = "Format email tidak valid!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } elseif ($new_email === $email) {
            $_SESSION['profile_error'] = "Email baru harus berbeda dengan email lama!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            try {
                // Check if email already exists
                $check_stmt = $conn->prepare("SELECT email FROM users WHERE email = ?");
                if ($check_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$check_stmt->bind_param("s", $new_email)) {
                    throw new Exception("Binding parameters failed: " . $check_stmt->error);
                }
                
                if (!$check_stmt->execute()) {
                    throw new Exception("Execute failed: " . $check_stmt->error);
                }
                
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    $_SESSION['profile_error'] = "Email sudah digunakan oleh user lain!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                }
                
                // Check if there's already a pending request
                $pending_stmt = $conn->prepare("SELECT id FROM email_change_requests WHERE user_email = ? AND status = 'pending'");
                if ($pending_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$pending_stmt->bind_param("s", $email)) {
                    throw new Exception("Binding parameters failed: " . $pending_stmt->error);
                }
                
                if (!$pending_stmt->execute()) {
                    throw new Exception("Execute failed: " . $pending_stmt->error);
                }
                
                $pending_result = $pending_stmt->get_result();
                
                if ($pending_result->num_rows > 0) {
                    $_SESSION['profile_error'] = "Anda sudah memiliki permintaan ganti email yang sedang menunggu approval!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                }
                
                // Insert email change request
                $request_stmt = $conn->prepare("INSERT INTO email_change_requests (user_email, old_email, new_email, reason) VALUES (?, ?, ?, ?)");
                if ($request_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$request_stmt->bind_param("ssss", $email, $email, $new_email, $reason)) {
                    throw new Exception("Binding parameters failed: " . $request_stmt->error);
                }
                
                if (!$request_stmt->execute()) {
                    throw new Exception("Execute failed: " . $request_stmt->error);
                }
                
                // Add to history
                $ip = getUserIP();
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown Agent';
                $history_stmt = $conn->prepare("INSERT INTO profile_history (user_email, change_type, old_value, new_value, status, ip_address, user_agent) VALUES (?, 'email_change', ?, ?, 'pending', ?, ?)");
                if ($history_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$history_stmt->bind_param("sssss", $email, $email, $new_email, $ip, $user_agent)) {
                    throw new Exception("Binding parameters failed: " . $history_stmt->error);
                }
                
                if (!$history_stmt->execute()) {
                    throw new Exception("Execute failed: " . $history_stmt->error);
                }
                
                // Redirect to prevent form resubmission
                $_SESSION['profile_success'] = "Permintaan ganti email berhasil dikirim! Menunggu approval dari admin.";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
                
            } catch (Exception $e) {
                error_log("Profile error: " . $e->getMessage());
                $_SESSION['profile_error'] = "Terjadi kesalahan sistem. Silakan coba lagi nanti.";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            }
        }
    }
    
    if (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $_SESSION['profile_error'] = "Semua field password harus diisi!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } elseif ($new_password !== $confirm_password) {
            $_SESSION['profile_error'] = "Konfirmasi password tidak cocok!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } elseif (strlen($new_password) < 6) {
            $_SESSION['profile_error'] = "Password baru minimal 6 karakter!";
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            try {
                // Verify current password
                $verify_stmt = $conn->prepare("SELECT password FROM users WHERE email = ?");
                if ($verify_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$verify_stmt->bind_param("s", $email)) {
                    throw new Exception("Binding parameters failed: " . $verify_stmt->error);
                }
                
                if (!$verify_stmt->execute()) {
                    throw new Exception("Execute failed: " . $verify_stmt->error);
                }
                
                $verify_result = $verify_stmt->get_result();
                $user_data = $verify_result->fetch_assoc();
                
                if (!password_verify($current_password, $user_data['password'])) {
                    $_SESSION['profile_error'] = "Password lama salah!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                }
                
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $update_stmt = $conn->prepare("UPDATE users SET password = ? WHERE email = ?");
                if ($update_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$update_stmt->bind_param("ss", $hashed_password, $email)) {
                    throw new Exception("Binding parameters failed: " . $update_stmt->error);
                }
                
                if (!$update_stmt->execute()) {
                    throw new Exception("Execute failed: " . $update_stmt->error);
                }
                
                // Add to history
                $ip = getUserIP();
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown Agent';
                $history_stmt = $conn->prepare("INSERT INTO profile_history (user_email, change_type, old_value, new_value, status, ip_address, user_agent) VALUES (?, 'password_change', 'HIDDEN', 'HIDDEN', 'success', ?, ?)");
                if ($history_stmt === false) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }
                
                if (!$history_stmt->bind_param("sss", $email, $ip, $user_agent)) {
                    throw new Exception("Binding parameters failed: " . $history_stmt->error);
                }
                
                if (!$history_stmt->execute()) {
                    throw new Exception("Execute failed: " . $history_stmt->error);
                }
                
                // Redirect to prevent form resubmission
                $_SESSION['profile_success'] = "Password berhasil diubah!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
                
            } catch (Exception $e) {
                error_log("Profile error: " . $e->getMessage());
                $_SESSION['profile_error'] = "Terjadi kesalahan sistem. Silakan coba lagi nanti.";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            }
        }
    }
}

try {
    // Only proceed if database connection is valid
    if (isset($conn) && $conn->ping()) {
        // Get pending email change request
        $pending_request = null;
        $pending_stmt = $conn->prepare("SELECT * FROM email_change_requests WHERE user_email = ? AND status = 'pending' ORDER BY request_date DESC LIMIT 1");
        if ($pending_stmt === false) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        if (!$pending_stmt->bind_param("s", $email)) {
            throw new Exception("Binding parameters failed: " . $pending_stmt->error);
        }
        
        if (!$pending_stmt->execute()) {
            throw new Exception("Execute failed: " . $pending_stmt->error);
        }
        
        $pending_result = $pending_stmt->get_result();
        if ($pending_result === false) {
            throw new Exception("Failed to get result set");
        }
        
        if ($pending_result->num_rows > 0) {
            $pending_request = $pending_result->fetch_assoc();
        }
        
        // Get profile history
        $history_stmt = $conn->prepare("SELECT * FROM profile_history WHERE user_email = ? ORDER BY change_date DESC LIMIT 10");
        if ($history_stmt === false) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        if (!$history_stmt->bind_param("s", $email)) {
            throw new Exception("Binding parameters failed: " . $history_stmt->error);
        }
        
        if (!$history_stmt->execute()) {
            throw new Exception("Execute failed: " . $history_stmt->error);
        }
        
        $history_result = $history_stmt->get_result();
        if ($history_result === false) {
            throw new Exception("Failed to get result set");
        }
    } else {
        throw new Exception("Database connection is not valid");
    }
} catch (Exception $e) {
    error_log("Profile error: " . $e->getMessage());
    $_SESSION['profile_error'] = "Terjadi kesalahan saat memuat data profil. Silakan refresh halaman.";
    // Initialize empty results to prevent undefined variable errors
    $pending_request = null;
    $history_result = null;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Dopminer.com - Profile</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
    
    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background-color: #0a0e27;
            color: #ffffff;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: "Jost", sans-serif;
        }

        .profile-section {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .section-title {
            color: #4fc3f7;
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
        }

        .section-subtitle {
            color: #b0b0b0;
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .profile-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(79, 195, 247, 0.2);
            border-color: #4fc3f7;
        }

        .profile-card h3 {
            color: #4fc3f7;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            color: #e0e0e0;
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4fc3f7;
            box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.25);
            color: white;
        }

        .form-control::placeholder {
            color: #b0b0b0;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(5px);
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .alert-danger {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        .alert-info {
            background: rgba(79, 195, 247, 0.2);
            color: #4fc3f7;
            border: 1px solid #4fc3f7;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            white-space: nowrap;
            min-width: 80px;
            text-align: center;
        }

        .status-pending {
            background: rgba(255, 152, 0, 0.15);
            color: #ff9800;
            border: 1px solid rgba(255, 152, 0, 0.5);
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
        }

        .status-approved {
            background: rgba(76, 175, 80, 0.15);
            color: #4caf50;
            border: 1px solid rgba(76, 175, 80, 0.5);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        .status-rejected {
            background: rgba(244, 67, 54, 0.15);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.5);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2);
        }

        .status-success {
            background: rgba(76, 175, 80, 0.15);
            color: #4caf50;
            border: 1px solid rgba(76, 175, 80, 0.5);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        .status-unknown {
            background: rgba(158, 158, 158, 0.15);
            color: #9e9e9e;
            border: 1px solid rgba(158, 158, 158, 0.5);
            box-shadow: 0 2px 8px rgba(158, 158, 158, 0.2);
        }

        .history-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4fc3f7;
        }

        .history-meta {
            font-size: 0.85rem;
            color: #b0b0b0;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .profile-section {
                padding: 100px 0 1rem 0;
            }
            
            .profile-card {
                margin: 0 1rem 2rem 1rem;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>

    <section class="profile-section">
        <div class="container" data-aos="fade-up">
            <div class="section-title">
                PROFILE SETTING
            </div>
            <div class="section-subtitle">
                Kelola akun dan keamanan profil Anda
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error_message; ?>
                    <?php if (strpos($error_message, "Silakan refresh halaman") !== false): ?>
                        <div class="mt-2">
                            <button onclick="window.location.reload()" class="btn btn-danger btn-sm">
                                <i class="bi bi-arrow-clockwise"></i> Refresh Halaman
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if (!isset($conn) || !$conn->ping()): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-wifi-off"></i> Koneksi database terputus. Silakan coba:
                    <ul class="mb-0 mt-2">
                        <li>Refresh halaman</li>
                        <li>Periksa koneksi internet Anda</li>
                        <li>Coba lagi beberapa saat lagi</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="row">
                    <!-- Email Change Section -->
                    <div class="col-lg-6 mb-4">
                        <div class="profile-card">
                            <h3><i class="bi bi-envelope"></i> Ganti Email</h3>
                            
                            <?php if ($pending_request): ?>
                                <div class="alert alert-info">
                                    <strong>Permintaan Pending:</strong><br>
                                    Dari: <?php echo htmlspecialchars($pending_request['old_email']); ?><br>
                                    Ke: <?php echo htmlspecialchars($pending_request['new_email']); ?><br>
                                    Status: <span class="status-badge status-pending">Menunggu Approval</span><br>
                                    <small>Tanggal: <?php echo date('d M Y H:i', strtotime($pending_request['request_date'])); ?></small>
                                </div>
                            <?php else: ?>
                                <form method="POST">
                                    <div class="form-group">
                                        <label class="form-label">Email Saat Ini</label>
                                        <input type="email" class="form-control" value="<?php echo htmlspecialchars($email); ?>" readonly>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Email Baru *</label>
                                        <input type="email" name="new_email" class="form-control" placeholder="<EMAIL>" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Alasan Ganti Email</label>
                                        <textarea name="reason" class="form-control" rows="3" placeholder="Opsional: Jelaskan alasan Anda ingin mengganti email"></textarea>
                                    </div>
                                    
                                    <button type="submit" name="change_email" class="btn btn-primary">
                                        <i class="bi bi-send"></i> Kirim Permintaan
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Password Change Section -->
                    <div class="col-lg-6 mb-4">
                        <div class="profile-card">
                            <h3><i class="bi bi-shield-lock"></i> Ganti Password</h3>
                            
                            <form method="POST">
                                <div class="form-group">
                                    <label class="form-label">Password Lama *</label>
                                    <input type="password" name="current_password" class="form-control" placeholder="Masukkan password lama" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Password Baru *</label>
                                    <input type="password" name="new_password" class="form-control" placeholder="Minimal 6 karakter" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Konfirmasi Password Baru *</label>
                                    <input type="password" name="confirm_password" class="form-control" placeholder="Ulangi password baru" required>
                                </div>
                                
                                <button type="submit" name="change_password" class="btn btn-primary">
                                    <i class="bi bi-shield-check"></i> Update Password
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- History Section -->
                <div class="profile-card">
                    <h3><i class="bi bi-clock-history"></i> Riwayat Perubahan</h3>
                    
                    <?php if ($history_result->num_rows > 0): ?>
                        <div class="row">
                            <?php while ($history = $history_result->fetch_assoc()): ?>
                                <div class="col-12">
                                    <div class="history-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>
                                                    <?php echo $history['change_type'] === 'email_change' ? 'Perubahan Email' : 'Perubahan Password'; ?>
                                                </strong>
                                                <?php if ($history['change_type'] === 'email_change'): ?>
                                                    <br>
                                                    <small>Dari: <?php echo !empty($history['old_value']) ? htmlspecialchars($history['old_value']) : 'N/A'; ?></small><br>
                                                    <small>Ke: <?php echo !empty($history['new_value']) ? htmlspecialchars($history['new_value']) : 'N/A'; ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <?php 
                                            // Convert status to proper format
                                            $statusClass = '';
                                            $statusText = '';
                                            
                                            // Handle both string and numeric status values
                                            switch($history['status']) {
                                                case 'pending':
                                                case '0':
                                                case 0:
                                                    $statusClass = 'pending';
                                                    $statusText = 'MENUNGGU';
                                                    break;
                                                case 'approved':
                                                case '1':
                                                case 1:
                                                    $statusClass = 'approved';
                                                    $statusText = 'DISETUJUI';
                                                    break;
                                                case 'rejected':
                                                case '2':
                                                case 2:
                                                    $statusClass = 'rejected';
                                                    $statusText = 'DITOLAK';
                                                    break;
                                                case 'success':
                                                case '3':
                                                case 3:
                                                    $statusClass = 'success';
                                                    $statusText = 'BERHASIL';
                                                    break;
                                                default:
                                                    $statusClass = 'unknown';
                                                    $statusText = 'UNKNOWN';
                                            }
                                            ?>
                                            <span class="status-badge status-<?php echo $statusClass; ?>">
                                                <?php echo $statusText; ?>
                                            </span>
                                        </div>
                                        <div class="history-meta">
                                            <i class="bi bi-calendar"></i> <?php echo date('d M Y H:i', strtotime($history['change_date'])); ?>
                                            <i class="bi bi-globe ms-3"></i> <?php echo !empty($history['ip_address']) ? htmlspecialchars($history['ip_address']) : 'Unknown IP'; ?>
                                            <?php if (!empty($history['admin_approved_by'])): ?>
                                                <i class="bi bi-person-check ms-3"></i> Admin: <?php echo htmlspecialchars($history['admin_approved_by']); ?>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php 
                                        // Get admin notes for this history entry
                                        if ($history['change_type'] === 'email_change' && ($history['status'] === 'rejected' || $history['status'] === '2' || $history['status'] == 2)) {
                                            // Get rejection reason from email_change_requests table
                                            $notes_stmt = $conn->prepare("SELECT admin_notes FROM email_change_requests WHERE old_email = ? AND new_email = ? AND admin_notes IS NOT NULL AND admin_notes != '' ORDER BY admin_action_date DESC LIMIT 1");
                                            $notes_stmt->bind_param("ss", $history['old_value'], $history['new_value']);
                                            $notes_stmt->execute();
                                            $notes_result = $notes_stmt->get_result();
                                            if ($notes_result->num_rows > 0) {
                                                $notes_data = $notes_result->fetch_assoc();
                                                if (!empty($notes_data['admin_notes'])) {
                                                    echo '<div class="mt-2" style="background: rgba(244, 67, 54, 0.1); border-left: 3px solid #f44336; padding: 8px 12px; border-radius: 5px;">';
                                                    echo '<small style="color: #f44336;"><i class="bi bi-exclamation-triangle"></i> <strong>Alasan Ditolak:</strong></small><br>';
                                                    echo '<small style="color: #fff;">' . htmlspecialchars($notes_data['admin_notes']) . '</small>';
                                                    echo '</div>';
                                                }
                                            }
                                                                     }
                                         ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox" style="font-size: 3rem; color: #666;"></i>
                            <p class="mt-2" style="color: #999;">Belum ada riwayat perubahan</p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
