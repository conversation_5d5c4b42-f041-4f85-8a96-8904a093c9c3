<?php
/**
 * Security Functions for DopMiner Website
 * Comprehensive security measures to protect against various attacks
 */

/**
 * Generate CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize input to prevent XSS
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    // Remove null bytes
    $input = str_replace(chr(0), '', $input);
    
    // Convert special characters to HTML entities
    $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Remove any remaining script tags
    $input = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $input);
    
    return trim($input);
}

/**
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate password strength
 */
function validatePassword($password) {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
}

/**
 * Rate limiting for login attempts
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
    $key = 'rate_limit_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $data = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $data['first_attempt'] > $timeWindow) {
        $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
        return true;
    }
    
    // Check if limit exceeded
    if ($data['count'] >= $maxAttempts) {
        return false;
    }
    
    // Increment counter
    $_SESSION[$key]['count']++;
    return true;
}

/**
 * Secure password hashing
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'No file uploaded or invalid upload';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    // Check file type
    if (!empty($allowedTypes)) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            $errors[] = 'File type not allowed';
        }
    }
    
    // Check for malicious content
    $content = file_get_contents($file['tmp_name']);
    if (preg_match('/<\?php|<script|javascript:|vbscript:|onload=|onerror=/i', $content)) {
        $errors[] = 'File contains malicious content';
    }
    
    return $errors;
}

/**
 * Sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove path traversal attempts
    $filename = basename($filename);
    
    // Remove dangerous characters
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    
    // Limit length
    $filename = substr($filename, 0, 255);
    
    return $filename;
}

/**
 * Check if request is from allowed IP
 */
function checkAllowedIP($allowedIPs = []) {
    if (empty($allowedIPs)) {
        return true; // No restriction
    }
    
    $clientIP = $_SERVER['REMOTE_ADDR'];
    
    // Check for proxy headers
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $clientIP = trim(explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0]);
    } elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
        $clientIP = $_SERVER['HTTP_X_REAL_IP'];
    }
    
    return in_array($clientIP, $allowedIPs);
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'event' => $event,
        'details' => $details
    ];
    
    $logFile = __DIR__ . '/../logs/security.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Check for suspicious patterns in input
 */
function detectSuspiciousInput($input) {
    $suspiciousPatterns = [
        '/union.*select/i',
        '/drop.*table/i',
        '/insert.*into/i',
        '/delete.*from/i',
        '/update.*set/i',
        '/<script/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onerror=/i',
        '/\.\.\//',
        '/etc\/passwd/i',
        '/proc\/self\/environ/i'
    ];
    
    foreach ($suspiciousPatterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Validate and sanitize database input
 */
function sanitizeDBInput($input, $connection) {
    if (is_array($input)) {
        return array_map(function($item) use ($connection) {
            return sanitizeDBInput($item, $connection);
        }, $input);
    }
    
    // Remove null bytes
    $input = str_replace(chr(0), '', $input);
    
    // Escape for database
    return mysqli_real_escape_string($connection, $input);
}

/**
 * Set secure headers
 */
function setSecurityHeaders() {
    // Prevent XSS
    header('X-XSS-Protection: 1; mode=block');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // Prevent clickjacking
    header('X-Frame-Options: DENY');
    
    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy - More permissive for development
    $csp = "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; ";
    $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: data: blob:; ";
    $csp .= "style-src 'self' 'unsafe-inline' https: http: data: blob:; ";
    $csp .= "font-src 'self' https: http: data: blob:; ";
    $csp .= "img-src 'self' data: blob: https: http:; ";
    $csp .= "connect-src 'self' https: http:; ";
    $csp .= "media-src 'self' data: blob: https: http:; ";
    $csp .= "object-src 'none'; ";
    $csp .= "frame-ancestors 'none';";
    // Disable CSP temporarily for debugging
    // header("Content-Security-Policy: $csp");
    
    // Remove server information
    header_remove('X-Powered-By');
    header_remove('Server');
}

/**
 * Check if user is authenticated and authorized
 */
function checkAuth($requiredRole = null) {
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        return false;
    }
    
    if ($requiredRole && (!isset($_SESSION['role']) || $_SESSION['role'] !== $requiredRole)) {
        return false;
    }
    
    return true;
}

/**
 * Secure session configuration
 */
function configureSecureSession() {
    // Only set session parameters if session hasn't started yet
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 0); // Set to 0 for localhost (HTTP)
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
    }
    
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Initialize security measures only if not already done
if (!defined('SECURITY_INITIALIZED')) {
    define('SECURITY_INITIALIZED', true);
    
    // Configure secure session first
    configureSecureSession();
    
    // Set security headers
    setSecurityHeaders();
    
    // Check for suspicious input in all requests
    foreach ($_REQUEST as $key => $value) {
        if (detectSuspiciousInput($value)) {
            logSecurityEvent('suspicious_input', ['key' => $key, 'value' => $value]);
            http_response_code(403);
            die('Suspicious input detected');
        }
    }
}
?> 