<?php
// Auto-runner untuk QRIS Monitor
// File ini bisa dipanggil via cron job atau browser untuk monitoring otomatis

// Set time limit untuk script yang be<PERSON><PERSON><PERSON> lama
set_time_limit(300); // 5 menit

// Include file monitor
require_once 'qris_monitor.php';

// Konfigurasi interval monitoring (dalam detik)
$monitor_interval = 30; // Cek setiap 30 detik
$max_iterations = 120;  // Maksimal 120 iterasi (1 jam jika interval 30 detik)

// Fungsi untuk continuous monitoring
function continuousMonitor($discord_webhook_url, $cache_file, $interval, $max_iterations) {
    $iteration = 0;
    
    echo "=== QRIS Continuous Monitor Started ===\n";
    echo "Interval: {$interval} seconds\n";
    echo "Max iterations: {$max_iterations}\n";
    echo "Estimated runtime: " . ($interval * $max_iterations / 60) . " minutes\n\n";
    
    while ($iteration < $max_iterations) {
        $iteration++;
        
        echo "\n--- Iteration {$iteration}/{$max_iterations} ---\n";
        
        try {
            monitorQRIS($discord_webhook_url, $cache_file);
        } catch (Exception $e) {
            echo "ERROR: " . $e->getMessage() . "\n";
        }
        
        if ($iteration < $max_iterations) {
            echo "Waiting {$interval} seconds before next check...\n";
            sleep($interval);
        }
        
        // Flush output buffer
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    echo "\n=== Monitoring session completed ===\n";
}

// Cek mode eksekusi
if (php_sapi_name() === 'cli') {
    // Mode command line
    echo "Running in CLI mode\n";
    continuousMonitor($discord_webhook_url, $cache_file, $monitor_interval, $max_iterations);
} else {
    // Mode web browser
    header('Content-Type: text/plain');
    echo "Running in web mode\n";
    
    // Set interval lebih pendek untuk web mode
    $web_interval = 10; // 10 detik
    $web_max_iterations = 30; // 30 iterasi (5 menit)
    
    continuousMonitor($discord_webhook_url, $cache_file, $web_interval, $web_max_iterations);
}

// Jalankan monitoring
monitorQRIS($discord_webhook_url, $cache_file);
?> 