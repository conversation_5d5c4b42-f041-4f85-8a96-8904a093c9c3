<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Set error reporting for production security
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    error_log("User not logged in");
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized - Please login first']);
    exit;
}

// Include database
try {
    include __DIR__ . '/../../database/database.php';
} catch (Exception $e) {
    error_log("Database include error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Database connection failed']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
try {
    $stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user || $user['group'] !== 'ADMIN') {
        error_log("User is not admin: " . ($user['group'] ?? 'NULL'));
        http_response_code(403);
        echo json_encode(['status' => 'error', 'message' => 'Access denied - Admin required']);
        exit;
    }
} catch (Exception $e) {
    error_log("Admin check error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'User validation failed']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Invalid method: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed. Expected POST, got: ' . $_SERVER['REQUEST_METHOD']]);
    exit;
}

// Get and validate JSON input
$rawInput = file_get_contents('php://input');
error_log("Raw input: " . $rawInput);

if (empty($rawInput)) {
    error_log("Empty input received");
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'No data received']);
    exit;
}

$input = json_decode($rawInput, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON decode error: " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid JSON: ' . json_last_error_msg()]);
    exit;
}

error_log("Decoded input: " . json_encode($input));

// Validate required fields
if (!isset($input['id']) || empty($input['id'])) {
    error_log("Missing server ID");
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Server ID is required']);
    exit;
}

// Extract and validate data
$serverId = intval($input['id']);
$ownerEmail = $input['pemilik'] ?? '';
$serverName = $input['nama_server'] ?? '';
$serverType = $input['type_game'] ?? '';
$serverIP = $input['ip_server'] ?? '';
$paket = $input['paket'] ?? '';
$hargaNormal = floatval($input['harga_normal'] ?? 0);
$hargaBulanan = floatval($input['harga_bulanan'] ?? 0);
$expired = $input['expired'] ?? '';
$catatan = $input['catatan'] ?? '';
$token = $input['token'] ?? '';

// Validate required fields - removed validation to allow empty fields
// if (empty($ownerEmail) || empty($serverName) || empty($serverType)) {
//     error_log("Missing required fields");
//     http_response_code(400);
//     echo json_encode(['status' => 'error', 'message' => 'Owner email, server name, and type are required']);
//     exit;
// }

// Log the data to be updated
error_log("Updating server ID $serverId with data: " . json_encode([
    'email' => $ownerEmail,
    'namakota' => $serverName,
    'typehosting' => $serverType,
    'iphosting' => $serverIP,
    'pakethosting' => $paket,
    'harganormal' => $hargaNormal,
    'hargabulanan' => $hargaBulanan,
    'expired' => $expired,
    'catatan' => $catatan,
    'token' => $token
]));

try {
    // First check if server exists
    $checkStmt = $conn->prepare("SELECT id FROM data_client WHERE id = ?");
    $checkStmt->bind_param("i", $serverId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows === 0) {
        error_log("Server not found: ID $serverId");
        http_response_code(404);
        echo json_encode(['status' => 'error', 'message' => 'Server not found']);
        exit;
    }
    
    // Update server in data_client table
    $stmt = $conn->prepare("UPDATE data_client SET 
        email = ?, 
        namakota = ?, 
        typehosting = ?, 
        iphosting = ?, 
        pakethosting = ?,
        harganormal = ?,
        hargabulanan = ?,
        expired = ?,
        catatan = ?,
        token = ?
        WHERE id = ?");
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("sssssddsssi", 
        $ownerEmail, 
        $serverName, 
        $serverType, 
        $serverIP, 
        $paket,
        $hargaNormal,
        $hargaBulanan,
        $expired,
        $catatan,
        $token,
        $serverId
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            error_log("Server updated successfully: ID $serverId");
            echo json_encode(['success' => true, 'message' => 'Server updated successfully']);
        } else {
            error_log("No changes made for server ID: $serverId");
            echo json_encode(['success' => false, 'message' => 'No changes were made']);
        }
    } else {
        error_log("Execute failed: " . $stmt->error);
        throw new Exception("Failed to update server: " . $stmt->error);
    }
    
} catch (Exception $e) {
    error_log("Update server error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>