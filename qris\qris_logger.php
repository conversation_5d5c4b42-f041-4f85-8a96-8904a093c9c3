<?php
// QRIS Logger - Helper untuk menyimpan transaksi ke database
require_once __DIR__ . '/../database/database.php';

class QRISLogger {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
        
        // Create table if not exists
        $this->createTableIfNotExists();
    }
    
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS `log_qris` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `payment_id` varchar(255) NOT NULL UNIQUE,
            `amount` decimal(15,2) NOT NULL,
            `type` enum('CR','DR') NOT NULL COMMENT 'CR=Credit(Masuk), DR=Debit(Keluar)',
            `qris_type` enum('static','dynamic') DEFAULT NULL,
            `brand_name` varchar(100) DEFAULT NULL COMMENT 'Nama bank/provider',
            `buyer_reff` varchar(255) DEFAULT NULL COMMENT 'Referensi pengirim',
            `issuer_reff` varchar(255) DEFAULT NULL COMMENT 'Referensi bank',
            `balance` decimal(15,2) DEFAULT NULL COMMENT 'Saldo akhir setelah transaksi',
            `transaction_date` datetime NOT NULL,
            `webhook_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Status pengiriman webhook ke Discord',
            `raw_data` json DEFAULT NULL COMMENT 'Data mentah dari API QRIS',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_payment_id` (`payment_id`),
            KEY `idx_type` (`type`),
            KEY `idx_brand_name` (`brand_name`),
            KEY `idx_transaction_date` (`transaction_date`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_amount` (`amount`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->conn->query($sql);
    }
    
    /**
     * Log transaksi QRIS ke database
     * @param array $data Data transaksi dari API QRIS
     * @return bool Success status
     */
    public function logTransaction($data) {
        try {
            // Generate payment ID jika tidak ada
            $payment_id = $this->generatePaymentId($data);
            
            // Cek apakah transaksi sudah ada
            if ($this->transactionExists($payment_id)) {
                return false; // Skip duplicate
            }
            
            // Parse data
            $amount = $this->parseAmount($data);
            $type = $this->parseType($data);
            $transaction_date = $this->parseDate($data);
            
            // Insert ke database
            $stmt = $this->conn->prepare("
                INSERT INTO log_qris 
                (payment_id, amount, type, qris_type, brand_name, buyer_reff, issuer_reff, balance, transaction_date, raw_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $qris_type = isset($data['qris']) ? $data['qris'] : null;
            $brand_name = isset($data['brand_name']) ? $data['brand_name'] : null;
            $buyer_reff = isset($data['buyer_reff']) ? $data['buyer_reff'] : null;
            $issuer_reff = isset($data['issuer_reff']) ? $data['issuer_reff'] : null;
            $balance = isset($data['balance']) ? $data['balance'] : null;
            $raw_data = json_encode($data);
            
            $stmt->bind_param(
                "ssssssssss",
                $payment_id,
                $amount,
                $type,
                $qris_type,
                $brand_name,
                $buyer_reff,
                $issuer_reff,
                $balance,
                $transaction_date,
                $raw_data
            );
            
            $result = $stmt->execute();
            
            if ($result) {
                error_log("QRIS Transaction logged: " . $payment_id . " - Rp " . number_format($amount, 0, ',', '.'));
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Error logging QRIS transaction: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update status webhook
     * @param string $payment_id Payment ID
     * @param bool $sent Status pengiriman
     * @return bool Success status
     */
    public function updateWebhookStatus($payment_id, $sent = true) {
        try {
            $stmt = $this->conn->prepare("UPDATE log_qris SET webhook_sent = ? WHERE payment_id = ?");
            $webhook_sent = $sent ? 1 : 0;
            $stmt->bind_param("is", $webhook_sent, $payment_id);
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error updating webhook status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Cek apakah transaksi sudah ada
     * @param string $payment_id Payment ID
     * @return bool
     */
    private function transactionExists($payment_id) {
        $stmt = $this->conn->prepare("SELECT id FROM log_qris WHERE payment_id = ?");
        $stmt->bind_param("s", $payment_id);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->num_rows > 0;
    }
    
    /**
     * Generate payment ID dari data transaksi
     * @param array $data Data transaksi
     * @return string Payment ID
     */
    private function generatePaymentId($data) {
        // Gunakan payment_id yang sudah ada jika tersedia
        if (isset($data['payment_id'])) {
            return $data['payment_id'];
        }
        
        // Generate berdasarkan issuer_reff atau referensi lain
        if (isset($data['issuer_reff'])) {
            return $data['issuer_reff'];
        }
        
        // Generate berdasarkan kombinasi data
        $unique_data = [
            $data['amount'] ?? 0,
            $data['date'] ?? date('Y-m-d H:i:s'),
            $data['buyer_reff'] ?? '',
            $data['brand_name'] ?? ''
        ];
        
        return 'QRIS_' . md5(implode('|', $unique_data));
    }
    
    /**
     * Parse amount dari data
     * @param array $data Data transaksi
     * @return float Amount
     */
    private function parseAmount($data) {
        if (isset($data['amount'])) {
            return (float) $data['amount'];
        }
        
        // Fallback jika amount dalam format string
        if (isset($data['nominal'])) {
            return (float) str_replace([',', '.'], ['', ''], $data['nominal']);
        }
        
        return 0;
    }
    
    /**
     * Parse type dari data (CR/DR)
     * @param array $data Data transaksi
     * @return string Type
     */
    private function parseType($data) {
        if (isset($data['type'])) {
            return strtoupper($data['type']) === 'CR' ? 'CR' : 'DR';
        }
        
        // Default ke CR jika tidak ada type
        return 'CR';
    }
    
    /**
     * Parse tanggal transaksi
     * @param array $data Data transaksi
     * @return string Tanggal dalam format MySQL
     */
    private function parseDate($data) {
        if (isset($data['date'])) {
            $date = DateTime::createFromFormat('d/m/Y H:i:s', $data['date']);
            if ($date) {
                return $date->format('Y-m-d H:i:s');
            }
        }
        
        if (isset($data['timestamp'])) {
            if (is_numeric($data['timestamp'])) {
                return date('Y-m-d H:i:s', $data['timestamp']);
            } else {
                return date('Y-m-d H:i:s', strtotime($data['timestamp']));
            }
        }
        
        // Default ke waktu sekarang
        return date('Y-m-d H:i:s');
    }
    
    /**
     * Get statistik transaksi
     * @param string $date_from Tanggal mulai (Y-m-d)
     * @param string $date_to Tanggal akhir (Y-m-d)
     * @return array Statistik
     */
    public function getStatistics($date_from = null, $date_to = null) {
        $where_clause = '';
        $params = [];
        $param_types = '';
        
        if ($date_from && $date_to) {
            $where_clause = 'WHERE transaction_date BETWEEN ? AND ?';
            $params[] = $date_from . ' 00:00:00';
            $params[] = $date_to . ' 23:59:59';
            $param_types = 'ss';
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN type = 'CR' THEN amount ELSE 0 END) as total_income,
                    SUM(CASE WHEN type = 'DR' THEN amount ELSE 0 END) as total_expense,
                    COUNT(CASE WHEN type = 'CR' THEN 1 END) as income_count,
                    COUNT(CASE WHEN type = 'DR' THEN 1 END) as expense_count
                FROM log_qris $where_clause";
        
        $stmt = $this->conn->prepare($sql);
        
        if (!empty($params)) {
            $stmt->bind_param($param_types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
}

// Helper function untuk mudah digunakan
function logQRISTransaction($data) {
    $logger = new QRISLogger();
    return $logger->logTransaction($data);
}

function updateQRISWebhookStatus($payment_id, $sent = true) {
    $logger = new QRISLogger();
    return $logger->updateWebhookStatus($payment_id, $sent);
}
?> 