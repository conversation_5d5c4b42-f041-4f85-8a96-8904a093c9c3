<?php
// Konfigurasi QRIS Monitor

// =====================================================
// KONFIGURASI DISCORD WEBHOOK
// =====================================================
// Ganti dengan URL webhook Discord Anda
// Cara membuat webhook: Server Settings > Integrations > Webhooks > Create Webhook
$discord_webhook_url = 'https://discordapp.com/api/webhooks/1385302178251411588/xMCaAbnMXx7ciV4EpWuycD6rjr_33olJATq_tnH1fXjzoIzdtrgOQbR6bV7nDdL4hTNn';

// =====================================================
// KONFIGURASI QRIS API
// =====================================================
// URL API QRIS untuk mengecek mutasi pembayaran
$qris_api_url = 'https://gateway.okeconnect.com/api/mutasi/qris/*********/514461517502647711118573OKCTADE9912BFC19E077733D2B818346E8C5';

// =====================================================
// KONFIGURASI CACHE
// =====================================================
// File untuk menyimpan cache pembayaran yang sudah diproses
$cache_file = 'qris_cache.json';

// =====================================================
// KONFIGURASI MONITORING
// =====================================================
// Interval checking dalam detik (untuk continuous monitor)
$check_interval = 30; // 5 detik

// Timeout untuk request API dalam detik
$api_timeout = 30;

// =====================================================
// KONFIGURASI DISCORD MESSAGE
// =====================================================
// Customisasi pesan Discord
$discord_config = array(
    'title' => '💰 Pembayaran QRIS Baru Diterima!',
    'color' => 3447003, // Blue color (decimal)
    'footer_text' => 'QRIS Payment Monitor',
    'emoji' => array(
        'amount' => '💵',
        'reference' => '🔗',
        'time' => '🕐',
        'sender' => '👤',
        'data' => '📄'
    )
);

// =====================================================
// KONFIGURASI WEB MONITOR DURATION
// =====================================================
// Opsi durasi untuk web monitoring (dalam detik)
$duration_options = array(
    '5' => array(
        'runtime' => 300,        // 5 menit = 300 detik
        'name' => '5 Menit'
    ),
    '15' => array(
        'runtime' => 900,        // 15 menit = 900 detik
        'name' => '15 Menit'
    ),
    '30' => array(
        'runtime' => 1800,       // 30 menit = 1800 detik
        'name' => '30 Menit'
    ),
    '60' => array(
        'runtime' => 3600,       // 1 jam = 3600 detik
        'name' => '1 Jam'
    ),
    '120' => array(
        'runtime' => 7200,       // 2 jam = 7200 detik
        'name' => '2 Jam'
    ),
    'unlimited' => array(
        'runtime' => 0,          // 0 = unlimited
        'name' => 'Unlimited (Hati-hati!)'
    )
);

// Default durasi untuk web monitor
$default_web_duration = 'unlimited'; // Key dari $duration_options

// =====================================================
// VALIDASI KONFIGURASI
// =====================================================
function validateConfig() {
    global $discord_webhook_url;
    
    $errors = array();
    
    if ($discord_webhook_url === 'YOUR_DISCORD_WEBHOOK_URL_HERE') {
        $errors[] = 'Discord webhook URL belum dikonfigurasi';
    }
    
    if (!function_exists('curl_init')) {
        $errors[] = 'PHP cURL extension tidak tersedia';
    }
    
    return $errors;
}

// Cek konfigurasi jika file dijalankan langsung
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "=== QRIS Monitor Configuration Check ===\n";
    
    $errors = validateConfig();
    
    if (empty($errors)) {
        echo "✅ Konfigurasi valid!\n";
        
        echo "\nKonfigurasi saat ini:\n";
        echo "- Discord Webhook: " . (strlen($discord_webhook_url) > 50 ? 'Configured' : 'Not configured') . "\n";
        echo "- QRIS API: " . $qris_api_url . "\n";
        echo "- Cache File: " . $cache_file . "\n";
        echo "- Check Interval: " . $check_interval . " seconds\n";
        echo "- API Timeout: " . $api_timeout . " seconds\n";
        
    } else {
        echo "❌ Error dalam konfigurasi:\n";
        foreach ($errors as $error) {
            echo "   - " . $error . "\n";
        }
        echo "\nSilakan perbaiki konfigurasi di file config_qris.php\n";
    }
}
?>