<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

$email = $_SESSION['email'];

// Ambil data order user
$orders = [];
try {
    $query = "SELECT o.*, p.nama as product_name FROM `order` o JOIN produk p ON o.id_produk = p.id_produk WHERE o.email = ? ORDER BY o.created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }
} catch (Exception $e) {
    error_log("Order history error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Order History - Dopminer</title>
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
        }
        .dashboard-container {
            padding: 220px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        .section-title {
            color: #4fc3f7;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 2rem;
        }
        .order-cards-list {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .welcome-card {
            background: rgba(255,255,255,0.07);
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.13);
            padding: 2rem 1.5rem 1.5rem 1.5rem;
            display: flex;
            flex-direction: column;
            transition: box-shadow 0.2s, transform 0.2s;
            border: 1.5px solid rgba(79,195,247,0.08);
            position: relative;
            backdrop-filter: blur(10px);
        }
        .welcome-card:hover {
            box-shadow: 0 12px 36px rgba(79,195,247,0.18);
            transform: translateY(-2px) scale(1.01);
            border-color: #4fc3f7;
        }
        .order-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.7rem;
        }
        .order-card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4fc3f7;
            margin-bottom: 0.2rem;
        }
        .order-card-id {
            font-size: 0.98rem;
            color: #b0b0b0;
            font-weight: 500;
        }
        .badge-status {
            font-weight: 600;
            border-radius: 20px;
            padding: 0.35rem 1.1rem;
            font-size: 0.95rem;
            display: inline-block;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        }
        .badge-pending { background: #ffc10733; color: #ffc107; border: 1px solid #ffc10755; }
        .badge-paid { background: #28a74533; color: #28a745; border: 1px solid #28a74555; }
        .badge-failed { background: #dc354533; color: #dc3545; border: 1px solid #dc354555; }
        .badge-verif { background: #17a2b833; color: #17a2b8; border: 1px solid #17a2b855; }
        .order-card-body {
            display: flex;
            flex-wrap: wrap;
            gap: 2.5rem 2rem;
            margin-bottom: 0.5rem;
        }
        .order-info-block {
            min-width: 180px;
            margin-bottom: 0.5rem;
        }
        .order-info-label {
            color: #b0b0b0;
            font-size: 0.93rem;
            margin-bottom: 0.1rem;
            display: block;
        }
        .order-info-value {
            color: #fff;
            font-size: 1.08rem;
            font-weight: 500;
        }
        @media (max-width: 900px) {
            .order-card-body { flex-direction: column; gap: 0.7rem; }
        }
        @media (max-width: 768px) {
            .dashboard-container { padding: 100px 0 1rem 0; }
            .section-title { font-size: 1.2rem; }
            .welcome-card { padding: 1.2rem 0.7rem; }
        }
    </style>
</head>
<body>
<?php include '../components/navbar.php'; ?>
<div class="dashboard-container">
    <div class="container">
        <div class="section-title">
            <i class="fas fa-history"></i> Riwayat Order
        </div>
        <div class="order-cards-list">
            <?php if (empty($orders)): ?>
                <div class="text-center text-light">Belum ada order.</div>
            <?php else: ?>
                <?php 
                    $has_pending_payment = false;
                    foreach ($orders as $order) {
                        if ($order['status_bayar'] === 'Menunggu Pembayaran') {
                            $has_pending_payment = true;
                            break;
                        }
                    }
                ?>
                <?php if ($has_pending_payment): ?>
                    <div class="alert alert-info" style="background: rgba(23, 162, 184, 0.1); border: 1px solid rgba(23, 162, 184, 0.3); color: #17a2b8; margin-bottom: 20px; border-radius: 8px; padding: 15px;">
                        <i class="fas fa-info-circle"></i> <strong>Info:</strong> Anda memiliki order yang belum diselesaikan pembayarannya. Klik tombol "Lanjutkan Pembayaran" untuk melanjutkan proses pembayaran.
                    </div>
                <?php endif; ?>
                <?php foreach ($orders as $order): ?>
                    <?php
                        $status = strtolower($order['status_bayar']);
                        $status_class = 'badge-pending';
                        if ($status === 'sudah dibayar') $status_class = 'badge-paid';
                        else if ($status === 'menunggu verifikasi') $status_class = 'badge-verif';
                        else if ($status === 'gagal') $status_class = 'badge-failed';
                    ?>
                    <div class="welcome-card aos-init aos-animate order-card">
                        <div class="order-card-header">
                            <div class="order-card-title"><?php echo htmlspecialchars($order['product_name']); ?></div>
                            <span class="badge-status <?php echo $status_class; ?>"><?php echo htmlspecialchars($order['status_bayar']); ?></span>
                        </div>
                        <div class="order-card-body">
                            <div class="order-info-block">
                                <span class="order-info-label"><i class="fas fa-server"></i> Nama Server</span>
                                <span class="order-info-value"><?php echo htmlspecialchars($order['nama_server']); ?></span>
                            </div>
                            <?php if (!empty($order['paket'])): ?>
                                <div class="order-info-block">
                                    <span class="order-info-label"><i class="fas fa-users"></i> Paket</span>
                                    <span class="order-info-value">
                                        <span style="background: #4fc3f7; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px;">
                                            <?php echo htmlspecialchars($order['paket']); ?>
                                        </span>
                                    </span>
                                </div>
                            <?php endif; ?>
                            <div class="order-info-block">
                                <span class="order-info-label"><i class="fas fa-calendar-alt"></i> Tanggal Order</span>
                                <span class="order-info-value"><?php echo date('d-m-Y H:i', strtotime($order['created_at'])); ?></span>
                            </div>
                            <div class="order-info-block">
                                <span class="order-info-label"><i class="fas fa-money-bill-wave"></i> Jumlah Bayar</span>
                                <span class="order-info-value">Rp <?php echo number_format($order['jumlah_bayar'], 0, ',', '.'); ?></span>
                            </div>
                            <div class="order-info-block">
                                <span class="order-info-label"><i class="fas fa-wallet"></i> Metode</span>
                                <span class="order-info-value"><?php echo htmlspecialchars($order['pembayaran']); ?></span>
                            </div>
                            <?php if ($order['status_bayar'] === 'Menunggu Pembayaran'): ?>
                                <div class="order-info-block" style="margin-top: 15px;">
                                    <a href="continue_payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-primary btn-sm" style="width: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 8px 16px; border-radius: 8px; text-decoration: none; color: white; font-weight: 500;">
                                        <i class="fas fa-credit-card"></i> Lanjutkan Pembayaran
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php include '../components/footer.php'; ?>
<!-- Scripts -->
<script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Navbar Scroll Effect -->
<style>
#header.scrolled {
    background: #121212e8 !important;
    transition: background 0.3s ease;
}
</style>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const header = document.getElementById('header');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
});
</script>
</body>
</html>