<?php
/**
 * Discord Webhook Logger
 * Mengirim log aktivitas login dan register ke Discord
 */

class DiscordWebhook {
    private $webhookUrl;
    
    public function __construct($webhookUrl) {
        $this->webhookUrl = $webhookUrl;
    }
    
    /**
     * Mengirim log login ke Discord
     */
    public function sendLoginLog($email, $success = true, $ip = null) {
        $ip = $ip ?: $_SERVER['REMOTE_ADDR'];
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        if ($success) {
            $embed = [
                'title' => '✅ Login Berhasil',
                'description' => 'Pengguna berhasil login ke sistem',
                'color' => 3447003, // Biru
                'fields' => [
                    [
                        'name' => '📧 Email',
                        'value' => $email,
                        'inline' => true
                    ],
                    [
                        'name' => '⏰ Waktu',
                        'value' => $timestamp,
                        'inline' => true
                    ],
                    [
                        'name' => '🖥️ User Agent',
                        'value' => substr($userAgent, 0, 100) . (strlen($userAgent) > 100 ? '...' : ''),
                        'inline' => false
                    ]
                ],
                'footer' => [
                    'text' => 'DopMiner Security Log',
                    'icon_url' => 'https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png'
                ],
                'timestamp' => date('c')
            ];
        } else {
            $embed = [
                'title' => '❌ Login Gagal',
                'description' => 'Percobaan login yang gagal terdeteksi',
                'color' => 3447003, // Biru
                'fields' => [
                    [
                        'name' => '📧 Email',
                        'value' => $email,
                        'inline' => true
                    ],
                    [
                        'name' => '⏰ Waktu',
                        'value' => $timestamp,
                        'inline' => true
                    ],
                    [
                        'name' => '🖥️ User Agent',
                        'value' => substr($userAgent, 0, 100) . (strlen($userAgent) > 100 ? '...' : ''),
                        'inline' => false
                    ]
                ],
                'footer' => [
                    'text' => 'DopMiner Security Log',
                    'icon_url' => 'https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png'
                ],
                'timestamp' => date('c')
            ];
        }
        
        $this->sendWebhook($embed);
    }
    
    /**
     * Mengirim log register ke Discord
     */
    public function sendRegisterLog($email, $success = true, $ip = null) {
        $ip = $ip ?: $_SERVER['REMOTE_ADDR'];
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        if ($success) {
            $embed = [
                'title' => '🎉 Registrasi Berhasil',
                'description' => 'Pengguna baru berhasil mendaftar',
                'color' => 3447003, // Biru
                'fields' => [
                    [
                        'name' => '📧 Email',
                        'value' => $email,
                        'inline' => true
                    ],
                    [
                        'name' => '⏰ Waktu',
                        'value' => $timestamp,
                        'inline' => true
                    ],
                    [
                        'name' => '🖥️ User Agent',
                        'value' => substr($userAgent, 0, 100) . (strlen($userAgent) > 100 ? '...' : ''),
                        'inline' => false
                    ]
                ],
                'footer' => [
                    'text' => 'DopMiner Security Log',
                    'icon_url' => 'https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png'
                ],
                'timestamp' => date('c')
            ];
        } else {
            $embed = [
                'title' => '⚠️ Registrasi Gagal',
                'description' => 'Percobaan registrasi yang gagal',
                'color' => 3447003, // Biru
                'fields' => [
                    [
                        'name' => '📧 Email',
                        'value' => $email,
                        'inline' => true
                    ],
                    [
                        'name' => '⏰ Waktu',
                        'value' => $timestamp,
                        'inline' => true
                    ],
                    [
                        'name' => '🖥️ User Agent',
                        'value' => substr($userAgent, 0, 100) . (strlen($userAgent) > 100 ? '...' : ''),
                        'inline' => false
                    ]
                ],
                'footer' => [
                    'text' => 'Dopminer Security Log',
                    'icon_url' => 'https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png'
                ],
                'timestamp' => date('c')
            ];
        }
        
        $this->sendWebhook($embed);
    }
    
    /**
     * Mengirim webhook ke Discord
     */
    private function sendWebhook($embed) {
        $data = [
            'embeds' => [$embed]
        ];
        
        $options = [
            'http' => [
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data)
            ]
        ];
        
        $context = stream_context_create($options);
        
        // Kirim webhook secara asinkron agar tidak mengganggu response
        try {
            @file_get_contents($this->webhookUrl, false, $context);
        } catch (Exception $e) {
            // Log error tapi jangan ganggu proses utama
            error_log('Discord webhook error: ' . $e->getMessage());
        }
    }
}
?>