<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
require_once '../includes/config.php';

// Load uptime data from JSON file
function loadUptimeData() {
    $json_file = '../assets/uptime.json';
    if (file_exists($json_file)) {
        $json_data = file_get_contents($json_file);
        $data = json_decode($json_data, true);
        if ($data) {
            return $data;
        }
    }
    
    // Return default data if file doesn't exist or is invalid
    return [
        'last_updated' => 'Never',
        'servers' => [],
        'overall_stats' => [
            'total' => 0,
            'online' => 0,
            'offline' => 0,
            'warning' => 0,
            'avg_uptime' => 0
        ]
    ];
}

// Load data from JSON
$uptime_data = loadUptimeData();
$servers = $uptime_data['servers'];
$overall_stats = $uptime_data['overall_stats'];
$last_updated = $uptime_data['last_updated'];

// Helper functions for backward compatibility
function checkServerStatus($ip, $port) {
    global $servers;
    foreach ($servers as $server) {
        if ($server['ip'] === $ip && $server['port'] == $port) {
            return $server['status'];
        }
    }
    return 'offline';
}

function getUptimePercentage($ip) {
    global $servers;
    foreach ($servers as $server) {
        if ($server['ip'] === $ip) {
            return $server['uptime'];
        }
    }
    return '0.00';
}

function getResponseTime($ip) {
    global $servers;
    foreach ($servers as $server) {
        if ($server['ip'] === $ip) {
            return $server['response_time'];
        }
    }
    return 0;
}

function calculateOverallStats($servers) {
    global $overall_stats;
    return $overall_stats;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Server Status - Dopminer</title>
    <meta content="Dopminer Server Status and Uptime Monitor" name="description">
    <meta content="server status, uptime, monitoring, hosting" name="keywords">

    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    <link href="https://dopminer.com/Gambar/ezgif-6f3505eaac0f6a.png" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom Uptime CSS -->
    <!-- External CSS -->
    <link rel="stylesheet" href="../assets/css/uptime.css">
</head>

<body>
    <?php include '../components/navbar.php'; ?>

    <section class="uptime-section">
        <div class="container" data-aos="fade-up">
            <div class="section-title">
                <h2>Server Status & Uptime Monitor</h2>
            </div>
            
            <!-- Loading Indicator -->
            <div id="loading-indicator" class="text-center mb-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Checking server status...</p>
            </div>

            <div class="server-content">
                <?php 
                // Use loaded statistics from JSON
                $stats = $overall_stats;
                ?>
                
                <!-- Overall Statistics -->
                <div class="overall-stats" data-aos="fade-up" data-aos-delay="100">
                    <h3>Overall System Status</h3>
                    <p style="text-align: center; color: #b0b0b0; margin-bottom: 20px;">
                        Last Updated: <?php echo $last_updated !== 'Never' ? date('d M Y, H:i:s', strtotime($last_updated)) : 'Never'; ?>
                    </p>
                    <div class="server-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="total-servers"><?php echo $stats['total']; ?></div>
                            <div class="stat-label">Total Servers</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="online-servers"><?php echo $stats['online']; ?></div>
                            <div class="stat-label">Online</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="offline-servers"><?php echo $stats['offline']; ?></div>
                            <div class="stat-label">Offline</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="warning-servers"><?php echo $stats['warning']; ?></div>
                            <div class="stat-label">Warning</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avg-uptime"><?php echo $stats['avg_uptime']; ?>%</div>
                            <div class="stat-label">Avg Uptime</div>
                        </div>
                    </div>
                </div>

            <!-- Filter Buttons -->
            <div class="filter-buttons" data-aos="fade-up" data-aos-delay="200">
                <button class="filter-btn active" data-filter="all">All Servers</button>
                <button class="filter-btn" data-filter="online">Online</button>
                <button class="filter-btn" data-filter="offline">Offline</button>
                <button class="filter-btn" data-filter="warning">Warning</button>
            </div>

            <!-- Server Cards -->
            <div class="server-grid" id="server-container">
                <?php if (empty($servers)): ?>
                    <div class="col-12">
                        <div class="no-hosting">
                            <div class="icon-container">
                                <i class="fas fa-server"></i>
                            </div>
                            <h3>No Server Data Available</h3>
                            <p>Server monitoring data is not available yet. Please run the uptime refresh script to collect server status information.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach($servers as $index => $server): ?>
                        <div class="" data-status="<?php echo $server['status']; ?>" data-aos="fade-up" data-aos-delay="<?php echo 300 + ($index * 100); ?>">
                            <div class="server-card-horizontal">
                                <div class="server-type"><?php echo htmlspecialchars($server['type']); ?></div>
                                
                                <h3><?php echo htmlspecialchars($server['name']); ?></h3>
                                
                                <div class="server-info">
                                    <p><i class="fas fa-map-marker-alt"></i> Location: <?php echo htmlspecialchars($server['location']); ?></p>
                                    <p><i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($server['description']); ?></p>
                                </div>
                                
                                <div class="status status-<?php echo $server['status']; ?>">
                                    <?php 
                                    switch($server['status']) {
                                        case 'online': echo 'ONLINE'; break;
                                        case 'offline': echo 'OFFLINE'; break;
                                        case 'warning': echo 'WARNING'; break;
                                    }
                                    ?>
                                </div>
                                
                                <div class="server-stats">
                                    <div class="stat-row">
                                        <div class="stat-item">
                                            <div class="stat-value"><?php echo $server['uptime']; ?>%</div>
                                            <div class="stat-label">Uptime</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value"><?php echo $server['response_time']; ?>ms</div>
                                            <div class="stat-label">Response</div>
                                        </div>
                                    </div>
                                    <div class="last-check">
                                        <small>Last Check: <?php echo isset($server['last_check']) ? date('H:i', strtotime($server['last_check'])) : 'N/A'; ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            </div>
        </div>
    </section>

    <!-- Refresh Button -->
    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

    <!-- External JavaScript -->
    <script src="../assets/js/uptime.js"></script>
</body>

</html>