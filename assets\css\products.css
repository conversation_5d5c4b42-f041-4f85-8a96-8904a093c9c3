/* Product Page Styles */
/* Existing styles from products.php */
.swal2-popup {
    animation: fadeIn 0.5s ease-in-out;
}

.swal2-backdrop-show {
    animation: fadeIn 0.4s ease-in-out;
}

/* Sweetalert Custom Styling */
.swal2-popup {
    border-radius: 25px !important;
    background: rgba(9, 16, 27, 0.55) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 2em !important;
}

.swal2-title {
    color: #fff !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-bottom: 0.5em !important;
}

.swal2-html-container {
    color: #ccc !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    margin-top: 0 !important;
}

.swal2-icon {
    border-color: rgba(255, 255, 255, 0.3) !important;
    margin: 1.5em auto !important;
}

.swal2-confirm {
    border-radius: 50px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 3px 16px rgba(211, 51, 51, 0.3) !important;
}

.swal2-cancel {
    border-radius: 50px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.swal2-cancel:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.swal2-actions {
    margin-top: 2em !important;
}

/* Animation */
.swal2-show {
    animation: swal2-show 0.3s ease-out !important;
}

.swal2-hide {
    animation: swal2-hide 0.3s ease-in !important;
}

@keyframes swal2-show {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes swal2-hide {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

.swal2-hide {
    animation: fadeOut 0.5s ease-in-out !important;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

.animate__customFadeIn {
    animation: modalFadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate__customFadeOut {
    animation: modalFadeOut 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate__slower {
    animation-duration: 0.8s !important;
}

.animate__slow {
    animation-duration: 0.6s !important;
}

.animate__medium {
    animation-duration: 0.5s !important;
}

/* Product Specification Styling */
.product-description {
    margin: 15px 0;
}

.spec-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    color: #fff;
    font-size: 14px;
}

.spec-item i {
    width: 20px;
    margin-right: 10px;
    color: #47b2e4;
}

.swal2-html-container {
    margin: 0 !important;
    padding: 15px 0 !important;
    opacity: 1;
    transition: opacity 0.5s ease;
}

/* Modal Specification Styling */
.modal-specs-container {
    background: rgba(71, 178, 228, 0.05);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(71, 178, 228, 0.1);
}

.modal-spec-item {
    display: flex;
    align-items: center;
    margin: 10px 0;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal-spec-item:hover {
    transform: translateX(5px);
}

.modal-spec-item i {
    width: 24px;
    margin-right: 12px;
    color: #47b2e4;
    font-size: 16px;
}

/* Paket Selection Styling - Grid Layout */
.paket-selection {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin: 20px 0;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

.paket-btn {
    background: rgba(71, 178, 228, 0.15);
    border: 2px solid rgba(71, 178, 228, 0.3);
    color: #47b2e4;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    min-height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.paket-btn:hover {
    background: rgba(71, 178, 228, 0.25);
    border-color: #47b2e4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(71, 178, 228, 0.2);
}

.paket-btn.active {
    background: linear-gradient(135deg, #47b2e4, #369bd4);
    border-color: #47b2e4;
    color: #fff;
    box-shadow: 0 5px 20px rgba(71, 178, 228, 0.4);
}

.paket-btn.paket-disabled {
    background: #ff4c4c !important;
    border-color: #ff4c4c !important;
    color: #fff !important;
    cursor: not-allowed !important;
    opacity: 0.7 !important;
    pointer-events: none;
}

.paket-btn.paket-disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Responsive Design for Paket Selection */
@media (max-width: 768px) {
    .paket-selection {
        max-width: 100%;
        gap: 10px;
        margin: 15px 0;
    }
    
    .paket-btn {
        padding: 10px 15px;
        font-size: 13px;
        min-height: 40px;
    }
}

@media (max-width: 480px) {
    .paket-selection {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }
    
    .paket-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 38px;
    }
}

/* Modal Order Button Styling */
.modal-order-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 35px;
    background: #25D366;
    color: #fff;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid #25D366;
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
}

.modal-order-button:hover {
    background: #128C7E;
    color: #fff;
    border-color: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.4);
}

.modal-order-button i {
    margin-right: 8px;
    font-size: 20px;
}

/* Category Filter Styling */
.category-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.category-btn {
    padding: 8px 20px;
    border: 2px solid #47b2e4;
    background: transparent;
    color: #47b2e4;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-btn:hover {
    background: rgba(71, 178, 228, 0.1);
    transform: translateY(-2px);
}

.category-btn.active {
    background: #47b2e4;
    color: #fff;
}

.product-item {
    transition: all 0.4s ease-in-out;
}

.product-item.hide {
    display: none;
}

/* Product Image Slideshow */
.product-image-slideshow {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.product-slide.active {
    opacity: 1;
}

/* Modal Image Slider */
.product-modal-slider {
    position: relative;
    width: 100%;
    height: 250px;  /* Ukuran tetap */
    overflow: hidden;
    border-radius: 10px;
}

.slider-nav {
    position: absolute;
    top: 40%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
}

.slider-nav:hover {
    background: rgba(0, 0, 0, 0.8);
}

.slider-prev {
    left: 10px;
}

.slider-next {
    right: 10px;
}

.modal-image {
    width: 100%;
    height: 250px;  /* Ukuran tetap */
    object-fit: cover;  /* Memastikan gambar terlihat penuh tanpa merubah aspek rasio */
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.modal-image:hover {
    transform: scale(1.02);
}

/* Product Card Styling */
.product-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Image Container Styles */
.product-image-container {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 10px;
    margin: 15px 0;
    position: relative;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-image:hover {
    transform: scale(1.05);
}

/* Slideshow Styles */
.product-image-slideshow {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 10px;
    margin: 15px 0;
    position: relative;
}

.product-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.product-slide.active {
    opacity: 1;
}

/* Product Type Badge */
.product-type {
    position: absolute;
    top: 250px;
    right: 150px;
    background: rgba(71, 178, 228, 0.1);
    color: #47b2e4;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 1;
}

/* Product Title */
.product-title {
    color: #fff;
    font-size: 20px;
    margin: 15px 0;
    font-weight: 600;
}

/* Product Description */
.product-description {
    color: #aaa;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Menghilangkan kotak pada tombol close modal */
.swal2-close {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.swal2-close:hover {
    opacity: 1;
    background: none !important;
}