<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Get order_id from URL parameter
$order_id = $_GET['order_id'] ?? null;
if (!$order_id) {
    $_SESSION['error'] = 'Order ID tidak valid.';
    header('Location: order_history');
    exit;
}

$email = $_SESSION['email'];

// Fetch order details to set session variables
try {
    $query = "SELECT o.*, p.nama as product_name FROM `order` o 
              JOIN produk p ON o.id_produk = p.id_produk 
              WHERE o.id = ? AND o.email = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("is", $order_id, $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $_SESSION['error'] = 'Order tidak ditemukan.';
        header('Location: order_history');
        exit;
    }
    
    $order = $result->fetch_assoc();
    
    // Check if order can be continued (only for 'Menunggu Pembayaran' status)
    if ($order['status_bayar'] !== 'Menunggu Pembayaran') {
        $_SESSION['error'] = 'Order ini tidak dapat dilanjutkan pembayarannya.';
        header('Location: order_history');
        exit;
    }
    
    // Set session variables for payment monitor
    $_SESSION['order_id'] = $order['id'];
    $_SESSION['product_id'] = $order['id_produk'];
    $_SESSION['server_name'] = $order['nama_server'];
    $_SESSION['unique_amount'] = $order['jumlah_bayar'];
    $_SESSION['selected_paket'] = $order['paket'];
    
    // Redirect to payment monitor
    header('Location: payment_monitor.php?order_id=' . $order['id']);
    exit;
    
} catch (Exception $e) {
    error_log("Continue payment error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat memuat data pesanan.";
    header('Location: order_history');
    exit;
}
?>