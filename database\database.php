<?php 
// Database configuration
$is_local = false; // Set this to true for local development

if ($is_local) {
    // Local database configuration
    $hostname = "localhost";  // or 127.0.0.1
    $username = "root";      // default XAMPP/Laragon username
    $password = "";         // default XAMPP/Laragon password
    $dbname   = "database_bot";
} else {
    // Production database configuration
    $hostname = $_ENV['DB_HOST'] ?? "database.dopminer.cloud";
    $username = $_ENV['DB_USER'] ?? "website";
    $password = $_ENV['DB_PASS'] ?? "Dopminer!@007";
    $dbname   = $_ENV['DB_NAME'] ?? "database_bot";
}

// Connection options
$driver = new mysqli_driver();
$driver->report_mode = MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT;

// Create connection with error handling
try {
    // Create connection with retry mechanism
    $max_retries = 3;
    $retry_count = 0;
    $connected = false;
    
    while (!$connected && $retry_count < $max_retries) {
        try {
            $conn = mysqli_init();
            
            // Set important options
            $conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 3);
            $conn->options(MYSQLI_OPT_READ_TIMEOUT, 3);
            $conn->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
            
            // Connect to database
            $conn->real_connect($hostname, $username, $password, $dbname);
            
            // Set charset and collation
            $conn->set_charset("utf8mb4");
            $conn->query("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Set session variables for better performance
            $conn->query("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");
            $conn->query("SET SESSION wait_timeout = 28800"); // 8 hours
            $conn->query("SET SESSION interactive_timeout = 28800"); // 8 hours
            
            $connected = true;
            
        } catch (Exception $e) {
            $retry_count++;
            if ($retry_count >= $max_retries) {
                throw $e;
            }
            usleep(100000); // Wait 100ms before retrying
        }
    }
    
    // Final connection check
    if ($conn->connect_error) {
        throw new Exception($conn->connect_error);
    }
    
} catch (Exception $e) {
    // Log error securely
    error_log("Database connection error: " . $e->getMessage());
    
    // For API calls, don't output HTML error messages
    if (isset($_SERVER['HTTP_CONTENT_TYPE']) && $_SERVER['HTTP_CONTENT_TYPE'] === 'application/json') {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Database connection failed']);
        exit;
    }
    
    // Show user-friendly message for regular pages
    if ($is_local) {
        die("Database connection failed. Please check if your local MySQL server is running and the credentials are correct.");
    } else {
        die("Database connection failed. Please try again later or contact administrator.");
    }
}