# CORS Headers for Font Files
<IfModule mod_headers.c>
    # Allow CORS for font files
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Accept, Origin, X-Requested-With"
    Header always set Access-Control-Max-Age "3600"
    
    # Set correct MIME types for font files
    <FilesMatch "\.(woff|woff2|ttf|eot|svg)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
</IfModule>

# MIME Types for Font Files
<IfModule mod_mime.c>
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType image/svg+xml .svg
</IfModule>

# Handle OPTIONS requests
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule> 