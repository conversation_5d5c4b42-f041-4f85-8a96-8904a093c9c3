<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set proper headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Set error reporting for production security
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Include database with error handling
    include __DIR__ . '/../database/database.php';
    
    // Include Discord webhook
    include __DIR__ . '/../includes/discord_webhook.php';
    
    // Initialize Discord webhook
    $discordWebhook = new DiscordWebhook('https://discordapp.com/api/webhooks/1390490529904591000/uOiNXwHt6lL66vS-Gx3wSC9jcg2yN3aH0PPJWRa0ZJtF8MSdYBeydMXg41E9_-6Iw9pd');
    
    // Check if database connection exists
    if (!isset($conn) || $conn->connect_error) {
        echo json_encode(['error' => 'Database connection failed. Please try again.']);
        exit;
    }

    // Validate input
    if (!isset($_POST['email'], $_POST['password'], $_POST['captcha'])) {
        echo json_encode(['error' => 'All fields are required!']);
        exit;
    }

    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $captcha = $_POST['captcha'];

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['error' => 'Invalid email format!']);
        exit;
    }

    // Check if CAPTCHA session exists
    if (!isset($_SESSION['captcha'])) {
        echo json_encode(['error' => 'CAPTCHA session expired. Please refresh the page.']);
        exit;
    }

    // Check CAPTCHA
    if ($captcha != $_SESSION['captcha']) {
        echo json_encode(['error' => 'CAPTCHA salah!']);
        exit;
    }

    // Verify email and password from database
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    if (!$stmt) {
        echo json_encode(['error' => 'Database error. Please try again.']);
        exit;
    }

    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if (password_verify($password, $user['password'])) {
            // Regenerate session ID for security
            session_regenerate_id(true);
            
            // Set session variables
            $_SESSION['loggedin'] = true;
            $_SESSION['email'] = $email;
            $_SESSION['group'] = $user['group'];
            
            // Send successful login log to Discord
            $discordWebhook->sendLoginLog($email, true);
            
            if ($user['group'] === 'ADMIN') {
                echo json_encode(['success' => true, 'message' => 'Login berhasil!', 'redirect' => '../admin/admin.php']);
            } else {
                echo json_encode(['success' => true, 'message' => 'Login berhasil!', 'redirect' => '../pages/dashboard']);
            }
            exit;
        } else {
            // Send failed login log to Discord
            $discordWebhook->sendLoginLog($email, false);
            echo json_encode(['error' => 'Email atau kata sandi salah!']);
            exit;
        }
    } else {
        // Send failed login log to Discord
        $discordWebhook->sendLoginLog($email, false);
        echo json_encode(['error' => 'Email atau kata sandi salah!']);
        exit;
    }

} catch (Exception $e) {
    // Log error for debugging
    error_log('Login error: ' . $e->getMessage());
    echo json_encode(['error' => 'An error occurred. Please try again.']);
    exit;
}
?>