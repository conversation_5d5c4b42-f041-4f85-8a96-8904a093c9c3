<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input']);
    exit;
}

// Validate required fields
if (!isset($input['order_id']) || empty($input['order_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Order ID is required']);
    exit;
}

$orderId = $input['order_id'];
$email = $input['email'] ?? '';
$namaServer = $input['nama_server'] ?? '';
$paket = $input['paket'] ?? '';
$statusBayar = $input['status_bayar'] ?? '';
$pembayaran = $input['pembayaran'] ?? '';
$jumlahBayar = $input['jumlah_bayar'] ?? 0;

try {
    // Check if order exists
    $checkQuery = "SELECT id FROM `order` WHERE id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    if (!$checkStmt) {
        throw new Exception('Prepare statement error: ' . $conn->error);
    }
    
    $checkStmt->bind_param("i", $orderId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Order not found']);
        exit;
    }
    
    // Update order
    $updateQuery = "
        UPDATE `order` SET 
            email = ?,
            nama_server = ?,
            paket = ?,
            status_bayar = ?,
            pembayaran = ?,
            jumlah_bayar = ?,
            updated_at = NOW()
        WHERE id = ?
    ";
    
    $updateStmt = $conn->prepare($updateQuery);
    if (!$updateStmt) {
        throw new Exception('Prepare statement error: ' . $conn->error);
    }
    
    $updateStmt->bind_param("sssssdi", $email, $namaServer, $paket, $statusBayar, $pembayaran, $jumlahBayar, $orderId);
    
    if ($updateStmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Order updated successfully'
        ]);
    } else {
        throw new Exception('Failed to update order: ' . $updateStmt->error);
    }
    
} catch (Exception $e) {
    error_log('update_order.php error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Clean output buffer
ob_end_flush();
?>