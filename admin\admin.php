<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely for admin panel
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include config after session is handled
require_once __DIR__ . '/../includes/config.php';

// Handle AJAX requests FIRST before any output
if (isset($_GET['action'])) {
    include __DIR__ . '/../database/database.php';
    
    // Check authentication - but be more lenient for debugging
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        // For debugging, let's check if this is a valid admin session in a different way
        if (!isset($_SESSION['email']) || empty($_SESSION['email'])) {
            header('Content-Type: application/json');
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated - no email in session']);
            exit;
        }
        
        // Try to validate the user from database
        $email = $_SESSION['email'];
        $stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
        if ($stmt) {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if (!$user || $user['group'] !== 'ADMIN') {
                header('Content-Type: application/json');
                http_response_code(401);
                echo json_encode(['error' => 'Not authenticated - not admin user']);
                exit;
            }
            
            // Set session as logged in if user is valid admin
            $_SESSION['loggedin'] = true;
        } else {
            header('Content-Type: application/json');
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated - database error']);
            exit;
        }
    }
    
    $email = $_SESSION['email'];

    if ($_GET['action'] == 'get_unread_counts') {
        try {
            if (!$conn) {
                throw new Exception('Database connection failed');
            }
            
            // Check if chats table exists
            $tableCheck = $conn->query("SHOW TABLES LIKE 'chats'");
            if ($tableCheck->num_rows === 0) {
                // Table doesn't exist, return empty response
                header('Content-Type: application/json');
                echo json_encode([]);
                exit;
            }

            $chats = $conn->prepare("
                SELECT idchat, 
                       SUM(CASE WHEN dibaca = 0 AND email != ? THEN 1 ELSE 0 END) as unread_count 
                FROM chats 
                GROUP BY idchat 
                ORDER BY created_at DESC
            ");

            if (!$chats) {
                throw new Exception('Prepare failed: ' . $conn->error);
            }

            $chats->bind_param("s", $email);
            
            if (!$chats->execute()) {
                throw new Exception('Execute failed: ' . $chats->error);
            }
            
            $chat_results = $chats->get_result();

            $unread_counts = [];
            while ($chat = $chat_results->fetch_assoc()) {
                $unread_counts[$chat['idchat']] = $chat['unread_count'];
            }

            header('Content-Type: application/json');
            echo json_encode($unread_counts);
            exit;
        } catch (Exception $e) {
            error_log('get_unread_counts error: ' . $e->getMessage());
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
            exit;
        }
    }

    if ($_GET['action'] == 'get_new_messages') {
        try {
            if (!$conn) {
                throw new Exception('Database connection failed');
            }

            $last_chat_id = isset($_GET['last_chat_id']) ? intval($_GET['last_chat_id']) : 0;

            // Check if chats table exists
            $tableCheck = $conn->query("SHOW TABLES LIKE 'chats'");
            if ($tableCheck->num_rows === 0) {
                // Table doesn't exist, return empty response
                header('Content-Type: application/json');
                echo json_encode([]);
                exit;
            }

            $new_messages = $conn->prepare("
                SELECT idchat, 
                       MAX(email) as email, 
                       MAX(created_at) as created_at, 
                       SUM(CASE WHEN dibaca = 0 AND email != ? THEN 1 ELSE 0 END) as unread_count 
                FROM chats 
                WHERE idchat > ? 
                GROUP BY idchat 
                ORDER BY MAX(created_at) DESC
            ");

            if ($new_messages === false) {
                throw new Exception('MySQL prepare error: ' . $conn->error);
            }

            $new_messages->bind_param("si", $email, $last_chat_id);

            if (!$new_messages->execute()) {
                throw new Exception('MySQL execute error: ' . $new_messages->error);
            }

            $new_messages_results = $new_messages->get_result();

            $new_messages_array = [];
            while ($chat = $new_messages_results->fetch_assoc()) {
                $new_messages_array[] = $chat;
            }

            header('Content-Type: application/json');
            echo json_encode($new_messages_array);
            exit;
        } catch (Exception $e) {
            error_log('get_new_messages error: ' . $e->getMessage());
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
            exit;
        }
    }
}

// Regular page logic starts here
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
      header('Location: ../auth/login');
  exit;
}

include '../database/database.php';



// Cek apakah pengguna adalah admin

$email = $_SESSION['email'];

$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");

$stmt->bind_param("s", $email);

$stmt->execute();

$result = $stmt->get_result();

$user = $result->fetch_assoc();

$is_admin = ($user['group'] === 'ADMIN');



if (!$is_admin) {

      header('Location: ../pages/dashboard');

  exit;

}



// Function to get unread counts

function getUnreadCounts($conn) {

    $query = "SELECT idchat, COUNT(*) as unread_count FROM chats WHERE is_read = 0 GROUP BY idchat";

    $result = $conn->query($query);

    $unreadCounts = [];

    while ($row = $result->fetch_assoc()) {

        $unreadCounts[$row['idchat']] = $row['unread_count'];

    }

    return $unreadCounts;

}



// Function to get new messages

function getNewMessages($conn, $lastChatId) {

    $query = "SELECT * FROM chats WHERE idchat > ? ORDER BY idchat ASC";

    $stmt = $conn->prepare($query);

    $stmt->bind_param("i", $lastChatId);

    $stmt->execute();

    $result = $stmt->get_result();

    $newMessages = [];

    while ($row = $result->fetch_assoc()) {

        $newMessages[] = $row;

    }

    return $newMessages;

}



// Old duplicate handlers removed - now handled at top of file

?>


<!DOCTYPE html>

<html lang="en">



<head>

  <meta charset="utf-8">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">



  <title>Dopminer.com - Admin Dashboard</title>

  <meta content="" name="description">

  <meta content="" name="keywords">

  <!-- Favicon -->
  <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">

  <link href="../assets/img/Dopminer.png" rel="apple-touch-icon">



  <!-- Google Fonts -->

  <link

    href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i"

    rel="stylesheet">



  <!-- Vendor CSS Files -->

  <link href="../assets/vendor/aos/aos.css" rel="stylesheet">

  <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
  
  <!-- Responsive Card Layout CSS -->
  <style>
    /* Global Font Settings */
    body {
      font-family: "Open Sans", sans-serif !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: "Jost", sans-serif !important;
    }
    /* Card Layout Styles */
    .data-grid {
      display: grid;
      gap: 20px;
      margin: 20px 0;
    }
    
    /* Desktop: 2 columns */
    @media (min-width: 992px) {
      .data-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    /* Tablet: 1 column */
    @media (max-width: 991px) {
      .data-grid {
        grid-template-columns: 1fr;
      }
    }
    
    /* Mobile: 1 column with smaller gaps */
    @media (max-width: 576px) {
      .data-grid {
        gap: 15px;
        margin: 15px 0;
      }
    }
    
    .data-card {
      background: #2b3035;
      border: 1px solid #404040;
      border-radius: 12px;
      padding: 20px;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      outline: none;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
    }
    
    .data-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #2b3035 0%, #1a1d20 100%);
      border-radius: 11px;
      z-index: -1;
    }
    
    .data-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
      border-color: #46aedf;
      outline: none;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
      flex-wrap: wrap;
      gap: 10px;
      position: relative;
      z-index: 1;
    }
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #46aedf;
      margin: 0;
      flex: 1;
      min-width: 200px;
    }
    
    .card-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .card-body {
      margin: 15px 0;
      position: relative;
      z-index: 1;
    }
    
    .card-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .card-row:last-child {
      border-bottom: none;
    }
    
    .card-label {
      font-weight: 500;
      color: #b0b0b0;
      font-size: 14px;
      min-width: 100px;
    }
    
    .card-value {
      font-weight: 600;
      color: white;
      font-size: 14px;
      text-align: right;
      flex: 1;
    }
    
    .card-actions {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;
    }
    
    .card-actions .btn {
      flex: 1;
      min-width: 120px;
    }
    
    /* Mobile optimizations */
    @media (max-width: 576px) {
      .data-card {
        padding: 15px;
      }
      
      .card-title {
        font-size: 16px;
        min-width: auto;
      }
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
      
      .card-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
      }
      
      .card-label {
        min-width: auto;
      }
      
      .card-value {
        text-align: left;
      }
      
      .card-actions {
        flex-direction: column;
      }
      
      .card-actions .btn {
        width: 100%;
        min-width: auto;
      }
    }
    
    /* Status colors */
    .status-active { background: #28a745; color: white; }
    .status-expiring { background: #ffc107; color: #212529; }
    .status-expired { background: #dc3545; color: white; }
    .status-admin { background: #dc3545; color: white; }
    .status-user { background: #28a745; color: white; }
    .status-vps { background: #46aedf; color: white; }
    .status-pma { background: #ff6b35; color: white; }
    .status-fivem { background: #00d4aa; color: white; }
    .status-redm { background: #ff4757; color: white; }
    
    /* Loading animation */
    .loading-grid {
      display: grid;
      gap: 20px;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .loading-card {
      background: #2b3035;
      border-radius: 12px;
      padding: 20px;
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
    
    @keyframes pulse {
      0% { opacity: 0.6; }
      100% { opacity: 1; }
    }
    
    .loading-line {
      height: 20px;
      background: #404040;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    .loading-line.short { width: 60%; }
    .loading-line.medium { width: 80%; }
    .loading-line.long { width: 100%; }
    
    /* Remove any unwanted borders/outlines */
    .data-card *,
    .data-card *:before,
    .data-card *:after {
      box-sizing: border-box;
    }
    
    .data-card .btn:focus,
    .data-card .btn:active,
    .data-card button:focus,
    .data-card button:active {
      outline: none !important;
      box-shadow: none !important;
    }
    
    /* Remove white borders from any child elements */
    .data-card .card-header,
    .data-card .card-body,
    .data-card .card-actions,
    .data-card .card-row {
      border: none;
      outline: none;
    }
  </style>

  <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

  <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">

  <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">

  <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">



  <!-- Template Main CSS File -->

  <link href="../assets/css/style.css" rel="stylesheet">

  <!-- Sweetalert Custom CSS -->
  <link rel="stylesheet" href="../assets/css/sweetalert-custom.css">



  <style>

    .admin-dashboard-container {

      display: flex;

      flex-direction: column;

      align-items: center;

      padding: 20px;

      background-color: rgb(26 26 26 / 72%);

      color: white;

      border-radius: 10px;

      margin-top: 20px;

    }



    /* Old admin-menu styles removed - now using modern nav cards */



    .admin-details {

      width: 100%;

      background-color: #2c2f33;

      padding: 20px;

      border-radius: 10px;

      transition: opacity 0.5s ease-in-out;

    }



    .admin-details.hidden {

      opacity: 0;

    }



    .admin-details.visible {

      opacity: 1;

    }



    .admin-details h3 {

      margin-bottom: 20px;

    }



    .admin-details p {

      margin: 10px 0;

    }



    .active-button {

      background-color: #202f42 !important;

    }

    /* Loading Styles */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      margin: 20px 0;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(79, 195, 247, 0.2);
      border-top: 4px solid #4fc3f7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .progress-container {
      width: 100%;
      max-width: 400px;
      margin: 20px 0;
    }

    .progress-bar-custom {
      width: 100%;
      height: 8px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      overflow: hidden;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4fc3f7, #29b6f6, #4fc3f7);
      background-size: 200% 100%;
      border-radius: 10px;
      animation: progressAnimation 2s ease-in-out infinite;
      width: 0%;
      transition: width 0.3s ease;
    }

    @keyframes progressAnimation {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }

    .loading-text {
      color: #4fc3f7;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 10px;
      text-align: center;
    }

    .loading-subtext {
      color: #b0b0b0;
      font-size: 0.9rem;
      text-align: center;
      margin-bottom: 20px;
    }

    .progress-percentage {
      color: #4fc3f7;
      font-size: 0.9rem;
      font-weight: 600;
      text-align: center;
      margin-top: 10px;
    }

    /* Active menu item styles */
    .nav-item.active {
      background: rgba(79, 195, 247, 0.15) !important;
      border-color: #4fc3f7 !important;
      transform: translateX(8px) !important;
      box-shadow: 0 4px 15px rgba(79, 195, 247, 0.2) !important;
    }

    .nav-item.active[id="server-management-card"] {
      background: rgba(76, 175, 80, 0.15) !important;
      border-color: #4caf50 !important;
      box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2) !important;
    }

    .nav-item.active[id="email-approvals-card"] {
      background: rgba(255, 152, 0, 0.15) !important;
      border-color: #ff9800 !important;
      box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2) !important;
    }

    .nav-item.active[id="settings-card"] {
      background: rgba(156, 39, 176, 0.15) !important;
      border-color: #9c27b0 !important;
      box-shadow: 0 4px 15px rgba(156, 39, 176, 0.2) !important;
    }

    /* Sidebar styles */
    .admin-sidebar {
      max-height: calc(100vh - 200px);
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: rgba(79, 195, 247, 0.3) transparent;
    }

    .admin-sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .admin-sidebar::-webkit-scrollbar-track {
      background: transparent;
    }

    .admin-sidebar::-webkit-scrollbar-thumb {
      background: rgba(79, 195, 247, 0.3);
      border-radius: 3px;
    }

    .admin-sidebar::-webkit-scrollbar-thumb:hover {
      background: rgba(79, 195, 247, 0.5);
    }

    /* Responsive design for sidebar layout */
    @media (max-width: 991px) {
      .admin-sidebar {
        position: static !important;
        margin-bottom: 20px;
      }

      .admin-nav-menu {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
        gap: 12px !important;
      }

      .nav-item {
        margin-bottom: 0 !important;
      }
    }

    @media (max-width: 768px) {
      h2 {
        font-size: 2rem !important;
        margin: 15px 0 !important;
      }

      .admin-sidebar {
        padding: 20px !important;
        position: static !important;
      }

      .admin-nav-menu {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
        gap: 10px !important;
      }

      .nav-item {
        padding: 14px !important;
      }

      .nav-item h5 {
        font-size: 0.95rem !important;
      }

      .nav-item p {
        font-size: 0.75rem !important;
      }

      .admin-content {
        padding: 20px !important;
        min-height: 500px !important;
      }

      .loading-container {
        padding: 40px 15px;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
      }

      .progress-container {
        max-width: 300px;
      }
    }

    @media (max-width: 576px) {
      .admin-nav-menu {
        grid-template-columns: 1fr !important;
      }

      .nav-item {
        gap: 10px !important;
      }

      .nav-item div[style*="width: 40px"] {
        width: 35px !important;
        height: 35px !important;
      }

      .nav-item i {
        font-size: 1rem !important;
      }
    }

    /* Smooth transitions for content */
    .admin-details {
      transition: all 0.5s ease-in-out;
    }

    .admin-details.hidden {
      opacity: 0;
      transform: translateY(10px);
    }

    .admin-details.visible {
      opacity: 1;
      transform: translateY(0);
    }

    /* Loading animation improvements */
    .loading-spinner {
      position: relative;
    }

    .loading-spinner::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      background: #4fc3f7;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: pulse 1.5s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
      50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
    }

    /* Admin dashboard improvements */
    .admin-dashboard-section {
      min-height: 100vh;
    }
    
    .admin-sidebar {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }
    
    .admin-content {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    /* Welcome message styling */
    .welcome-message {
      background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
      border: 1px solid rgba(79, 195, 247, 0.2);
      border-radius: 20px;
      padding: 30px;
      text-align: center;
      margin: 20px 0;
    }

    .welcome-message h3 {
      color: #4fc3f7;
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .welcome-message p {
      color: #b0b0b0;
      font-size: 1.1rem;
      line-height: 1.6;
      margin: 0;
    }

    /* Ensure smooth transitions */
    * {
      box-sizing: border-box;
    }

  </style>



</head>



<body style="display: flex; flex-direction: column; min-height: 100vh;">



  <?php include '../components/navbar.php'; ?>



  <section id="admin-dashboard" class="admin-dashboard-section"

    style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg'); background-size: cover; background-position: center; flex: 1; padding: 150px 0;">



    <div class="container" data-aos="fade-up">

      <h2 style="margin: 20px 0; font-size: 2.5em; text-align: center;">Admin Dashboard</h2>

      <div class="row">
        <!-- Sidebar Navigation Menu -->
        <div class="col-lg-3 col-md-4 mb-4">
          <div class="admin-sidebar" style="
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            position: sticky;
            top: 20px;
          ">
          <div style="
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          ">
            <div style="
              background: linear-gradient(45deg, #4fc3f7, #29b6f6);
              border-radius: 50%;
              width: 45px;
              height: 45px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
            ">
              <i class="bi bi-speedometer2" style="color: white; font-size: 1.3rem;"></i>
            </div>
            <div>
              <h4 style="color: #4fc3f7; margin: 0; font-weight: 700; font-size: 1.2rem;">Admin Panel</h4>
              <p style="color: #b0b0b0; margin: 0; font-size: 0.85rem;">Control Center</p>
            </div>
          </div>

          <div class="admin-nav-menu">
            <div class="nav-item" id="user-management-card" onclick="showUserManagement()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(79, 195, 247, 0.1)'; this.style.borderColor='#4fc3f7'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-people-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #4fc3f7; margin: 0; font-weight: 600; font-size: 1rem;">User Management</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Kelola pengguna sistem</p>
              </div>
            </div>

            <div class="nav-item" id="server-management-card" onclick="showServerManagement()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(76, 175, 80, 0.1)'; this.style.borderColor='#4caf50'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #4caf50, #45a049);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-hdd-network-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #4caf50; margin: 0; font-weight: 600; font-size: 1rem;">Server Management</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Kelola server hosting</p>
              </div>
            </div>

            <div class="nav-item" id="email-approvals-card" onclick="showEmailApprovals()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(255, 152, 0, 0.1)'; this.style.borderColor='#ff9800'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #ff9800, #f57c00);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-envelope-check-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #ff9800; margin: 0; font-weight: 600; font-size: 1rem;">Email Approvals</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Persetujuan email</p>
              </div>
            </div>

            <div class="nav-item" id="qris-history-card" onclick="loadQrisHistory()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(76, 175, 80, 0.1)'; this.style.borderColor='#4caf50'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #4caf50, #388e3c);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-credit-card-2-front" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #4caf50; margin: 0; font-weight: 600; font-size: 1rem;">Riwayat Qris</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Riwayat transaksi QRIS</p>
              </div>
            </div>

            <div class="nav-item" id="order-management-card" onclick="showOrderManagement()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(255, 193, 7, 0.1)'; this.style.borderColor='#ffc107'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #ffc107, #ffb300);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-cart-check-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #ffc107; margin: 0; font-weight: 600; font-size: 1rem;">Order Management</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Kelola pesanan pelanggan</p>
              </div>
            </div>

            <div class="nav-item" id="settings-card" onclick="showSettings()" style="
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 12px;
            " onmouseover="this.style.background='rgba(156, 39, 176, 0.1)'; this.style.borderColor='#9c27b0'; this.style.transform='translateX(5px)';" 
               onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateX(0)';">
              <div style="
                background: linear-gradient(45deg, #9c27b0, #7b1fa2);
                border-radius: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
              ">
                <i class="bi bi-gear-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
              <div style="flex: 1;">
                <h5 style="color: #9c27b0; margin: 0; font-weight: 600; font-size: 1rem;">Settings</h5>
                <p style="color: #b0b0b0; margin: 2px 0 0 0; font-size: 0.8rem;">Konfigurasi sistem</p>
              </div>
            </div>
          </div>
                  </div>
        </div>

        <!-- Main Content Area -->
        <div class="col-lg-9 col-md-8">
          <div class="admin-content" style="
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            min-height: 600px;
          ">
            <div id="details-content" style="
              background: transparent;
              padding: 0;
              border-radius: 0;
            ">

          <div class="welcome-message">
            <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
              <div style="
                background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                border-radius: 50%;
                width: 70px;
                height: 70px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
              ">
                <i class="bi bi-shield-check" style="color: white; font-size: 2rem;"></i>
        </div>
      </div>
            <h3>Selamat Datang di Admin Dashboard</h3>
            <p>Kelola sistem dengan mudah menggunakan panel kontrol yang telah disediakan. Pilih menu di atas untuk memulai mengelola pengguna, server, email approvals, atau pengaturan sistem.</p>
            
            <div style="display: flex; justify-content: center; gap: 20px; margin-top: 25px; flex-wrap: wrap;">
              <div style="
                background: rgba(79, 195, 247, 0.1);
                border: 1px solid rgba(79, 195, 247, 0.3);
                border-radius: 12px;
                padding: 15px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
              ">
                <i class="bi bi-people-fill" style="color: #4fc3f7; font-size: 1.2rem;"></i>
                <span style="color: #e0e0e0;">User Management</span>
              </div>
              <div style="
                background: rgba(76, 175, 80, 0.1);
                border: 1px solid rgba(76, 175, 80, 0.3);
                border-radius: 12px;
                padding: 15px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
              ">
                <i class="bi bi-hdd-network-fill" style="color: #4caf50; font-size: 1.2rem;"></i>
                <span style="color: #e0e0e0;">Server Management</span>
              </div>
              <div style="
                background: rgba(255, 152, 0, 0.1);
                border: 1px solid rgba(255, 152, 0, 0.3);
                border-radius: 12px;
                padding: 15px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
              ">
                <i class="bi bi-envelope-check-fill" style="color: #ff9800; font-size: 1.2rem;"></i>
                <span style="color: #e0e0e0;">Email Approvals</span>
              </div>
              <div style="
                background: rgba(156, 39, 176, 0.1);
                border: 1px solid rgba(156, 39, 176, 0.3);
                border-radius: 12px;
                padding: 15px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
              ">
                <i class="bi bi-gear-fill" style="color: #9c27b0; font-size: 1.2rem;"></i>
                <span style="color: #e0e0e0;">Settings</span>
              </div>
                         </div>
           </div>
         </div>
       </div>
    </div>

  </section>


  
  <script>

    // Loading function with progress bar
    function showLoadingWithProgress(title, subtitle, color = '#4fc3f7') {
      return `
        <div class="loading-container">
          <div class="loading-spinner" style="border-top-color: ${color};"></div>
          <div class="loading-text" style="color: ${color};">${title}</div>
          <div class="loading-subtext">${subtitle}</div>
          <div class="progress-container">
            <div class="progress-bar-custom">
              <div class="progress-fill" id="progress-fill" style="background: linear-gradient(90deg, ${color}, ${color}aa, ${color});"></div>
            </div>
            <div class="progress-percentage" id="progress-percentage" style="color: ${color};">0%</div>
          </div>
        </div>
      `;
    }

    // Progress animation function with messages
    function animateProgress(duration = 2000, messages = []) {
      const progressFill = document.getElementById('progress-fill');
      const progressPercentage = document.getElementById('progress-percentage');
      const loadingSubtext = document.querySelector('.loading-subtext');
      
      if (!progressFill || !progressPercentage) return;
      
      let progress = 0;
      const increment = 100 / (duration / 50);
      let messageIndex = 0;
      
      // Default messages if none provided
      const defaultMessages = [
        'Menginisialisasi koneksi...',
        'Mengambil data dari database...',
        'Memproses informasi...',
        'Menyiapkan tampilan...',
        'Selesai!'
      ];
      
      const progressMessages = messages.length > 0 ? messages : defaultMessages;
      
      const timer = setInterval(() => {
        progress += increment;
        if (progress >= 100) {
          progress = 100;
          clearInterval(timer);
        }
        
        // Update progress bar
        progressFill.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
        
        // Update message based on progress
        const newMessageIndex = Math.floor((progress / 100) * (progressMessages.length - 1));
        if (newMessageIndex !== messageIndex && loadingSubtext) {
          messageIndex = newMessageIndex;
          loadingSubtext.textContent = progressMessages[messageIndex];
        }
      }, 50);
    }

    // Set active menu item
    function setActiveMenu(menuId) {
      // Remove active class from all nav items
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });
      
      // Add active class to selected menu
      const activeItem = document.getElementById(menuId);
      if (activeItem) {
        activeItem.classList.add('active');
      }
    }

    function showUserManagement() {
      // Stop QRIS auto-refresh when leaving QRIS page
      handleMenuChange();
      
      // Save active menu state
      localStorage.setItem('activeMenu', 'user-management');
      setActiveMenu('user-management-card');

      const detailsContent = document.getElementById('details-content');

      detailsContent.classList.remove('visible');
      detailsContent.classList.add('hidden');

      // Show loading immediately
      detailsContent.innerHTML = showLoadingWithProgress(
        'Loading User Management',
        'Mengambil data pengguna dari server...',
        '#4fc3f7'
      );
      
      detailsContent.classList.remove('hidden');
      detailsContent.classList.add('visible');
      
      // Start progress animation with user-specific messages
      setTimeout(() => animateProgress(1500, [
        'Menginisialisasi sistem...',
        'Mengambil data pengguna...',
        'Memvalidasi akses pengguna...',
        'Menyiapkan interface...',
        'Berhasil dimuat!'
      ]), 100);

      setTimeout(() => {

        fetch('api/get_users.php?v=' + Date.now())
          .then(response => {
            console.log('Users API Response Status:', response.status);
            console.log('Users API Response URL:', response.url);
            console.log('Users API Response Headers:', response.headers);
            console.log('Users API Response Type:', response.type);
            
            // Try to get response text for debugging
            return response.text().then(text => {
              console.log('Users API Raw Response:', text);
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
              }
              try {
                return JSON.parse(text);
              } catch (e) {
                console.error('JSON Parse Error:', e);
                throw new Error(`Invalid JSON response: ${text}`);
              }
            });
          })
          .then(users => {
            console.log('Users data parsed successfully:', users);

            let userManagementHTML = `
              <!-- Header Section -->
              <div style="
                background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                border: 1px solid rgba(79, 195, 247, 0.2);
                border-radius: 20px;
                padding: 25px;
                margin-bottom: 30px;
                text-align: center;
              ">
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
                  ">
                    <i class="bi bi-people-fill" style="color: white; font-size: 1.8rem;"></i>
                  </div>
                  <div>
                    <h2 style="color: #4fc3f7; margin: 0; font-weight: 700; font-size: 1.8rem;">
                      User Management
                    </h2>
                    <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                      Kelola pengguna terdaftar dan akses sistem
                    </p>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                  <div style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 12px 20px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  ">
                    <i class="bi bi-info-circle" style="color: #4fc3f7;"></i>
                    <span style="color: #e0e0e0; font-size: 0.9rem;">
                      Total: <strong style="color: #4fc3f7;" id="user-count">${users.length}</strong> pengguna
                    </span>
                  </div>
                </div>
              </div>

              <!-- Search and Filter Section -->
              <div style="
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 25px;
              ">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                  <i class="bi bi-search" style="color: #4fc3f7; font-size: 1.2rem;"></i>
                  <h4 style="color: #4fc3f7; margin: 0; font-weight: 600;">Search & Filter Users</h4>
                </div>
                
                <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                  <!-- Search Input -->
                  <div style="flex: 1; min-width: 250px;">
                    <div style="position: relative;">
                      <input type="text" id="user-search-input" placeholder="Cari email, tanggal bergabung..." style="
                        width: 100%;
                        background: rgba(255, 255, 255, 0.08);
                        border: 1px solid rgba(255, 255, 255, 0.15);
                        border-radius: 12px;
                        padding: 12px 45px 12px 15px;
                        color: #ffffff;
                        font-size: 0.9rem;
                        transition: all 0.3s ease;
                        box-sizing: border-box;
                      " onkeyup="filterUsers()" onfocus="this.style.borderColor='#4fc3f7'; this.style.background='rgba(255, 255, 255, 0.12)';" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.background='rgba(255, 255, 255, 0.08)';">
                      <i class="bi bi-search" style="
                        position: absolute;
                        top: 50%;
                        right: 15px;
                        transform: translateY(-50%);
                        color: #b0b0b0;
                        font-size: 0.9rem;
                      "></i>
                    </div>
                  </div>
                  
                  <!-- Filter Buttons -->
                  <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button id="filter-all-users" onclick="filterUsersByGroup('ALL')" style="
                      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                      border: none;
                      color: white;
                      padding: 10px 16px;
                      border-radius: 8px;
                      font-weight: 500;
                      font-size: 0.85rem;
                      transition: all 0.3s ease;
                      cursor: pointer;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                    " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(79, 195, 247, 0.4)';" 
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                      <i class="bi bi-people"></i>
                      All Users
                    </button>
                    
                    <button id="filter-admin-users" onclick="filterUsersByGroup('ADMIN')" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      color: #e0e0e0;
                      padding: 10px 16px;
                      border-radius: 8px;
                      font-weight: 500;
                      font-size: 0.85rem;
                      transition: all 0.3s ease;
                      cursor: pointer;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                    " onmouseover="this.style.background='rgba(255, 152, 0, 0.2)'; this.style.borderColor='#ff9800'; this.style.color='#ff9800';" 
                       onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                      <i class="bi bi-shield-check"></i>
                      Admin
                    </button>
                    
                    <button id="filter-user-users" onclick="filterUsersByGroup('USER')" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      color: #e0e0e0;
                      padding: 10px 16px;
                      border-radius: 8px;
                      font-weight: 500;
                      font-size: 0.85rem;
                      transition: all 0.3s ease;
                      cursor: pointer;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                    " onmouseover="this.style.background='rgba(76, 175, 80, 0.2)'; this.style.borderColor='#4caf50'; this.style.color='#4caf50';" 
                       onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                      <i class="bi bi-person"></i>
                      Regular Users
                    </button>
                    
                    <button onclick="clearUserFilters()" style="
                      background: rgba(255, 68, 68, 0.1);
                      border: 1px solid rgba(255, 68, 68, 0.3);
                      color: #ff4444;
                      padding: 10px 16px;
                      border-radius: 8px;
                      font-weight: 500;
                      font-size: 0.85rem;
                      transition: all 0.3s ease;
                      cursor: pointer;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                    " onmouseover="this.style.background='rgba(255, 68, 68, 0.2)'; this.style.borderColor='#ff4444';" 
                       onmouseout="this.style.background='rgba(255, 68, 68, 0.1)'; this.style.borderColor='rgba(255, 68, 68, 0.3)';">
                      <i class="bi bi-x-circle"></i>
                      Clear
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Users Grid -->
              <div id="users-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 25px;">
              </div>
              
              <!-- Pagination for Users -->
              <div id="users-pagination" style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                margin-top: 30px;
                padding: 20px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
              ">
                <!-- Pagination will be generated by JavaScript -->
              </div>
            `;

            // Initialize pagination variables
            window.currentUserPage = 1;
            window.usersPerPage = 4;
            window.filteredUsers = users;

            // Render users with pagination
            window.renderUserCards = function() {
              const startIndex = (window.currentUserPage - 1) * window.usersPerPage;
              const endIndex = startIndex + window.usersPerPage;
              const usersToShow = window.filteredUsers.slice(startIndex, endIndex);
              
              let userCardsHTML = '';
              
              usersToShow.forEach(user => {
              const groupClass = user.group === 'ADMIN' ? 'admin' : 'user';
              const groupColor = user.group === 'ADMIN' ? '#ff9800' : '#4caf50';
              const groupBg = user.group === 'ADMIN' ? 'rgba(255, 152, 0, 0.2)' : 'rgba(76, 175, 80, 0.2)';
              const createdDate = new Date(user.created_at).toLocaleDateString('id-ID', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              });
              
                userCardsHTML += `
                <div class="user-card" data-email="${user.email.toLowerCase()}" data-group="${user.group}" data-date="${user.created_at}" style="
                  background: rgba(255, 255, 255, 0.05);
                  backdrop-filter: blur(10px);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                  border-radius: 20px;
                  padding: 25px;
                  transition: all 0.3s ease;
                  position: relative;
                  overflow: hidden;
                " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7';" 
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                  
                  <!-- Status Badge -->
                  <div style="position: absolute; top: 20px; right: 20px;">
                    <span style="
                      background: ${groupBg};
                      color: ${groupColor};
                      border: 1px solid ${groupColor};
                      padding: 6px 12px;
                      border-radius: 20px;
                      font-size: 0.7rem;
                      font-weight: 700;
                      text-transform: uppercase;
                      letter-spacing: 0.5px;
                    ">${user.group}</span>
                  </div>
                  
                  <!-- User Header -->
                  <div style="margin-bottom: 20px; padding-right: 80px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                      <div style="
                        background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                        border-radius: 50%;
                        width: 45px;
                        height: 45px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
                      ">
                        <i class="bi bi-person-fill" style="color: white; font-size: 1.2rem;"></i>
                    </div>
                      <div>
                        <h4 style="color: #4fc3f7; margin: 0; font-weight: 600; font-size: 1.1rem; word-break: break-all;">
                          ${user.email}
                        </h4>
                        <small style="color: #b0b0b0; font-size: 0.8rem;">
                          <i class="bi bi-calendar3"></i> Bergabung ${createdDate}
                        </small>
                      </div>
                    </div>
                  </div>
                  
                  <!-- User Details -->
                  <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 15px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                      <i class="bi bi-key" style="color: #4fc3f7; font-size: 1rem;"></i>
                      <strong style="color: #e0e0e0; font-size: 0.9rem;">Password</strong>
                    </div>
                    <div style="
                      background: rgba(255, 255, 255, 0.05);
                      border-radius: 8px;
                      padding: 10px;
                      font-family: 'Courier New', monospace;
                      color: #fff;
                      font-size: 0.85rem;
                      word-break: break-all;
                      border-left: 3px solid #4fc3f7;
                    ">
                      ••••••••••••••••
                  </div>
                </div>

                  <!-- Actions -->
                  <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button onclick="resetPassword('${user.email}')" style="
                      background: linear-gradient(45deg, #ff9800, #f57c00);
                      border: none;
                      color: white;
                      padding: 8px 16px;
                      border-radius: 8px;
                      font-weight: 500;
                      font-size: 0.8rem;
                      transition: all 0.3s ease;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                      cursor: pointer;
                      flex: 1;
                      justify-content: center;
                      min-width: 120px;
                    " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(255, 152, 0, 0.4)';" 
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                      <i class="bi bi-key"></i>
                      Reset Password
                    </button>
                    
                    ${user.group !== 'ADMIN' ? `
                      <button onclick="deleteUser('${user.email}')" style="
                        background: linear-gradient(45deg, #f44336, #d32f2f);
                        border: none;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 8px;
                        font-weight: 500;
                        font-size: 0.8rem;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        cursor: pointer;
                        flex: 1;
                        justify-content: center;
                        min-width: 120px;
                      " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(244, 67, 54, 0.4)';" 
                         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                        <i class="bi bi-trash3"></i>
                        Delete User
                      </button>
                    ` : ''}
                  </div>
                </div>
              `;
            });

              document.getElementById('users-grid').innerHTML = userCardsHTML;
              window.renderUserPagination();
            }
            
            // Render pagination for users
            window.renderUserPagination = function() {
              const totalPages = Math.ceil(window.filteredUsers.length / window.usersPerPage);
              const paginationContainer = document.getElementById('users-pagination');
              
              if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
              }
              
              paginationContainer.style.display = 'flex';
              
              let paginationHTML = `
                <button onclick="changeUserPage(${window.currentUserPage - 1})" 
                        ${window.currentUserPage === 1 ? 'disabled' : ''} 
                        style="
                          background: ${window.currentUserPage === 1 ? 'rgba(255, 255, 255, 0.1)' : 'linear-gradient(45deg, #4fc3f7, #29b6f6)'};
                          border: none;
                          color: ${window.currentUserPage === 1 ? '#666' : 'white'};
                          padding: 10px 15px;
                          border-radius: 8px;
                          font-weight: 500;
                          cursor: ${window.currentUserPage === 1 ? 'not-allowed' : 'pointer'};
                          transition: all 0.3s ease;
                        ">
                  <i class="bi bi-chevron-left"></i>
                </button>
              `;
              
              // Show page numbers
              for (let i = 1; i <= totalPages; i++) {
                const isActive = i === window.currentUserPage;
                paginationHTML += `
                  <button onclick="changeUserPage(${i})" style="
                    background: ${isActive ? 'linear-gradient(45deg, #4fc3f7, #29b6f6)' : 'rgba(255, 255, 255, 0.1)'};
                    border: ${isActive ? 'none' : '1px solid rgba(255, 255, 255, 0.2)'};
                    color: ${isActive ? 'white' : '#e0e0e0'};
                    padding: 10px 15px;
                    border-radius: 8px;
                    font-weight: ${isActive ? '600' : '500'};
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 45px;
                  " onmouseover="if (!${isActive}) { this.style.background='rgba(79, 195, 247, 0.2)'; this.style.color='#4fc3f7'; }" 
                     onmouseout="if (!${isActive}) { this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#e0e0e0'; }">
                    ${i}
                  </button>
                `;
              }
              
              paginationHTML += `
                <button onclick="changeUserPage(${window.currentUserPage + 1})" 
                        ${window.currentUserPage === totalPages ? 'disabled' : ''} 
                        style="
                          background: ${window.currentUserPage === totalPages ? 'rgba(255, 255, 255, 0.1)' : 'linear-gradient(45deg, #4fc3f7, #29b6f6)'};
                          border: none;
                          color: ${window.currentUserPage === totalPages ? '#666' : 'white'};
                          padding: 10px 15px;
                          border-radius: 8px;
                          font-weight: 500;
                          cursor: ${window.currentUserPage === totalPages ? 'not-allowed' : 'pointer'};
                          transition: all 0.3s ease;
                        ">
                  <i class="bi bi-chevron-right"></i>
                </button>
              `;
              
              paginationContainer.innerHTML = paginationHTML;
            }
            
            // Change user page
            window.changeUserPage = function(page) {
              const totalPages = Math.ceil(window.filteredUsers.length / window.usersPerPage);
              if (page >= 1 && page <= totalPages) {
                window.currentUserPage = page;
                renderUserCards();
              }
            }

            detailsContent.innerHTML = userManagementHTML;

            // Store users data globally for filtering
            window.allUsers = users;
            
            // Initial render of user cards with pagination
            window.renderUserCards();

            detailsContent.classList.remove('hidden');

            detailsContent.classList.add('visible');

          })

          .catch(error => {
            console.error('Users API Error:', error);
            detailsContent.innerHTML = `<h3>Error loading users</h3><p>Error: ${error.message}</p>`;
            detailsContent.classList.remove('hidden');
            detailsContent.classList.add('visible');
          });

      }, 500);

    }



    function resetPassword(email) {

      // Remove any existing modal to prevent duplicates

      const existingModal = document.getElementById('resetPasswordModal');

      if (existingModal) {

        existingModal.remove();

      }



      const modalHTML = `
                <style>
          @keyframes modalSlideIn {
            from {
              opacity: 0;
              transform: translateY(-50px) scale(0.9);
            }
            to {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
          }
          
          @keyframes modalSlideOut {
            from {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
            to {
              opacity: 0;
              transform: translateY(-50px) scale(0.9);
            }
          }
          
          #resetPasswordModal input::placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
            opacity: 1 !important;
                  }
                </style>
        <div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true" style="
          transition: all 0.3s ease-in-out;
        ">
          <div class="modal-dialog modal-dialog-centered" style="
            transition: transform 0.3s ease-out;
          ">
            <div class="modal-content" style="
              background: rgba(43, 48, 53, 0.95);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(79, 195, 247, 0.3);
              border-radius: 15px;
              box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
              animation: modalSlideIn 0.3s ease-out;
            ">
              <div class="modal-header" style="
                background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                border-bottom: 1px solid rgba(79, 195, 247, 0.2);
                border-radius: 15px 15px 0 0;
                padding: 20px 25px;
              ">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="
                    background: linear-gradient(45deg, #ff9800, #f57c00);
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-key-fill" style="color: white; font-size: 1.1rem;"></i>
              </div>
                  <div>
                    <h5 style="color: #4fc3f7; margin: 0; font-weight: 600;">Reset Password</h5>
                    <small style="color: #b0b0b0;">Update user password</small>
            </div>
          </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" style="
                  background: rgba(255, 255, 255, 0.1);
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  border-radius: 50%;
                  width: 35px;
                  height: 35px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: #fff;
                  position: relative;
                  cursor: pointer;
                  transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.transform='scale(1)'">
                  <i class="bi bi-x" style="font-size: 1.2rem; font-weight: bold;"></i>
                </button>
        </div>

              <div class="modal-body" style="padding: 25px;">
                <div style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                  border-radius: 10px;
                  padding: 15px;
                  margin-bottom: 20px;
                  display: flex;
                  align-items: center;
                  gap: 12px;
                ">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 35px;
                    height: 35px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-person-fill" style="color: white; font-size: 1rem;"></i>
                  </div>
                  <div>
                    <div style="color: #4fc3f7; font-weight: 600; word-break: break-all;">${email}</div>
                    <small style="color: #b0b0b0;">Password will be reset for this user</small>
                  </div>
                </div>
                
                <div class="form-group">
                  <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                    <i class="bi bi-shield-lock"></i> New Password
                  </label>
                  <input type="password" id="newPassword" class="form-control" placeholder="Enter new password..." required style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 12px 15px;
                    color: #fff;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                  " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                  <small style="color: #ffb74d; margin-top: 8px; display: block;">
                    <i class="bi bi-info-circle"></i> Minimum 6 characters recommended
                  </small>
                </div>
              </div>
              
              <div class="modal-footer" style="
                background: rgba(255, 255, 255, 0.02);
                border-top: 1px solid rgba(79, 195, 247, 0.2);
                border-radius: 0 0 15px 15px;
                padding: 20px 25px;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
              ">
                <button type="button" class="btn" data-bs-dismiss="modal" style="
                  background: rgba(255, 255, 255, 0.1);
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  color: #e0e0e0;
                  padding: 10px 20px;
                  border-radius: 8px;
                  font-weight: 500;
                ">Cancel</button>
                <button type="button" class="btn" onclick="confirmResetPassword('${email}')" style="
                  background: linear-gradient(45deg, #ff9800, #f57c00);
                  border: none;
                  color: white;
                  padding: 10px 20px;
                  border-radius: 8px;
                  font-weight: 600;
                ">Reset Password</button>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHTML);

      $('#resetPasswordModal').modal('show');

    }



    function closeModal() {
      const modal = document.getElementById('resetPasswordModal');
      if (modal) {
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
          modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
        }
        
        // Langsung remove tanpa memanggil Bootstrap modal hide
        setTimeout(() => {
          if (document.getElementById('resetPasswordModal')) {
      document.getElementById('resetPasswordModal').remove();
          }
          // Force restore body scroll - selalu jalankan
          restoreBodyScroll();
        }, 300);
      } else {
        // Jika modal tidak ditemukan, tetap restore scroll
        restoreBodyScroll();
      }
    }



    function confirmResetPassword(email) {

      const newPassword = document.getElementById('newPassword').value;

      if (!newPassword) {

        alert('Please enter a new password.');

        return;

      }



              fetch('api/reset_password.php', {

        method: 'POST',

        headers: {

          'Content-Type': 'application/json'

        },

        body: JSON.stringify({ email: email, password: newPassword })

      })

      .then(response => response.json())

      .then(data => {

        if (data.success) {

          alert('Password has been reset successfully.');

        } else {

          alert('Failed to reset password.');

        }

        const modal = document.getElementById('resetPasswordModal');
        if (modal) {
          const modalContent = modal.querySelector('.modal-content');
          if (modalContent) {
            modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
          }
          
          // Langsung remove tanpa memanggil Bootstrap modal hide
          setTimeout(() => {
            if (document.getElementById('resetPasswordModal')) {
        document.getElementById('resetPasswordModal').remove();
            }
            // Force restore body scroll - selalu jalankan
            restoreBodyScroll();
          }, 300);
        } else {
          // Jika modal tidak ditemukan, tetap restore scroll
          restoreBodyScroll();
        }

      })

      .catch(error => console.error('Error:', error));

    }

    // Function to delete user
    function deleteUser(userEmail) {
      if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch('api/delete_user.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: userEmail
          })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success === true) {
            alert('User deleted successfully.');
            showUserManagement(); // Refresh the user management view
          } else {
            alert('Failed to delete user: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while deleting the user.');
        });
      }
    }

    //////////////////////////////////////////////////////////////////////////////



    ///////////////Order Management///////////////////////////////////////////


    function showOrderManagement() {
      // Stop QRIS auto-refresh when leaving QRIS page
      handleMenuChange();
      
      // Save active menu state
      localStorage.setItem('activeMenu', 'order-management');
      setActiveMenu('order-management-card');

      const detailsContent = document.getElementById('details-content');

      detailsContent.classList.remove('visible');
      detailsContent.classList.add('hidden');

      // Show loading immediately
      detailsContent.innerHTML = showLoadingWithProgress(
        'Loading Order Management',
        'Mengambil data pesanan dari server...',
        '#ffc107'
      );
      
      detailsContent.classList.remove('hidden');
      detailsContent.classList.add('visible');
      
      // Start progress animation
      setTimeout(() => animateProgress(1500, [
        'Menginisialisasi sistem...',
        'Mengambil data pesanan...',
        'Memvalidasi status pembayaran...',
        'Menyiapkan interface...',
        'Berhasil dimuat!'
      ]), 100);

      setTimeout(() => {
        fetch('api/get_orders.php?v=' + Date.now())
          .then(response => {
            console.log('Orders API Response Status:', response.status);
            return response.text().then(text => {
              console.log('Orders API Raw Response:', text);
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
              }
              try {
                return JSON.parse(text);
              } catch (e) {
                console.error('JSON Parse Error:', e);
                throw new Error(`Invalid JSON response: ${text}`);
              }
            });
          })
          .then(orders => {
            console.log('Orders data parsed successfully:', orders);

            let orderManagementHTML = `
              <!-- Header Section -->
              <div style="
                background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
                border: 1px solid rgba(255, 193, 7, 0.2);
                border-radius: 20px;
                padding: 25px;
                margin-bottom: 30px;
                text-align: center;
              ">
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #ffc107, #ffb300);
                    border-radius: 50%;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
                  ">
                    <i class="bi bi-cart-check-fill" style="color: white; font-size: 1.8rem;"></i>
                  </div>
                  <div>
                    <h2 style="color: #ffc107; margin: 0; font-weight: 700; font-size: 1.8rem;">
                      Order Management
                    </h2>
                    <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                      Kelola pesanan dan verifikasi pembayaran
                    </p>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; align-items: center;">
                  <div style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 12px 20px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  ">
                    <i class="bi bi-info-circle" style="color: #ffc107;"></i>
                    <span style="color: #e0e0e0; font-size: 0.9rem;">
                      Total: <strong style="color: #ffc107;" id="order-count">${orders.length}</strong> pesanan
                    </span>
                  </div>
                  
                  <!-- Add Server Button -->
                  <button onclick="showStandaloneAddServerForm()" style="
                    background: linear-gradient(45deg, #28a745, #20c997);
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 12px;
                    cursor: pointer;
                    font-size: 0.9rem;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                    transition: all 0.3s ease;
                  " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.3)'">
                    <i class="bi bi-server"></i>
                    <span>Add Server</span>
                  </button>
                </div>
              </div>

              <!-- Filter Section -->
              <div style="
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 25px;
              ">
                <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                  <div style="flex: 1; min-width: 200px;">
                    <input type="text" id="order-search" placeholder="Cari berdasarkan email, server, atau ID..." style="
                      width: 100%;
                      padding: 12px 15px;
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      border-radius: 8px;
                      color: white;
                      font-size: 0.9rem;
                    " oninput="filterOrders()">
                  </div>
                  <select id="status-filter" style="
                    padding: 12px 15px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    color: white;
                    font-size: 0.9rem;
                  " onchange="filterOrders()">
                    <option value="">Semua Status</option>
                    <option value="Menunggu Pembayaran">Menunggu Pembayaran</option>
                    <option value="Menunggu Verifikasi">Menunggu Verifikasi</option>
                    <option value="Sudah Dibayar">Sudah Dibayar</option>
                  </select>
                  <select id="payment-filter" style="
                    padding: 12px 15px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    color: white;
                    font-size: 0.9rem;
                  " onchange="filterOrders()">
                    <option value="">Semua Pembayaran</option>
                    <option value="QRIS">QRIS</option>
                    <option value="MANUAL PAYMENT">Manual Payment</option>
                  </select>
                </div>
              </div>

              <!-- Orders Grid -->
              <div class="data-grid" id="orders-grid">
            `;

            // Generate order cards
            orders.forEach(order => {
              const statusColor = {
                'Menunggu Pembayaran': '#dc3545',
                'Menunggu Verifikasi': '#ffc107',
                'Sudah Dibayar': '#28a745'
              }[order.status_bayar] || '#6c757d';

              const paymentColor = {
                'QRIS': '#28a745',
                'MANUAL PAYMENT': '#ffc107'
              }[order.pembayaran] || '#6c757d';

              orderManagementHTML += `
                <div class="data-card order-card" data-order-id="${order.id}" data-status="${order.status_bayar}" data-payment="${order.pembayaran}" data-email="${order.email}" data-server="${order.nama_server}">
                  <div class="card-header">
                    <div class="card-title">Order #${order.id}</div>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                      <span class="card-badge" style="background: ${statusColor}; color: white;">
                        ${order.status_bayar}
                      </span>
                      <span class="card-badge" style="background: ${paymentColor}; color: white;">
                        ${order.pembayaran}
                      </span>
                    </div>
                  </div>
                  
                  <div class="card-row">
                    <strong style="color: #ffc107;">📧 Email:</strong>
                    <span>${order.email}</span>
                  </div>
                  
                  <div class="card-row">
                    <strong style="color: #ffc107;">🖥️ Server:</strong>
                    <span>${order.nama_server}</span>
                  </div>
                  
                  <div class="card-row">
                    <strong style="color: #ffc107;">📦 Produk:</strong>
                    <span>${order.nama_produk || 'N/A'}</span>
                  </div>
                  
                  ${order.paket ? `
                  <div class="card-row">
                    <strong style="color: #ffc107;">📦 Paket:</strong>
                    <span>${order.paket}</span>
                  </div>
                  ` : ''}
                  
                  <div class="card-row">
                    <strong style="color: #ffc107;">💰 Jumlah:</strong>
                    <span>Rp ${parseInt(order.jumlah_bayar).toLocaleString('id-ID')}</span>
                  </div>
                  
                  <div class="card-row">
                    <strong style="color: #ffc107;">📅 Tanggal:</strong>
                    <span>${new Date(order.created_at).toLocaleString('id-ID')}</span>
                  </div>
                  
                  ${order.bukti_pembayaran ? `
                  <div class="card-row">
                    <strong style="color: #ffc107;">🖼️ Bukti Bayar:</strong>
                    <button onclick="viewPaymentProof('${order.bukti_pembayaran}')" style="
                      background: #17a2b8;
                      color: white;
                      border: none;
                      padding: 5px 10px;
                      border-radius: 5px;
                      cursor: pointer;
                      font-size: 0.8rem;
                    ">Lihat Bukti</button>
                  </div>
                  ` : ''}
                  
                  <div class="card-actions" style="margin-top: 15px; display: flex; gap: 8px; flex-wrap: wrap;">
                    ${order.status_bayar === 'Menunggu Verifikasi' ? `
                      <button onclick="verifyOrder(${order.id})" style="
                        background: #28a745;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 0.85rem;
                        flex: 1;
                      ">✅ Verifikasi</button>
                    ` : ''}
                    
                    <button onclick="editOrder(${order.id})" style="
                      background: #ffc107;
                      color: #212529;
                      border: none;
                      padding: 8px 12px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 0.85rem;
                      flex: 1;
                    ">✏️ Edit</button>
                    
                    <button onclick="deleteOrder(${order.id})" style="
                      background: #dc3545;
                      color: white;
                      border: none;
                      padding: 8px 12px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 0.85rem;
                      flex: 1;
                    ">🗑️ Hapus</button>
                  </div>
                  
                  <!-- Add Server / Detail Server Button -->
                  <div style="margin-top: 10px; width: 100%;">
                    <button id="server-btn-${order.id}" onclick="handleServerAction('${order.id_order || order.id}', ${order.id})" style="
                      background: #17a2b8;
                      color: white;
                      border: none;
                      padding: 10px 15px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 0.85rem;
                      width: 100%;
                      font-weight: 600;
                    ">🖥️ Loading...</button>
                  </div>
                </div>
                </div>
              `;
            });

            orderManagementHTML += `
              </div>
            `;

            detailsContent.innerHTML = orderManagementHTML;
            
            // Check server status for each order
            orders.forEach(order => {
              checkServerStatus(order.id_order || order.id, order.id);
            });
          })
          .catch(error => {
            console.error('Error loading orders:', error);
            detailsContent.innerHTML = `
              <div style="text-align: center; padding: 50px; color: #dc3545;">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <h3>Error Loading Orders</h3>
                <p>Gagal memuat data pesanan: ${error.message}</p>
                <button onclick="showOrderManagement()" style="
                  background: #ffc107;
                  color: #212529;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 8px;
                  cursor: pointer;
                  margin-top: 15px;
                ">Coba Lagi</button>
              </div>
            `;
          });
      }, 1500);
    }

    // Order management helper functions

// Check if server exists in data_client table
function checkServerStatus(orderId, orderDbId) {
  console.log('Checking server status for order:', orderId, 'DB ID:', orderDbId);
  fetch(`api/check_server_status.php?order_id=${encodeURIComponent(orderId)}`)
    .then(response => {
      console.log('Response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('Server status response:', data);
      const button = document.getElementById(`server-btn-${orderDbId}`);
      if (button) {
        if (data.exists) {
          button.innerHTML = '🖥️ Detail Server';
          button.style.background = '#28a745';
        } else {
          button.innerHTML = '➕ Add Server';
          button.style.background = '#17a2b8';
        }
      }
    })
    .catch(error => {
      console.error('Error checking server status:', error);
      const button = document.getElementById(`server-btn-${orderDbId}`);
      if (button) {
        button.innerHTML = '❌ Error';
        button.style.background = '#dc3545';
        button.disabled = true;
      }
    });
}

// Handle server action (Add or Detail)
function handleServerAction(orderId, orderDbId) {
  const button = document.getElementById(`server-btn-${orderDbId}`);
  if (!button) return;
  
  const buttonText = button.innerHTML;
  
  if (buttonText.includes('Detail Server')) {
    // Show server details
    showServerDetails(orderId);
  } else if (buttonText.includes('Add Server')) {
    // Show add server form
    showAddServerForm(orderId, orderDbId);
  }
}

// Show server details
function showServerDetails(orderId) {
  fetch(`api/get_server_by_order.php?order_id=${encodeURIComponent(orderId)}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const server = data.server;
        Swal.fire({
          title: '🖥️ Detail Server',
          html: `
            <div style="text-align: left; padding: 20px;">
              <p><strong>📧 Email:</strong> ${server.email}</p>
              <p><strong>🏷️ Nama Server:</strong> ${server.namakota}</p>
              <p><strong>🔧 Type Hosting:</strong> ${server.typehosting}</p>
              <p><strong>🌐 IP Hosting:</strong> ${server.iphosting || 'N/A'}</p>
              <p><strong>📦 Paket:</strong> ${server.pakethosting || 'N/A'}</p>
              <p><strong>📅 Expired:</strong> ${new Date(server.expired).toLocaleDateString('id-ID')}</p>
              <p><strong>💰 Harga Normal:</strong> Rp ${parseInt(server.harganormal || 0).toLocaleString('id-ID')}</p>
              <p><strong>💳 Harga Bulanan:</strong> Rp ${parseInt(server.hargabulanan || 0).toLocaleString('id-ID')}</p>
              ${server.catatan ? `<p><strong>📝 Catatan:</strong> ${server.catatan}</p>` : ''}
            </div>
          `,
          width: '600px',
          confirmButtonText: 'Tutup',
          confirmButtonColor: '#ffc107'
        });
      } else {
        Swal.fire('Error', data.message || 'Gagal mengambil detail server', 'error');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      Swal.fire('Error', 'Terjadi kesalahan saat mengambil detail server', 'error');
    });
}

// Show add server form
function showAddServerForm(orderId, orderDbId) {
  console.log('showAddServerForm called with:', { orderId, orderDbId });
  // Get order details first
  fetch(`api/get_order_detail.php?id=${orderDbId}`)
    .then(response => {
      console.log('get_order_detail response status:', response.status);
      return response.json();
    })
    .then(order => {
      console.log('get_order_detail response data:', order);
      // Check if response contains error
      if (order.error) {
        throw new Error(order.error);
      }
        Swal.fire({
          title: '➕ Tambah Server',
          html: `
            <div style="text-align: left; padding: 20px;">
              <p style="color: #6c757d; font-size: 14px; margin-bottom: 20px; text-align: center;">💡 <strong>Info:</strong> Semua field bersifat opsional. Anda bisa mengisi kosong atau sesuai kebutuhan.</p>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; align-items: start;">
                <div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">📧 Email (Opsional):</label>
                    <input type="email" id="server-email" value="${order.email || ''}" placeholder="<EMAIL>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">🏷️ Nama Server (Opsional):</label>
                    <input type="text" id="server-name" value="${order.nama_server || ''}" placeholder="Nama server..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">🔧 Type Hosting (Opsional):</label>
                    <select id="server-type" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                      <option value="">-- Pilih Type Hosting --</option>
                      <option value="FIVEM">FIVEM</option>
                      <option value="REDM">REDM</option>
                      <option value="SAMP">SAMP</option>
                      <option value="VPS">VPS</option>
                      <option value="PMA">PMA</option>
                      <option value="BOT">BOT</option>
                    </select>
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">🌐 IP Hosting (Opsional):</label>
                    <input type="text" id="server-ip" placeholder="*********** atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">📦 Paket Hosting (Opsional):</label>
                    <input type="text" id="server-package" value="${order.paket || ''}" placeholder="Nama paket atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                </div>
                <div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">📅 Tanggal Expired (Opsional):</label>
                    <input type="date" id="server-expired" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">💰 Harga Normal (Opsional):</label>
                    <input type="number" id="server-price-normal" value="${order.jumlah_bayar || ''}" placeholder="0 atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">💳 Harga Bulanan (Opsional):</label>
                    <input type="number" id="server-price-monthly" value="${order.jumlah_bayar || ''}" placeholder="0 atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">📝 Catatan (Opsional):</label>
                    <textarea id="server-notes" placeholder="Catatan tambahan atau kosongkan..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 120px; resize: vertical;"></textarea>
                  </div>
                </div>
              </div>
              <input type="hidden" id="order-id" value="${orderId}">
            </div>
          `,
          width: '900px',
          showCancelButton: true,
          confirmButtonText: '💾 Simpan Server',
          cancelButtonText: 'Batal',
          confirmButtonColor: '#28a745',
          cancelButtonColor: '#dc3545',
          preConfirm: () => {
            const email = document.getElementById('server-email').value;
            const name = document.getElementById('server-name').value;
            const type = document.getElementById('server-type').value;
            const ip = document.getElementById('server-ip').value;
            const packageName = document.getElementById('server-package').value;
            const expired = document.getElementById('server-expired').value;
            const priceNormal = document.getElementById('server-price-normal').value;
            const priceMonthly = document.getElementById('server-price-monthly').value;
            const notes = document.getElementById('server-notes').value;
            
            // All fields are optional, no validation required
            
            return {
              order_id: orderId,
              email: email,
              namakota: name,
              typehosting: type,
              iphosting: ip,
              pakethosting: packageName,
              expired: expired,
              harganormal: priceNormal,
              hargabulanan: priceMonthly,
              catatan: notes
            };
          }
        }).then((result) => {
          if (result.isConfirmed) {
            // Save server data
            fetch('api/add_server.php', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                Swal.fire('Berhasil!', 'Server berhasil ditambahkan', 'success');
                // Update button
                checkServerStatus(orderId, orderDbId);
              } else {
                Swal.fire('Error', data.message || 'Gagal menambahkan server', 'error');
              }
            })
            .catch(error => {
              console.error('Error:', error);
              Swal.fire('Error', 'Terjadi kesalahan saat menambahkan server', 'error');
            });
          }
        });
        
        // Set default expired date (1 month from now)
        const expiredInput = document.getElementById('server-expired');
        if (expiredInput) {
          const nextMonth = new Date();
          nextMonth.setMonth(nextMonth.getMonth() + 1);
          expiredInput.value = nextMonth.toISOString().split('T')[0];
        }
    })
    .catch(error => {
      console.error('Error:', error);
      Swal.fire('Error', 'Terjadi kesalahan saat mengambil detail order', 'error');
    });
}

// Show standalone add server form (not linked to any order)
function showStandaloneAddServerForm() {
  Swal.fire({
    title: '🖥️ Tambah Server Baru',
    html: `
      <div style="text-align: left; padding: 20px;">
        <p style="color: #6c757d; font-size: 14px; margin-bottom: 20px; text-align: center;">💡 <strong>Info:</strong> Semua field bersifat opsional. Anda bisa mengisi kosong atau sesuai kebutuhan.</p>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; align-items: start;">
          <div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">📧 Email (Opsional):</label>
              <input type="email" id="standalone-server-email" placeholder="<EMAIL>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">🏷️ Nama Server (Opsional):</label>
              <input type="text" id="standalone-server-name" placeholder="Nama server..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">🔧 Type Hosting (Opsional):</label>
              <select id="standalone-server-type" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">-- Pilih Type Hosting --</option>
                <option value="FIVEM">FIVEM</option>
                <option value="REDM">REDM</option>
                <option value="SAMP">SAMP</option>
                <option value="VPS">VPS</option>
                <option value="PMA">PMA</option>
                <option value="BOT">BOT</option>
              </select>
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">🌐 IP Hosting (Opsional):</label>
              <input type="text" id="standalone-server-ip" placeholder="*********** atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">📦 Paket Hosting (Opsional):</label>
              <input type="text" id="standalone-server-package" placeholder="Nama paket atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
          </div>
          <div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">📅 Tanggal Expired (Opsional):</label>
              <input type="date" id="standalone-server-expired" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">💰 Harga Normal (Opsional):</label>
              <input type="number" id="standalone-server-price-normal" placeholder="0 atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">💳 Harga Bulanan (Opsional):</label>
              <input type="number" id="standalone-server-price-monthly" placeholder="0 atau kosongkan" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">📝 Catatan (Opsional):</label>
              <textarea id="standalone-server-notes" placeholder="Catatan tambahan atau kosongkan..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 120px; resize: vertical;"></textarea>
            </div>
          </div>
        </div>
      </div>
    `,
    width: '900px',
    showCancelButton: true,
    confirmButtonText: '💾 Simpan Server',
    cancelButtonText: '❌ Batal',
    confirmButtonColor: '#28a745',
    cancelButtonColor: '#dc3545',
    preConfirm: () => {
      const email = document.getElementById('standalone-server-email').value;
      const name = document.getElementById('standalone-server-name').value;
      const type = document.getElementById('standalone-server-type').value;
      const ip = document.getElementById('standalone-server-ip').value;
      const packageName = document.getElementById('standalone-server-package').value;
      const expired = document.getElementById('standalone-server-expired').value;
      const priceNormal = document.getElementById('standalone-server-price-normal').value;
      const priceMonthly = document.getElementById('standalone-server-price-monthly').value;
      const notes = document.getElementById('standalone-server-notes').value;
      
      // All fields are optional, no validation required
      
      return {
        order_id: '', // Empty for standalone server
        email: email,
        namakota: name,
        typehosting: type,
        iphosting: ip,
        pakethosting: packageName,
        expired: expired,
        harganormal: priceNormal,
        hargabulanan: priceMonthly,
        catatan: notes
      };
    }
  }).then((result) => {
    if (result.isConfirmed) {
      // Save server data
      fetch('api/add_server.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(result.value)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire({
            title: 'Berhasil!',
            text: 'Server berhasil ditambahkan ke database',
            icon: 'success',
            confirmButtonColor: '#28a745'
          });
        } else {
          Swal.fire('Error', data.message || 'Gagal menambahkan server', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        Swal.fire('Error', 'Terjadi kesalahan saat menambahkan server', 'error');
      });
    }
  });
  
  // Set default expired date (1 month from now)
  setTimeout(() => {
    const expiredInput = document.getElementById('standalone-server-expired');
    if (expiredInput) {
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      expiredInput.value = nextMonth.toISOString().split('T')[0];
    }
  }, 100);
}

    function filterOrders() {
      const searchTerm = document.getElementById('order-search').value.toLowerCase();
      const statusFilter = document.getElementById('status-filter').value;
      const paymentFilter = document.getElementById('payment-filter').value;
      const orderCards = document.querySelectorAll('.order-card');
      
      let visibleCount = 0;
      
      orderCards.forEach(card => {
        const email = card.dataset.email.toLowerCase();
        const server = card.dataset.server.toLowerCase();
        const orderId = card.dataset.orderId;
        const status = card.dataset.status;
        const payment = card.dataset.payment;
        
        const matchesSearch = email.includes(searchTerm) || 
                             server.includes(searchTerm) || 
                             orderId.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesPayment = !paymentFilter || payment === paymentFilter;
        
        if (matchesSearch && matchesStatus && matchesPayment) {
          card.style.display = 'block';
          visibleCount++;
        } else {
          card.style.display = 'none';
        }
      });
      
      // Update count
      const countElement = document.getElementById('order-count');
      if (countElement) {
        countElement.textContent = visibleCount;
      }
    }

    function viewPaymentProof(filename) {
      const imageUrl = `/uploads/payment_proofs/${filename}`;
      
      Swal.fire({
        title: '📄 Bukti Pembayaran',
        html: `
          <div style="text-align: center; padding: 10px;">
            <img src="${imageUrl}" 
                 style="max-width: 100%; max-height: 400px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);" 
                 alt="Bukti Pembayaran"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; padding: 20px; color: #dc3545;">
              <i class="bi bi-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
              <p>Gagal memuat gambar bukti pembayaran</p>
              <small>File: ${filename}</small>
            </div>
          </div>
        `,
        showCloseButton: true,
        showConfirmButton: true,
        confirmButtonText: '👍 Tutup',
        confirmButtonColor: '#6c757d',
        width: '500px',
        customClass: {
          popup: 'swal2-popup animated fadeInDown',
          confirmButton: 'swal2-confirm',
          closeButton: 'swal2-close'
        }
      });
    }

    function verifyOrder(orderId) {
      Swal.fire({
        title: 'Verifikasi Pesanan',
        text: 'Apakah Anda yakin ingin memverifikasi pesanan ini?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '✅ Ya, Verifikasi',
        cancelButtonText: '❌ Batal',
        customClass: {
          popup: 'swal2-popup animated fadeInDown',
          confirmButton: 'swal2-confirm',
          cancelButton: 'swal2-cancel'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          // Show loading
          Swal.fire({
            title: 'Memverifikasi...',
            text: 'Mohon tunggu sebentar',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });
          
          fetch('api/verify_order.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ order_id: orderId })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                title: 'Berhasil!',
                text: 'Pesanan berhasil diverifikasi!',
                icon: 'success',
                confirmButtonColor: '#28a745',
                confirmButtonText: '👍 OK'
              }).then(() => {
                showOrderManagement(); // Refresh the list
              });
            } else {
              Swal.fire({
                title: 'Error!',
                text: data.message || 'Gagal memverifikasi pesanan',
                icon: 'error',
                confirmButtonColor: '#dc3545',
                confirmButtonText: '😞 OK'
              });
            }
          })
          .catch(error => {
            console.error('Error:', error);
            Swal.fire({
              title: 'Error!',
              text: 'Terjadi kesalahan saat memverifikasi pesanan',
              icon: 'error',
              confirmButtonColor: '#dc3545',
              confirmButtonText: '😞 OK'
            });
          });
        }
      });
    }

    function editOrder(orderId) {
      console.log("Order ID: " + orderId);
      // Fetch order details
      fetch(`api/get_order_detail.php?id=${orderId}`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(order => {
          if (order.error) {
            throw new Error(order.error);
          }
          
          // Debug: Log the received order data
          console.log('Received order data:', order);
          
          // Remove any existing modal to prevent duplicates
          const existingModal = document.getElementById('editOrderModal');
          if (existingModal) {
            existingModal.remove();
          }

          const modalHTML = `
            <style>
              @keyframes modalSlideIn {
                from {
                  opacity: 0;
                  transform: translateY(-50px) scale(0.9);
                }
                to {
                  opacity: 1;
                  transform: translateY(0) scale(1);
                }
              }
              
              @keyframes modalSlideOut {
                from {
                  opacity: 1;
                  transform: translateY(0) scale(1);
                }
                to {
                  opacity: 0;
                  transform: translateY(-50px) scale(0.9);
                }
              }
              
              #editOrderModal input::placeholder,
              #editOrderModal textarea::placeholder {
                color: rgba(255, 255, 255, 0.6) !important;
                opacity: 1 !important;
              }
              
              #editOrderModal select {
                color: #fff !important;
              }
              
              #editOrderModal select option {
                background: rgba(43, 48, 53, 0.95) !important;
                color: #fff !important;
              }
            </style>
            <div class="modal fade" id="editOrderModal" tabindex="-1" aria-labelledby="editOrderModalLabel" aria-hidden="true" style="
              transition: all 0.3s ease-in-out;
            ">
              <div class="modal-dialog modal-dialog-centered modal-lg" style="
                transition: transform 0.3s ease-out;
              ">
                <div class="modal-content" style="
                  background: rgba(43, 48, 53, 0.95);
                  backdrop-filter: blur(10px);
                  border: 1px solid rgba(79, 195, 247, 0.3);
                  border-radius: 15px;
                  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
                  animation: modalSlideIn 0.3s ease-out;
                ">
                  <div class="modal-header" style="
                    background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
                    border-radius: 15px 15px 0 0;
                    padding: 20px 25px;
                  ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <div style="
                        background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      ">
                        <i class="bi bi-pencil-square" style="color: white; font-size: 1.1rem;"></i>
                      </div>
                      <div>
                        <h5 style="color: #4fc3f7; margin: 0; font-weight: 600;">Edit Pesanan</h5>
                        <small style="color: #b0b0b0;">Update informasi pesanan</small>
                      </div>
                    </div>
                    <button type="button" class="btn-close" onclick="closeEditOrderModal()" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      border-radius: 50%;
                      width: 35px;
                      height: 35px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: #fff;
                      position: relative;
                      cursor: pointer;
                      transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.transform='scale(1)'">
                      <i class="bi bi-x" style="font-size: 1.2rem; font-weight: bold;"></i>
                    </button>
                  </div>
                  
                  <div class="modal-body" style="padding: 25px;">
                    <form id="editOrderForm">
                      <div class="row g-3">
                        <div class="col-md-6">
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-person"></i> Email Pemesan
                            </label>
                            <input type="email" id="orderEmail" class="form-control" value="${order.email || ''}" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-server"></i> Nama Server
                            </label>
                            <input type="text" id="orderNamaServer" class="form-control" value="${order.nama_server || ''}" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-box"></i> Paket
                            </label>
                            <input type="text" id="orderPaket" class="form-control" value="${order.paket || ''}" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                        </div>
                        
                        <div class="col-md-6">
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-check-circle"></i> Status Bayar
                            </label>
                            <select id="orderStatusBayar" class="form-control" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                              <option value="Menunggu Pembayaran" ${(order.status_bayar || 'Menunggu Pembayaran') === 'Menunggu Pembayaran' ? 'selected' : ''}>Menunggu Pembayaran</option>
                              <option value="Menunggu Verifikasi" ${(order.status_bayar || 'Menunggu Pembayaran') === 'Menunggu Verifikasi' ? 'selected' : ''}>Menunggu Verifikasi</option>
                              <option value="Sudah Dibayar" ${(order.status_bayar || 'Menunggu Pembayaran') === 'Sudah Dibayar' ? 'selected' : ''}>Sudah Dibayar</option>
                            </select>
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-credit-card"></i> Metode Pembayaran
                            </label>
                            <select id="orderPembayaran" class="form-control" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                              <option value="QRIS" ${(order.pembayaran || 'QRIS') === 'QRIS' ? 'selected' : ''}>QRIS</option>
                              <option value="MANUAL PAYMENT" ${(order.pembayaran || 'QRIS') === 'MANUAL PAYMENT' ? 'selected' : ''}>MANUAL PAYMENT</option>
                            </select>
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-currency-dollar"></i> Jumlah Bayar
                            </label>
                            <input type="number" id="orderJumlahBayar" class="form-control" value="${order.jumlah_bayar || 0}" min="0" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                  
                  <div class="modal-footer" style="
                    background: rgba(255, 255, 255, 0.02);
                    border-top: 1px solid rgba(79, 195, 247, 0.2);
                    border-radius: 0 0 15px 15px;
                    padding: 20px 25px;
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                  ">
                    <button type="button" class="btn" onclick="closeEditOrderModal()" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      color: #e0e0e0;
                      padding: 10px 20px;
                      border-radius: 8px;
                      font-weight: 500;
                    ">Cancel</button>
                    <button type="button" class="btn" onclick="saveOrderChanges('${orderId}')" style="
                      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                      border: none;
                      color: white;
                      padding: 10px 20px;
                      border-radius: 8px;
                      font-weight: 600;
                    ">Save Changes</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', modalHTML);
          $('#editOrderModal').modal('show');
        })
        .catch(error => {
          console.error('Error:', error);
          Swal.fire({
            title: 'Error!',
            text: 'Gagal memuat data pesanan: ' + error.message,
            icon: 'error',
            confirmButtonColor: '#d33',
            background: 'rgba(13, 18, 26, 0.95)',
            color: '#fff',
            customClass: {
              popup: 'swal2-popup animated fadeInDown'
            }
          });
        });
    }

    function deleteOrder(orderId) {
      Swal.fire({
        title: 'Hapus Pesanan',
        text: 'Apakah Anda yakin ingin menghapus pesanan ini?',
        html: `
          <div style="text-align: center; padding: 15px;">
            <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: #dc3545; margin-bottom: 15px;"></i>
            <p style="margin-bottom: 10px;">Tindakan ini tidak dapat dibatalkan!</p>
            <p style="color: #6c757d; font-size: 0.9rem;">Order ID: <strong>${orderId}</strong></p>
          </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '🗑️ Ya, Hapus',
        cancelButtonText: '❌ Batal',
        customClass: {
          popup: 'swal2-popup animated fadeInDown',
          confirmButton: 'swal2-confirm',
          cancelButton: 'swal2-cancel'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          // Show loading
          Swal.fire({
            title: 'Menghapus...',
            text: 'Mohon tunggu sebentar',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });
          
          fetch('api/delete_order.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ order_id: orderId })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                title: 'Berhasil!',
                text: 'Pesanan berhasil dihapus!',
                icon: 'success',
                confirmButtonColor: '#28a745',
                confirmButtonText: '👍 OK'
              }).then(() => {
                showOrderManagement(); // Refresh the list
              });
            } else {
              Swal.fire({
                title: 'Error!',
                text: data.message || 'Gagal menghapus pesanan',
                icon: 'error',
                confirmButtonColor: '#dc3545',
                confirmButtonText: '😞 OK'
              });
            }
          })
          .catch(error => {
            console.error('Error:', error);
            Swal.fire({
              title: 'Error!',
              text: 'Terjadi kesalahan saat menghapus pesanan',
              icon: 'error',
              confirmButtonColor: '#dc3545',
              confirmButtonText: '😞 OK'
            });
          });
        }
      });
    }

    //////////////////////////////////////////////////////////////////////////////



    ///////////////Server Management///////////////////////////////////////////


    function showServerManagement() {
      // Stop QRIS auto-refresh when leaving QRIS page
      handleMenuChange();
      
      // Save active menu state
      localStorage.setItem('activeMenu', 'server-management');
      setActiveMenu('server-management-card');

      const detailsContent = document.getElementById('details-content');

      detailsContent.classList.remove('visible');
      detailsContent.classList.add('hidden');

      // Show loading immediately
      detailsContent.innerHTML = showLoadingWithProgress(
        'Loading Server Management',
        'Mengambil data server dari database...',
        '#4caf50'
      );
      
      detailsContent.classList.remove('hidden');
      detailsContent.classList.add('visible');
      
      // Start progress animation with server-specific messages
      setTimeout(() => animateProgress(1500, [
        'Koneksi ke server database...',
        'Mengambil daftar server...',
        'Memverifikasi status server...',
        'Menyiapkan dashboard...',
        'Server management siap!'
      ]), 100);

      setTimeout(() => {

        fetch('api/get_servers.php?v=' + Date.now())

          .then(response => {
            console.log('Get servers response status:', response.status);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })

          .then(servers => {
            console.log('Servers data received:', servers);
            
            // Handle error response
            if (servers.error) {
              throw new Error(servers.error);
            }

            let serverManagementHTML = `
              <!-- Header Section -->
              <div style="
                background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                border: 1px solid rgba(79, 195, 247, 0.2);
                border-radius: 20px;
                padding: 25px;
                margin-bottom: 30px;
                text-align: center;
              ">
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
                  ">
                    <i class="bi bi-hdd-network-fill" style="color: white; font-size: 1.8rem;"></i>
                  </div>
                  <div>
                    <h2 style="color: #4fc3f7; margin: 0; font-weight: 700; font-size: 1.8rem;">
                      Server Management
                    </h2>
                    <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                      Kelola semua server hosting dan layanan
                    </p>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                  <div style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 12px 20px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  ">
                    <i class="bi bi-info-circle" style="color: #4fc3f7;"></i>
                    <span style="color: #e0e0e0; font-size: 0.9rem;">
                      Total: <strong style="color: #4fc3f7;" id="server-count">${servers.length}</strong> server
                    </span>
                  </div>
                </div>
              </div>

              <!-- Search and Filter Section -->
              <div style="
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 25px;
              ">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                  <i class="bi bi-search" style="color: #4fc3f7; font-size: 1.2rem;"></i>
                  <h4 style="color: #4fc3f7; margin: 0; font-weight: 600;">Search & Filter Servers</h4>
                </div>
                
                <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                  <!-- Search Input -->
                  <div style="flex: 1; min-width: 250px;">
                    <div style="position: relative;">
                      <input type="text" id="server-search-input" placeholder="Cari nama server, pemilik, IP..." style="
                        width: 100%;
                        background: rgba(255, 255, 255, 0.08);
                        border: 1px solid rgba(255, 255, 255, 0.15);
                        border-radius: 12px;
                        padding: 12px 45px 12px 15px;
                        color: #ffffff;
                        font-size: 0.9rem;
                        transition: all 0.3s ease;
                        box-sizing: border-box;
                      " onkeyup="filterServersBySearch()" onfocus="this.style.borderColor='#4fc3f7'; this.style.background='rgba(255, 255, 255, 0.12)';" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.background='rgba(255, 255, 255, 0.08)';">
                      <i class="bi bi-search" style="
                        position: absolute;
                        top: 50%;
                        right: 15px;
                        transform: translateY(-50%);
                        color: #b0b0b0;
                        font-size: 0.9rem;
                      "></i>
                    </div>
                  </div>
                  
                  <!-- Clear Button -->
                  <button onclick="clearServerFilters()" style="
                    background: rgba(255, 68, 68, 0.1);
                    border: 1px solid rgba(255, 68, 68, 0.3);
                    color: #ff4444;
                    padding: 10px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(255, 68, 68, 0.2)'; this.style.borderColor='#ff4444';" 
                     onmouseout="this.style.background='rgba(255, 68, 68, 0.1)'; this.style.borderColor='rgba(255, 68, 68, 0.3)';">
                    <i class="bi bi-x-circle"></i>
                    Clear Search
                  </button>
                </div>
              </div>

              <!-- Filter Buttons -->
              <div style="
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 25px;
              ">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                  <i class="bi bi-funnel" style="color: #4fc3f7; font-size: 1.2rem;"></i>
                  <h4 style="color: #4fc3f7; margin: 0; font-weight: 600;">Filter Server</h4>
                </div>
                
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                  <button id="filter-all" onclick="filterServersEnhanced('ALL', 'filter-all')" style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border: none;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(79, 195, 247, 0.4)';" 
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <i class="bi bi-grid-3x3-gap"></i>
                    Show All
                  </button>
                  
                  <button id="filter-fivem" onclick="filterServersEnhanced('FiveM', 'filter-fivem')" style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #e0e0e0;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7'; this.style.color='#4fc3f7';" 
                     onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                    <i class="bi bi-controller"></i>
                    FiveM
                  </button>
                  
                  <button id="filter-samp" onclick="filterServersEnhanced('Samp', 'filter-samp')" style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #e0e0e0;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7'; this.style.color='#4fc3f7';" 
                     onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                    <i class="bi bi-joystick"></i>
                    SAMP
                  </button>
                  
                  <button id="filter-redm" onclick="filterServersEnhanced('RedM', 'filter-redm')" style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #e0e0e0;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7'; this.style.color='#4fc3f7';" 
                     onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                    <i class="bi bi-cpu"></i>
                    RedM
                  </button>
                  
                  <button id="filter-vps" onclick="filterServersEnhanced('VPS', 'filter-vps')" style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #e0e0e0;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7'; this.style.color='#4fc3f7';" 
                     onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                    <i class="bi bi-server"></i>
                    VPS
                  </button>
                  
                  <button id="filter-pma" onclick="filterServersEnhanced('PMA', 'filter-pma')" style="
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #e0e0e0;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.85rem;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7'; this.style.color='#4fc3f7';" 
                     onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.color='#e0e0e0';">
                    <i class="bi bi-database"></i>
                    PMA
                  </button>
                </div>
              </div>

              <!-- Servers Grid -->
              <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 25px;" id="server-grid">
            `;

            if (servers.length === 0) {
              serverManagementHTML += `
                <div style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                  border-radius: 20px;
                  padding: 60px 40px;
                  text-align: center;
                  grid-column: 1 / -1;
                ">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 80px;
                    height: 80px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 20px;
                    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
                  ">
                    <i class="bi bi-hdd-network" style="color: white; font-size: 2rem;"></i>
                  </div>
                  <h4 style="color: #4fc3f7; margin-bottom: 10px; font-weight: 600;">Tidak Ada Server</h4>
                  <p style="color: #b0b0b0; margin: 0; font-size: 0.9rem;">
                    Belum ada data server hosting. Silakan tambahkan data ke tabel data_client.
                  </p>
                </div>
              `;
            } else {
              
              
              servers.forEach((server, index) => {
                // console.log(`Rendering server ${index + 1}:`, {
                //   id: server.id,
                //   type_game: server.type_game,
                //   nama_server: server.nama_server,
                //   pemilik: server.pemilik
                // });
                
                const statusClass = server.status === 'Active' ? 'status-active' : 
                                   server.status === 'Expiring Soon' ? 'status-expiring' : 'status-expired';
                
                // Ensure type_game is properly set
                const typeGame = server.type_game || 'UNKNOWN';
                const typeClass = typeGame.toLowerCase().includes('vps') ? 'status-vps' :
                                 typeGame.toLowerCase().includes('pma') ? 'status-pma' :
                                 typeGame.toLowerCase().includes('fivem') ? 'status-fivem' :
                                 typeGame.toLowerCase().includes('redm') ? 'status-redm' : 'status-vps';
                
                // console.log(`Server ${index + 1} type_game: "${typeGame}"`);
                
                // Type-specific colors and icons
                const typeConfig = {
                  'FiveM': { color: '#4caf50', bg: 'rgba(76, 175, 80, 0.2)', icon: 'bi-controller' },
                  'Samp': { color: '#ff9800', bg: 'rgba(255, 152, 0, 0.2)', icon: 'bi-joystick' },
                  'RedM': { color: '#e91e63', bg: 'rgba(233, 30, 99, 0.2)', icon: 'bi-cpu' },
                  'VPS': { color: '#2196f3', bg: 'rgba(33, 150, 243, 0.2)', icon: 'bi-server' },
                  'PMA': { color: '#9c27b0', bg: 'rgba(156, 39, 176, 0.2)', icon: 'bi-database' }
                };
                
                const config = typeConfig[typeGame] || { color: '#607d8b', bg: 'rgba(96, 125, 139, 0.2)', icon: 'bi-question-circle' };
                
                // Status-specific colors
                const statusConfig = {
                  'Active': { color: '#4caf50', bg: 'rgba(76, 175, 80, 0.2)' },
                  'Expiring Soon': { color: '#ff9800', bg: 'rgba(255, 152, 0, 0.2)' },
                  'Expired': { color: '#f44336', bg: 'rgba(244, 67, 54, 0.2)' }
                };
                
                const statusConf = statusConfig[server.status] || { color: '#607d8b', bg: 'rgba(96, 125, 139, 0.2)' };
                
                const expiredDate = server.expired ? new Date(server.expired).toLocaleDateString('id-ID', {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                }) : 'N/A';
                
                serverManagementHTML += `
                  <div class="server-row server-card" data-type-game="${typeGame}" data-server-name="${(server.nama_server || '').toLowerCase()}" data-owner="${(server.pemilik || '').toLowerCase()}" data-package="${(server.paket || '').toLowerCase()}" data-ip="${(server.ip || '').toLowerCase()}" data-status="${server.status || ''}" style="
                    background: rgba(255, 255, 255, 0.05);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 20px;
                    padding: 25px;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                  " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7';" 
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                    
                    <!-- Type Badge -->
                    <div style="position: absolute; top: 20px; right: 20px;">
                      <span style="
                        background: ${config.bg};
                        color: ${config.color};
                        border: 1px solid ${config.color};
                        padding: 6px 12px;
                        border-radius: 20px;
                        font-size: 0.7rem;
                        font-weight: 700;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                      ">
                        <i class="${config.icon}" style="font-size: 0.8rem;"></i>
                        ${typeGame}
                      </span>
                    </div>
                    
                    <!-- Server Header -->
                    <div style="margin-bottom: 20px; padding-right: 100px;">
                      <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                        <div style="
                          background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                          border-radius: 50%;
                          width: 45px;
                          height: 45px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
                        ">
                          <i class="bi bi-hdd-network" style="color: white; font-size: 1.2rem;"></i>
                      </div>
                        <div>
                          <h4 style="color: #4fc3f7; margin: 0; font-weight: 600; font-size: 1.1rem; word-break: break-all;">
                            ${server.nama_server || 'N/A'}
                          </h4>
                          <small style="color: #b0b0b0; font-size: 0.8rem;">
                            <i class="bi bi-person"></i> ${server.pemilik || 'N/A'}
                          </small>
                      </div>
                      </div>
                      </div>
                    
                    <!-- Server Details Grid -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                      <!-- Package -->
                      <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                        <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                          <i class="bi bi-box" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                          <strong style="color: #e0e0e0; font-size: 0.8rem;">Package</strong>
                      </div>
                        <div style="color: #fff; font-size: 0.85rem; word-break: break-all;">
                          ${server.paket || 'N/A'}
                      </div>
                    </div>
                    
                      <!-- Status -->
                      <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                        <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                          <i class="bi bi-activity" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                          <strong style="color: #e0e0e0; font-size: 0.8rem;">Status</strong>
                        </div>
                        <span style="
                          background: ${statusConf.bg};
                          color: ${statusConf.color};
                          border: 1px solid ${statusConf.color};
                          padding: 4px 8px;
                          border-radius: 12px;
                          font-size: 0.7rem;
                          font-weight: 600;
                        ">${server.status || 'Unknown'}</span>
                    </div>
                  </div>

                    <!-- IP Address -->
                    <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 15px; margin-bottom: 15px;">
                      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <i class="bi bi-globe" style="color: #4fc3f7; font-size: 1rem;"></i>
                        <strong style="color: #e0e0e0; font-size: 0.9rem;">IP Address</strong>
                      </div>
                      <div style="
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 8px;
                        padding: 10px;
                        font-family: 'Courier New', monospace;
                        color: #fff;
                        font-size: 0.85rem;
                        border-left: 3px solid #4fc3f7;
                      ">
                        ${server.ip_server || 'N/A'}
                      </div>
                    </div>
                    
                    <!-- Expiry & Price -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                      <!-- Expiry -->
                      <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                        <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                          <i class="bi bi-calendar-x" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                          <strong style="color: #e0e0e0; font-size: 0.8rem;">Expired</strong>
                        </div>
                        <div style="color: #fff; font-size: 0.85rem;">
                          ${expiredDate}
                        </div>
                      </div>
                      
                      <!-- Price -->
                      <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                        <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                          <i class="bi bi-currency-dollar" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                          <strong style="color: #e0e0e0; font-size: 0.8rem;">Price</strong>
                        </div>
                        <div style="color: #4caf50; font-size: 0.85rem; font-weight: 600;">
                          Rp ${server.harga_bulanan}
                        </div>
                      </div>
                    </div>
                    
                    <!-- Actions -->
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                      <button onclick="editServer('${server.id}')" style="
                        background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                        border: none;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 8px;
                        font-weight: 500;
                        font-size: 0.8rem;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        cursor: pointer;
                        flex: 1;
                        justify-content: center;
                        min-width: 120px;
                      " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(79, 195, 247, 0.4)';" 
                         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                        <i class="bi bi-pencil"></i>
                        Edit Server
                      </button>
                      
                      <button onclick="deleteServer('${server.id}')" style="
                        background: linear-gradient(45deg, #f44336, #d32f2f);
                        border: none;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 8px;
                        font-weight: 500;
                        font-size: 0.8rem;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        cursor: pointer;
                        flex: 1;
                        justify-content: center;
                        min-width: 120px;
                      " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(244, 67, 54, 0.4)';" 
                         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                        <i class="bi bi-trash3"></i>
                        Delete Server
                      </button>
                    </div>
                  </div>
                `;

              });
              
            }

            serverManagementHTML += `
              </div>

              <!-- Pagination for Servers -->
              <div id="servers-pagination" style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                margin-top: 30px;
                padding: 20px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
              ">
                <!-- Pagination will be generated by JavaScript -->
              </div>
            `;

            // Initialize pagination variables for servers
            window.currentServerPage = 1;
            window.serversPerPage = 4;
            window.filteredServers = servers;

            detailsContent.innerHTML = serverManagementHTML;

            // Store servers data globally for filtering
            window.allServers = servers;
            
            // Render servers with pagination
            window.renderServerCards = function() {
              const startIndex = (window.currentServerPage - 1) * window.serversPerPage;
              const endIndex = startIndex + window.serversPerPage;
              const serversToShow = window.filteredServers.slice(startIndex, endIndex);
              
              let serverCardsHTML = '';
              
              if (serversToShow.length === 0) {
                serverCardsHTML = `
                  <div style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 20px;
                    padding: 60px 40px;
                    text-align: center;
                    grid-column: 1 / -1;
                  ">
                    <div style="
                      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                      border-radius: 50%;
                      width: 80px;
                      height: 80px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 20px;
                      box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
                    ">
                      <i class="bi bi-hdd-network" style="color: white; font-size: 2rem;"></i>
                    </div>
                    <h4 style="color: #4fc3f7; margin-bottom: 10px; font-weight: 600;">Tidak Ada Server</h4>
                    <p style="color: #b0b0b0; margin: 0; font-size: 0.9rem;">
                      Tidak ada server yang sesuai dengan filter atau pencarian.
                    </p>
                  </div>
                `;
              } else {
                serversToShow.forEach((server, index) => {
                  const statusClass = server.status === 'Active' ? 'status-active' : 
                                     server.status === 'Expiring Soon' ? 'status-expiring' : 'status-expired';
                  
                  const typeGame = server.type_game || 'UNKNOWN';
                  
                  const typeConfig = {
                    'FiveM': { color: '#4caf50', bg: 'rgba(76, 175, 80, 0.2)', icon: 'bi-controller' },
                    'Samp': { color: '#ff9800', bg: 'rgba(255, 152, 0, 0.2)', icon: 'bi-joystick' },
                    'RedM': { color: '#e91e63', bg: 'rgba(233, 30, 99, 0.2)', icon: 'bi-cpu' },
                    'VPS': { color: '#2196f3', bg: 'rgba(33, 150, 243, 0.2)', icon: 'bi-server' },
                    'PMA': { color: '#9c27b0', bg: 'rgba(156, 39, 176, 0.2)', icon: 'bi-database' }
                  };
                  
                  const config = typeConfig[typeGame] || { color: '#607d8b', bg: 'rgba(96, 125, 139, 0.2)', icon: 'bi-question-circle' };
                  
                  const statusConfig = {
                    'Active': { color: '#4caf50', bg: 'rgba(76, 175, 80, 0.2)' },
                    'Expiring Soon': { color: '#ff9800', bg: 'rgba(255, 152, 0, 0.2)' },
                    'Expired': { color: '#f44336', bg: 'rgba(244, 67, 54, 0.2)' }
                  };
                  
                  const statusConf = statusConfig[server.status] || { color: '#607d8b', bg: 'rgba(96, 125, 139, 0.2)' };
                  
                  const expiredDate = server.expired ? new Date(server.expired).toLocaleDateString('id-ID', {
                    weekday: 'short',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  }) : 'N/A';
                  
                  serverCardsHTML += `
                    <div class="server-row server-card" data-type-game="${typeGame}" data-server-name="${(server.nama_server || '').toLowerCase()}" data-owner="${(server.pemilik || '').toLowerCase()}" data-package="${(server.paket || '').toLowerCase()}" data-ip="${(server.ip || '').toLowerCase()}" data-status="${server.status || ''}" style="
                      background: rgba(255, 255, 255, 0.05);
                      backdrop-filter: blur(10px);
                      border: 1px solid rgba(255, 255, 255, 0.1);
                      border-radius: 20px;
                      padding: 25px;
                      transition: all 0.3s ease;
                      position: relative;
                      overflow: hidden;
                    " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7';" 
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                      
                      <!-- Type Badge -->
                      <div style="position: absolute; top: 20px; right: 20px;">
                        <span style="
                          background: ${config.bg};
                          color: ${config.color};
                          border: 1px solid ${config.color};
                          padding: 6px 12px;
                          border-radius: 20px;
                          font-size: 0.7rem;
                          font-weight: 700;
                          text-transform: uppercase;
                          letter-spacing: 0.5px;
                          display: flex;
                          align-items: center;
                          gap: 4px;
                        ">
                          <i class="${config.icon}" style="font-size: 0.8rem;"></i>
                          ${typeGame}
                        </span>
                      </div>
                      
                      <!-- Server Header -->
                      <div style="margin-bottom: 20px; padding-right: 100px;">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                          <div style="
                            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                            border-radius: 50%;
                            width: 45px;
                            height: 45px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
                          ">
                            <i class="bi bi-hdd-network" style="color: white; font-size: 1.2rem;"></i>
                        </div>
                          <div>
                            <h4 style="color: #4fc3f7; margin: 0; font-weight: 600; font-size: 1.1rem; word-break: break-all;">
                              ${server.nama_server || 'N/A'}
                            </h4>
                            <small style="color: #b0b0b0; font-size: 0.8rem;">
                              <i class="bi bi-person"></i> ${server.pemilik || 'N/A'}
                            </small>
                        </div>
                        </div>
                      </div>
                      
                      <!-- Server Details Grid -->
                      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <!-- Package -->
                        <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                          <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                            <i class="bi bi-box" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                            <strong style="color: #e0e0e0; font-size: 0.8rem;">Package</strong>
                        </div>
                          <div style="color: #fff; font-size: 0.85rem; word-break: break-all;">
                            ${server.paket || 'N/A'}
                        </div>
                      </div>
                      
                        <!-- Status -->
                        <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                          <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                            <i class="bi bi-activity" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                            <strong style="color: #e0e0e0; font-size: 0.8rem;">Status</strong>
                          </div>
                          <span style="
                            background: ${statusConf.bg};
                            color: ${statusConf.color};
                            border: 1px solid ${statusConf.color};
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.7rem;
                            font-weight: 600;
                          ">${server.status || 'Unknown'}</span>
                      </div>
                    </div>

                      <!-- IP Address -->
                      <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 15px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                          <i class="bi bi-globe" style="color: #4fc3f7; font-size: 1rem;"></i>
                          <strong style="color: #e0e0e0; font-size: 0.9rem;">IP Address</strong>
                        </div>
                        <div style="
                          background: rgba(255, 255, 255, 0.05);
                          border-radius: 8px;
                          padding: 10px;
                          font-family: 'Courier New', monospace;
                          color: #fff;
                          font-size: 0.85rem;
                          border-left: 3px solid #4fc3f7;
                        ">
                          ${server.ip_server || 'N/A'}
                        </div>
                      </div>
                      
                      <!-- Expiry & Price -->
                      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <!-- Expiry -->
                        <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                          <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                            <i class="bi bi-calendar-x" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                            <strong style="color: #e0e0e0; font-size: 0.8rem;">Expired</strong>
                          </div>
                          <div style="color: #fff; font-size: 0.85rem;">
                            ${expiredDate}
                          </div>
                        </div>
                        
                        <!-- Price -->
                        <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 12px;">
                          <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 6px;">
                            <i class="bi bi-currency-dollar" style="color: #4fc3f7; font-size: 0.9rem;"></i>
                            <strong style="color: #e0e0e0; font-size: 0.8rem;">Price</strong>
                          </div>
                          <div style="color: #4caf50; font-size: 0.85rem; font-weight: 600;">
                            Rp ${server.harga_bulanan}
                          </div>
                        </div>
                      </div>
                      
                      <!-- Actions -->
                      <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button onclick="editServer('${server.id}')" style="
                          background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                          border: none;
                          color: white;
                          padding: 8px 16px;
                          border-radius: 8px;
                          font-weight: 500;
                          font-size: 0.8rem;
                          transition: all 0.3s ease;
                          display: flex;
                          align-items: center;
                          gap: 6px;
                          cursor: pointer;
                          flex: 1;
                          justify-content: center;
                          min-width: 120px;
                        " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(79, 195, 247, 0.4)';" 
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                          <i class="bi bi-pencil"></i>
                          Edit Server
                        </button>
                        
                        <button onclick="deleteServer('${server.id}')" style="
                          background: linear-gradient(45deg, #f44336, #d32f2f);
                          border: none;
                          color: white;
                          padding: 8px 16px;
                          border-radius: 8px;
                          font-weight: 500;
                          font-size: 0.8rem;
                          transition: all 0.3s ease;
                          display: flex;
                          align-items: center;
                          gap: 6px;
                          cursor: pointer;
                          flex: 1;
                          justify-content: center;
                          min-width: 120px;
                        " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(244, 67, 54, 0.4)';" 
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                          <i class="bi bi-trash3"></i>
                          Delete Server
                        </button>
                      </div>
                    </div>
                  `;
                });
              }
              
              document.getElementById('server-grid').innerHTML = serverCardsHTML;
              window.renderServerPagination();
            }
            
            // Render pagination for servers
            window.renderServerPagination = function() {
              const totalPages = Math.ceil(window.filteredServers.length / window.serversPerPage);
              const paginationContainer = document.getElementById('servers-pagination');
              
              if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
              }
              
              paginationContainer.style.display = 'flex';
              
              let paginationHTML = `
                <button onclick="changeServerPage(${window.currentServerPage - 1})" 
                        ${window.currentServerPage === 1 ? 'disabled' : ''} 
                        style="
                          background: ${window.currentServerPage === 1 ? 'rgba(255, 255, 255, 0.1)' : 'linear-gradient(45deg, #4fc3f7, #29b6f6)'};
                          border: none;
                          color: ${window.currentServerPage === 1 ? '#666' : 'white'};
                          padding: 10px 15px;
                          border-radius: 8px;
                          font-weight: 500;
                          cursor: ${window.currentServerPage === 1 ? 'not-allowed' : 'pointer'};
                          transition: all 0.3s ease;
                        ">
                  <i class="bi bi-chevron-left"></i>
                </button>
              `;
              
              // Show page numbers
              for (let i = 1; i <= totalPages; i++) {
                const isActive = i === window.currentServerPage;
                paginationHTML += `
                  <button onclick="changeServerPage(${i})" style="
                    background: ${isActive ? 'linear-gradient(45deg, #4fc3f7, #29b6f6)' : 'rgba(255, 255, 255, 0.1)'};
                    border: ${isActive ? 'none' : '1px solid rgba(255, 255, 255, 0.2)'};
                    color: ${isActive ? 'white' : '#e0e0e0'};
                    padding: 10px 15px;
                    border-radius: 8px;
                    font-weight: ${isActive ? '600' : '500'};
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 45px;
                  " onmouseover="if (!${isActive}) { this.style.background='rgba(79, 195, 247, 0.2)'; this.style.color='#4fc3f7'; }" 
                     onmouseout="if (!${isActive}) { this.style.background='rgba(255, 255, 255, 0.1)'; this.style.color='#e0e0e0'; }">
                    ${i}
                  </button>
                `;
              }
              
              paginationHTML += `
                <button onclick="changeServerPage(${window.currentServerPage + 1})" 
                        ${window.currentServerPage === totalPages ? 'disabled' : ''} 
                        style="
                          background: ${window.currentServerPage === totalPages ? 'rgba(255, 255, 255, 0.1)' : 'linear-gradient(45deg, #4fc3f7, #29b6f6)'};
                          border: none;
                          color: ${window.currentServerPage === totalPages ? '#666' : 'white'};
                          padding: 10px 15px;
                          border-radius: 8px;
                          font-weight: 500;
                          cursor: ${window.currentServerPage === totalPages ? 'not-allowed' : 'pointer'};
                          transition: all 0.3s ease;
                        ">
                  <i class="bi bi-chevron-right"></i>
                </button>
              `;
              
              paginationContainer.innerHTML = paginationHTML;
            }
            
            // Change server page
            window.changeServerPage = function(page) {
              const totalPages = Math.ceil(window.filteredServers.length / window.serversPerPage);
              if (page >= 1 && page <= totalPages) {
                window.currentServerPage = page;
                renderServerCards();
              }
            }
            
            // Initial render of server cards with pagination
            window.renderServerCards();

            detailsContent.classList.remove('hidden');

            detailsContent.classList.add('visible');

          })

          .catch(error => {
            console.error('Servers API Error:', error);
            detailsContent.innerHTML = `<h3>Error loading servers</h3><p>Error: ${error.message}</p>`;
            detailsContent.classList.remove('hidden');
            detailsContent.classList.add('visible');
          });

      }, 500);

      



    }



    function filterServers(typeGame, buttonId) {
      

      const serverRows = document.querySelectorAll('.server-row');
      const serverGrid = document.getElementById('server-grid');

      if (serverRows.length === 0) {
        alert('No server data found. Please refresh the page or check if data exists.');
        return;
      }

      // Debug: Log all row data (commented out)
      // serverRows.forEach((row, index) => {
      //   const rowTypeGame = row.dataset.typeGame;
      //   const rowText = row.textContent.trim();
      //   console.log(`Row ${index + 1}:`, {
      //     typeGame: rowTypeGame,
      //     innerHTML: row.innerHTML.substring(0, 100) + '...',
      //     textContent: rowText.substring(0, 50) + '...'
      //   });
      // });

      serverGrid.classList.remove('fade-in');
      serverGrid.classList.add('fade-out');

      setTimeout(() => {
        let visibleCount = 0;
        let hiddenCount = 0;
        
        serverRows.forEach((row, index) => {
          const rowTypeGame = row.dataset.typeGame;
          console.log(`Processing row ${index + 1}: rowType="${rowTypeGame}", filter="${typeGame}"`);
          
          if (rowTypeGame === typeGame || typeGame === 'ALL') {
            row.style.display = '';
            visibleCount++;
            console.log(`✓ Row ${index + 1} VISIBLE (${rowTypeGame})`);
          } else {
            row.style.display = 'none';
            hiddenCount++;
            console.log(`✗ Row ${index + 1} HIDDEN (${rowTypeGame})`);
          }
        });

        console.log(`Filter result: ${visibleCount} visible, ${hiddenCount} hidden`);

        if (visibleCount === 0 && typeGame !== 'ALL') {
          console.log('No rows match the filter criteria');
          // Show a temporary message
          const messageCard = document.createElement('div');
          messageCard.id = 'no-results-message';
          messageCard.className = 'data-card';
          messageCard.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #ffc107;">
              <h4>No ${typeGame} servers found</h4>
              <p>Click "Show All" to see all servers.</p>
            </div>
          `;
          
          // Remove existing message if any
          const existingMessage = document.getElementById('no-results-message');
          if (existingMessage) {
            existingMessage.remove();
          }
          
          serverGrid.appendChild(messageCard);
        } else {
          // Remove no results message if it exists
          const existingMessage = document.getElementById('no-results-message');
          if (existingMessage) {
            existingMessage.remove();
          }
        }

        serverGrid.classList.remove('fade-out');
        serverGrid.classList.add('fade-in');
      }, 100); // Reduced timeout for better UX

      // Remove active class from all filter buttons
      document.querySelectorAll('button[id^="filter-"]').forEach(button => {
        button.style.backgroundColor = 'rgb(28, 113, 153)';
      });

      // Add active class to the clicked filter button
      if (document.getElementById(buttonId)) {
        document.getElementById(buttonId).style.backgroundColor = '#2a9fd6';
        console.log('Button activated:', buttonId);
      } else {
        console.log('Button not found:', buttonId);
      }
      
      
    }



    // Add CSS for fade effect

    const style = document.createElement('style');

    style.innerHTML = `

      .fade-out {

        opacity: 0;

        transition: opacity 0.5s;

      }

      .fade-in {

        opacity: 1;

        transition: opacity 0.5s;

      }

    `;

    document.head.appendChild(style);

    // Function to edit server
    function editServer(serverId) {
      console.log("Server ID: " + serverId);
      // Fetch server details
              fetch(`api/get_server_detail.php?id=${serverId}`)
        .then(response => response.json())
        .then(server => {
          // Remove any existing modal to prevent duplicates
          const existingModal = document.getElementById('editServerModal');
          if (existingModal) {
            existingModal.remove();
          }

          const modalHTML = `
            <style>
              @keyframes modalSlideIn {
                from {
                  opacity: 0;
                  transform: translateY(-50px) scale(0.9);
                }
                to {
                  opacity: 1;
                  transform: translateY(0) scale(1);
                }
              }
              
              @keyframes modalSlideOut {
                from {
                  opacity: 1;
                  transform: translateY(0) scale(1);
                }
                to {
                  opacity: 0;
                  transform: translateY(-50px) scale(0.9);
                }
              }
              
              #editServerModal input::placeholder,
              #editServerModal textarea::placeholder {
                color: rgba(255, 255, 255, 0.6) !important;
                opacity: 1 !important;
              }
              
              #editServerModal select {
                color: #fff !important;
              }
              
              #editServerModal select option {
                background: rgba(43, 48, 53, 0.95) !important;
                color: #fff !important;
              }
            </style>
            <div class="modal fade" id="editServerModal" tabindex="-1" aria-labelledby="editServerModalLabel" aria-hidden="true" style="
              transition: all 0.3s ease-in-out;
            ">
              <div class="modal-dialog modal-dialog-centered modal-lg" style="
                transition: transform 0.3s ease-out;
              ">
                <div class="modal-content" style="
                  background: rgba(43, 48, 53, 0.95);
                  backdrop-filter: blur(10px);
                  border: 1px solid rgba(79, 195, 247, 0.3);
                  border-radius: 15px;
                  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
                  animation: modalSlideIn 0.3s ease-out;
                ">
                  <div class="modal-header" style="
                    background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
                    border-radius: 15px 15px 0 0;
                    padding: 20px 25px;
                  ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <div style="
                        background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      ">
                        <i class="bi bi-pencil-square" style="color: white; font-size: 1.1rem;"></i>
                  </div>
                      <div>
                        <h5 style="color: #4fc3f7; margin: 0; font-weight: 600;">Edit Server</h5>
                        <small style="color: #b0b0b0;">Update server configuration</small>
                      </div>
                    </div>
                    <button type="button" class="btn-close" onclick="closeEditModal()" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      border-radius: 50%;
                      width: 35px;
                      height: 35px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: #fff;
                      position: relative;
                      cursor: pointer;
                      transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.transform='scale(1)'">
                      <i class="bi bi-x" style="font-size: 1.2rem; font-weight: bold;"></i>
                    </button>
                  </div>
                  
                  <div class="modal-body" style="padding: 25px;">
                    <form id="editServerForm">
                      <div class="row g-3">
                        <div class="col-md-6">
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-person"></i> Owner Email
                            </label>
                            <input type="email" id="serverOwner" class="form-control" value="${server.pemilik}" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-server"></i> Server Name
                            </label>
                            <input type="text" id="serverName" class="form-control" value="${server.nama_server}" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-gear"></i> Type Hosting
                            </label>
                            <select id="serverType" class="form-control" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                              <option value="FiveM" ${server.type_game === 'FiveM' ? 'selected' : ''}>FiveM</option>
                              <option value="Samp" ${server.type_game === 'Samp' ? 'selected' : ''}>SAMP</option>
                              <option value="RedM" ${server.type_game === 'RedM' ? 'selected' : ''}>RedM</option>
                              <option value="VPS" ${server.type_game === 'VPS' ? 'selected' : ''}>VPS</option>
                              <option value="PMA" ${server.type_game === 'PMA' ? 'selected' : ''}>PMA</option>
                            </select>
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-globe"></i> IP Address
                            </label>
                            <input type="text" id="serverIP" class="form-control" value="${server.ip_server || ''}" placeholder="e.g., ***********:30120" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              font-family: 'Courier New', monospace;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-calendar-x"></i> Expired Date
                            </label>
                            <input type="date" id="serverExpired" class="form-control" value="${server.expired ? server.expired.split(' ')[0] : ''}" required style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                        </div>
                        
                        <div class="col-md-6">
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-box"></i> Package
                            </label>
                            <input type="text" id="serverPaket" class="form-control" value="${server.paket || ''}" placeholder="e.g., FiveM 120 SLOT" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-tag"></i> Normal Price
                            </label>
                            <input type="number" id="serverHargaNormal" class="form-control" value="${server.harga_normal || 0}" min="0" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-currency-dollar"></i> Monthly Price
                            </label>
                            <input type="number" id="serverHargaBulanan" class="form-control" value="${server.harga_bulanan || 0}" min="0" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-key"></i> Token
                            </label>
                            <input type="text" id="serverToken" class="form-control" value="${server.token || ''}" placeholder="Optional" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              font-family: 'Courier New', monospace;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">
                          </div>
                          
                          <div class="form-group mb-3">
                            <label style="color: #4fc3f7; font-weight: 600; margin-bottom: 8px; display: block;">
                              <i class="bi bi-card-text"></i> Notes
                            </label>
                            <textarea id="serverCatatan" class="form-control" rows="2" placeholder="Optional notes" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid rgba(255, 255, 255, 0.1);
                              border-radius: 10px;
                              padding: 12px 15px;
                              color: #fff;
                              font-size: 0.9rem;
                              resize: vertical;
                              transition: all 0.3s ease;
                            " onfocus="this.style.borderColor='#4fc3f7'; this.style.boxShadow='0 0 0 2px rgba(79, 195, 247, 0.2)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.boxShadow='none'">${server.catatan || ''}</textarea>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                  
                  <div class="modal-footer" style="
                    background: rgba(255, 255, 255, 0.02);
                    border-top: 1px solid rgba(79, 195, 247, 0.2);
                    border-radius: 0 0 15px 15px;
                    padding: 20px 25px;
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                  ">
                    <button type="button" class="btn" onclick="closeEditModal()" style="
                      background: rgba(255, 255, 255, 0.1);
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      color: #e0e0e0;
                      padding: 10px 20px;
                      border-radius: 8px;
                      font-weight: 500;
                    ">Cancel</button>
                    <button type="button" class="btn" onclick="saveServerChanges('${serverId}')" style="
                      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                      border: none;
                      color: white;
                      padding: 10px 20px;
                      border-radius: 8px;
                      font-weight: 600;
                    ">Save Changes</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', modalHTML);
          $('#editServerModal').modal('show');
        })
        .catch(error => console.error('Error:', error));
    }

    function closeEditModal() {
      const modal = document.getElementById('editServerModal');
      if (modal) {
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
          modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
        }
        
        // Langsung remove tanpa memanggil Bootstrap modal hide
        setTimeout(() => {
          if (document.getElementById('editServerModal')) {
      document.getElementById('editServerModal').remove();
          }
          // Remove backdrop manually jika ada
          const backdrop = document.querySelector('.modal-backdrop');
          if (backdrop) {
            backdrop.remove();
          }
          // Force restore body scroll - selalu jalankan
          restoreBodyScroll();
        }, 300);
      } else {
        // Jika modal tidak ditemukan, tetap restore scroll
        restoreBodyScroll();
      }
    }
    
    // Function helper untuk restore scroll
    function restoreBodyScroll() {
      document.body.classList.remove('modal-open');
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
      document.documentElement.style.overflow = '';
      // Double check - hapus semua backdrop yang mungkin tertinggal
      document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
    }

    function saveServerChanges(serverId) {
      const serverOwner = document.getElementById('serverOwner').value;
      const serverName = document.getElementById('serverName').value;
      const serverType = document.getElementById('serverType').value;
      const serverIP = document.getElementById('serverIP').value;
      const serverExpired = document.getElementById('serverExpired').value;
      const serverPaket = document.getElementById('serverPaket').value;
      const serverHargaNormal = document.getElementById('serverHargaNormal').value;
      const serverHargaBulanan = document.getElementById('serverHargaBulanan').value;
      const serverToken = document.getElementById('serverToken').value;
      const serverCatatan = document.getElementById('serverCatatan').value;

      // Validate required fields - removed validation to allow empty fields
      // if (!serverOwner || !serverName || !serverType || !serverExpired) {
      //   alert('Please fill in all required fields (Owner, Server Name, Type, Expired Date)');
      //   return;
      // }

      // Format expired date to include time (only if not empty)
      const expiredDateTime = serverExpired ? serverExpired + ' 23:59:59' : '';

      console.log('Saving server data:', {
        id: serverId,
        pemilik: serverOwner,
        nama_server: serverName,
        type_game: serverType,
        ip_server: serverIP,
        expired: expiredDateTime,
        paket: serverPaket,
        harga_normal: parseFloat(serverHargaNormal) || 0,
        harga_bulanan: parseFloat(serverHargaBulanan) || 0,
        token: serverToken,
        catatan: serverCatatan
      });

      fetch('api/update_server.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: serverId,
          pemilik: serverOwner,
          nama_server: serverName,
          type_game: serverType,
          ip_server: serverIP,
          expired: expiredDateTime,
          paket: serverPaket,
          harga_normal: parseFloat(serverHargaNormal) || 0,
          harga_bulanan: parseFloat(serverHargaBulanan) || 0,
          token: serverToken,
          catatan: serverCatatan
        })
      })
      .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        console.log('Response URL:', response.url);
        
        if (!response.ok) {
          return response.text().then(text => {
            console.log('Error response body:', text);
            throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('Server response:', data);
        if (data.success === true) {
          alert('Server details updated successfully.');
                    const modal = document.getElementById('editServerModal');
          if (modal) {
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
            }
            
            // Langsung remove tanpa memanggil Bootstrap modal hide
            setTimeout(() => {
              if (document.getElementById('editServerModal')) {
          document.getElementById('editServerModal').remove();
              }
              // Force restore body scroll - selalu jalankan
              restoreBodyScroll();
            }, 300);
          } else {
            // Jika modal tidak ditemukan, tetap restore scroll
            restoreBodyScroll();
          }
          showServerManagement(); // Refresh the server management view
        } else {
          alert('Failed to update server details: ' + (data.message || 'Unknown error'));
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving: ' + error.message);
      });
    }

    // Function to close edit order modal
    function closeEditOrderModal() {
      const modal = document.getElementById('editOrderModal');
      if (modal) {
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
          modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
        }
        
        // Langsung remove tanpa memanggil Bootstrap modal hide
        setTimeout(() => {
          if (document.getElementById('editOrderModal')) {
            document.getElementById('editOrderModal').remove();
          }
          // Remove backdrop manually jika ada
          const backdrop = document.querySelector('.modal-backdrop');
          if (backdrop) {
            backdrop.remove();
          }
          // Force restore body scroll - selalu jalankan
          restoreBodyScroll();
        }, 300);
      } else {
        // Jika modal tidak ditemukan, tetap restore scroll
        restoreBodyScroll();
      }
    }

    // Function to save order changes
    function saveOrderChanges(orderId) {
      const orderEmail = document.getElementById('orderEmail').value;
      const orderServerName = document.getElementById('orderNamaServer').value;
      const orderPackage = document.getElementById('orderPaket').value;
      const orderPaymentStatus = document.getElementById('orderStatusBayar').value;
      const orderPaymentMethod = document.getElementById('orderPembayaran').value;
      const orderAmount = document.getElementById('orderJumlahBayar').value;

      console.log('Saving order data:', {
        id: orderId,
        email: orderEmail,
        nama_server: orderServerName,
        paket: orderPackage,
        status_bayar: orderPaymentStatus,
        pembayaran: orderPaymentMethod,
        jumlah_bayar: parseFloat(orderAmount) || 0
      });

      fetch('api/update_order.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          order_id: orderId,
          email: orderEmail,
          nama_server: orderServerName,
          paket: orderPackage,
          status_bayar: orderPaymentStatus,
          pembayaran: orderPaymentMethod,
          jumlah_bayar: parseFloat(orderAmount) || 0
        })
      })
      .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        console.log('Response URL:', response.url);
        
        if (!response.ok) {
          return response.text().then(text => {
            console.log('Error response body:', text);
            throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('Order response:', data);
        if (data.success === true) {
          Swal.fire({
            title: 'Berhasil!',
            text: 'Detail order berhasil diperbarui.',
            icon: 'success',
            confirmButtonColor: '#28a745',
            background: 'rgba(13, 18, 26, 0.95)',
            color: '#fff',
            customClass: {
              popup: 'swal2-popup animated fadeInDown'
            }
          });
          
          const modal = document.getElementById('editOrderModal');
          if (modal) {
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.animation = 'modalSlideOut 0.3s ease-in';
            }
            
            // Langsung remove tanpa memanggil Bootstrap modal hide
            setTimeout(() => {
              if (document.getElementById('editOrderModal')) {
                document.getElementById('editOrderModal').remove();
              }
              // Force restore body scroll - selalu jalankan
              restoreBodyScroll();
            }, 300);
          } else {
            // Jika modal tidak ditemukan, tetap restore scroll
            restoreBodyScroll();
          }
          
          // Refresh the order management view
          showOrderManagement();
        } else {
          Swal.fire({
            title: 'Error!',
            text: 'Gagal memperbarui detail order: ' + (data.message || 'Unknown error'),
            icon: 'error',
            confirmButtonColor: '#d33',
            background: 'rgba(13, 18, 26, 0.95)',
            color: '#fff',
            customClass: {
              popup: 'swal2-popup animated fadeInDown'
            }
          });
        }
      })
      .catch(error => {
        console.error('Error:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Terjadi kesalahan saat menyimpan: ' + error.message,
          icon: 'error',
          confirmButtonColor: '#d33',
          background: 'rgba(13, 18, 26, 0.95)',
          color: '#fff',
          customClass: {
            popup: 'swal2-popup animated fadeInDown'
          }
        });
      });
    }

    // Function to delete server
    function deleteServer(serverId) {
      if (!serverId) {
        Swal.fire({
          title: 'Error!',
          text: 'ID Server tidak ditemukan.',
          icon: 'error',
          confirmButtonColor: '#d33',
          background: 'rgba(13, 18, 26, 0.95)',
          color: '#fff'
        });
        return;
      }

      showDeleteConfirmation(
        'Hapus Server?',
        'Apakah Anda yakin ingin menghapus server ini? Tindakan ini tidak dapat dibatalkan.',
        function() {
          fetch('api/delete_server.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ server_id: serverId })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                title: 'Berhasil!',
                text: 'Server telah dihapus.',
                icon: 'success',
                background: 'rgba(13, 18, 26, 0.95)',
                color: '#fff',
                showConfirmButton: false,
                timer: 1500
              }).then(() => {
                location.reload();
              });
            } else {
              Swal.fire({
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan saat menghapus server.',
                icon: 'error',
                background: 'rgba(13, 18, 26, 0.95)',
                color: '#fff'
              });
            }
          })
          .catch(error => {
            console.error('Error:', error);
            Swal.fire({
              title: 'Error!',
              text: 'Terjadi kesalahan saat menghubungi server.',
              icon: 'error',
              background: 'rgba(13, 18, 26, 0.95)',
              color: '#fff'
            });
          });
        }
      );
    }

    // Pastikan data-server-id ada saat tombol delete diklik
    document.addEventListener('DOMContentLoaded', function() {
      const deleteButtons = document.querySelectorAll('[onclick*="deleteServer"]');
      deleteButtons.forEach(button => {
        const serverId = button.getAttribute('data-server-id');
        button.onclick = (e) => {
          e.preventDefault();
          if (!serverId) {
            console.error('Server ID not found on button:', button);
          }
          deleteServer(serverId);
        };
      });
    });

    //////////////////////////////////////////////////////////////////////////////





   



    function showEmailApprovals() {
      // Stop QRIS auto-refresh when leaving QRIS page
      handleMenuChange();
      
      // Save active menu state
      localStorage.setItem('activeMenu', 'email-approvals');
      setActiveMenu('email-approvals-card');
      
      const detailsContent = document.getElementById('details-content');
      detailsContent.classList.remove('visible');
      detailsContent.classList.add('hidden');

      // Show loading immediately
      detailsContent.innerHTML = showLoadingWithProgress(
        'Loading Email Approvals',
        'Mengambil data persetujuan email...',
        '#ff9800'
      );
      
      detailsContent.classList.remove('hidden');
      detailsContent.classList.add('visible');
      
      // Start progress animation with email-specific messages
      setTimeout(() => animateProgress(1500, [
        'Mengakses sistem email...',
        'Mengambil data persetujuan...',
        'Memfilter permintaan aktif...',
        'Menyiapkan form persetujuan...',
        'Email approvals siap!'
      ]), 100);

      // Fetch email approvals data
      setTimeout(() => {
      fetch('api/get_email_approvals.php')
        .then(response => response.json())
        .then(data => {
          setTimeout(() => {
            let approvalCards = '';
            
            if (data.requests && data.requests.length > 0) {
              data.requests.forEach(request => {
                const statusClass = request.status === 'pending' ? 'status-pending' : 
                                   request.status === 'approved' ? 'status-approved' : 'status-rejected';
                const statusText = request.status === 'pending' ? 'MENUNGGU' : 
                                  request.status === 'approved' ? 'DISETUJUI' : 'DITOLAK';
                
                approvalCards += `
                  <div class="email-request-card" style="
                    background: rgba(255, 255, 255, 0.05);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 20px;
                    padding: 25px;
                    margin-bottom: 25px;
                    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                  " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7';" 
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                    
                    <!-- Status Badge -->
                    <div style="position: absolute; top: 20px; right: 20px;">
                      <span class="status-badge ${statusClass}" style="
                        padding: 8px 16px;
                        border-radius: 25px;
                        font-size: 0.75rem;
                        font-weight: 700;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        ${request.status === 'pending' ? 'background: rgba(255, 152, 0, 0.2); color: #ff9800; border: 1px solid #ff9800;' : ''}
                        ${request.status === 'approved' ? 'background: rgba(76, 175, 80, 0.2); color: #4caf50; border: 1px solid #4caf50;' : ''}
                        ${request.status === 'rejected' ? 'background: rgba(244, 67, 54, 0.2); color: #f44336; border: 1px solid #f44336;' : ''}
                      ">${statusText}</span>
                    </div>
                    
                    <!-- Header -->
                    <div style="margin-bottom: 20px; padding-right: 120px;">
                      <h4 style="color: #4fc3f7; margin-bottom: 8px; font-weight: 600; display: flex; align-items: center; gap: 10px;">
                        <i class="bi bi-envelope-paper" style="font-size: 1.2rem;"></i>
                        Email Change Request #${request.id}
                      </h4>
                      <p style="color: #b0b0b0; margin: 0; font-size: 0.9rem;">
                        <i class="bi bi-calendar3"></i> ${new Date(request.request_date).toLocaleDateString('id-ID', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    
                    <!-- Email Change Details -->
                    <div class="row mb-4">
                      <div class="col-md-6">
                        <div style="background: rgba(255, 255, 255, 0.03); border-radius: 12px; padding: 15px; margin-bottom: 15px;">
                          <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <i class="bi bi-person-circle" style="color: #4fc3f7; font-size: 1.1rem;"></i>
                            <strong style="color: #e0e0e0;">User Account</strong>
                          </div>
                          <div style="color: #fff; font-size: 0.95rem; word-break: break-all;">
                            ${request.user_email}
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        ${request.reason ? `
                          <div style="background: rgba(79, 195, 247, 0.1); border-radius: 12px; padding: 15px; border-left: 4px solid #4fc3f7;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                              <i class="bi bi-chat-quote" style="color: #4fc3f7; font-size: 1.1rem;"></i>
                              <strong style="color: #4fc3f7;">Alasan Perubahan</strong>
                            </div>
                            <div style="color: #fff; font-size: 0.9rem; line-height: 1.4;">
                              "${request.reason}"
                            </div>
                          </div>
                        ` : ''}
                      </div>
                    </div>
                    
                    <!-- Email Transition -->
                    <div style="background: rgba(255, 255, 255, 0.03); border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                      <div class="row align-items-center">
                        <div class="col-md-5">
                          <div style="text-align: center; padding: 15px; background: rgba(244, 67, 54, 0.1); border-radius: 10px; border: 1px solid rgba(244, 67, 54, 0.3);">
                            <div style="color: #f44336; font-size: 0.8rem; font-weight: 600; text-transform: uppercase; margin-bottom: 5px;">Email Lama</div>
                            <div style="color: #fff; font-weight: 500; word-break: break-all;">${request.old_email}</div>
                          </div>
                        </div>
                        <div class="col-md-2 text-center">
                          <i class="bi bi-arrow-right-circle-fill" style="color: #4fc3f7; font-size: 2rem;"></i>
                        </div>
                        <div class="col-md-5">
                          <div style="text-align: center; padding: 15px; background: rgba(76, 175, 80, 0.1); border-radius: 10px; border: 1px solid rgba(76, 175, 80, 0.3);">
                            <div style="color: #4caf50; font-size: 0.8rem; font-weight: 600; text-transform: uppercase; margin-bottom: 5px;">Email Baru</div>
                            <div style="color: #fff; font-weight: 500; word-break: break-all;">${request.new_email}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    ${request.status === 'pending' ? `
                      <!-- Action Buttons -->
                      <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                        <button onclick="approveEmailRequest(${request.id})" style="
                          background: linear-gradient(45deg, #4caf50, #45a049);
                          border: none;
                          color: white;
                          padding: 12px 24px;
                          border-radius: 12px;
                          font-weight: 600;
                          font-size: 0.9rem;
                          transition: all 0.3s ease;
                          display: flex;
                          align-items: center;
                          gap: 8px;
                          cursor: pointer;
                          box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                          flex: 1;
                          min-width: 160px;
                          justify-content: center;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(76, 175, 80, 0.4)';" 
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(76, 175, 80, 0.3)';">
                          <i class="bi bi-check-circle-fill"></i>
                          Setujui Perubahan
                        </button>
                        
                        <button onclick="rejectEmailRequest(${request.id})" style="
                          background: linear-gradient(45deg, #f44336, #d32f2f);
                          border: none;
                          color: white;
                          padding: 12px 24px;
                          border-radius: 12px;
                          font-weight: 600;
                          font-size: 0.9rem;
                          transition: all 0.3s ease;
                          display: flex;
                          align-items: center;
                          gap: 8px;
                          cursor: pointer;
                          box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
                          flex: 1;
                          min-width: 160px;
                          justify-content: center;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(244, 67, 54, 0.4)';" 
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(244, 67, 54, 0.3)';">
                          <i class="bi bi-x-circle-fill"></i>
                          Tolak Perubahan
                        </button>
                      </div>
                      
                      <!-- Approve Form -->
                      <div id="approve-${request.id}" style="margin-top: 15px; display: none;">
                        <div style="
                          background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
                          border: 2px solid rgba(76, 175, 80, 0.3);
                          border-radius: 15px;
                          padding: 20px;
                          backdrop-filter: blur(5px);
                        ">
                          <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                            <i class="bi bi-check-circle-fill" style="color: #4caf50; font-size: 1.3rem;"></i>
                            <h6 style="color: #4caf50; margin: 0; font-weight: 600; font-size: 1.1rem;">Setujui Perubahan Email</h6>
                          </div>
                          
                          <div style="margin-bottom: 15px;">
                            <label style="color: #e0e0e0; font-weight: 500; margin-bottom: 8px; display: block;">
                              Catatan Admin (Opsional)
                            </label>
                            <textarea id="approve-notes-${request.id}" 
                                      placeholder="Tambahkan catatan persetujuan..." 
                                      style="
                                        width: 100%;
                                        padding: 12px;
                                        border-radius: 10px;
                                        background: rgba(255,255,255,0.1);
                                        border: 1px solid rgba(255,255,255,0.2);
                                        color: white;
                                        font-size: 0.9rem;
                                        resize: vertical;
                                        min-height: 60px;
                                      " rows="2"></textarea>
                          </div>
                          
                          <div style="display: flex; gap: 10px;">
                            <button onclick="confirmAction(${request.id}, 'approve')" style="
                              background: linear-gradient(45deg, #4caf50, #45a049);
                              border: none;
                              color: white;
                              padding: 10px 20px;
                              border-radius: 8px;
                              font-weight: 600;
                              cursor: pointer;
                              transition: all 0.2s ease;
                            ">
                              <i class="bi bi-check-lg"></i> Konfirmasi Setuju
                            </button>
                            <button onclick="cancelAction(${request.id})" style="
                              background: #6c757d;
                              border: none;
                              color: white;
                              padding: 10px 20px;
                              border-radius: 8px;
                              font-weight: 500;
                              cursor: pointer;
                              transition: all 0.2s ease;
                            ">
                              <i class="bi bi-x-lg"></i> Batal
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Reject Form -->
                      <div id="reject-${request.id}" style="margin-top: 15px; display: none;">
                        <div style="
                          background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.05));
                          border: 2px solid rgba(244, 67, 54, 0.3);
                          border-radius: 15px;
                          padding: 20px;
                          backdrop-filter: blur(5px);
                        ">
                          <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                            <i class="bi bi-x-circle-fill" style="color: #f44336; font-size: 1.3rem;"></i>
                            <h6 style="color: #f44336; margin: 0; font-weight: 600; font-size: 1.1rem;">Tolak Perubahan Email</h6>
                          </div>
                          
                          <div style="margin-bottom: 15px;">
                            <label style="color: #e0e0e0; font-weight: 500; margin-bottom: 8px; display: block;">
                              Alasan Penolakan <span style="color: #f44336;">*</span>
                            </label>
                            <textarea id="reject-notes-${request.id}" 
                                      placeholder="Jelaskan alasan penolakan perubahan email..." 
                                      style="
                                        width: 100%;
                                        padding: 12px;
                                        border-radius: 10px;
                                        background: rgba(255,255,255,0.1);
                                        border: 1px solid rgba(244, 67, 54, 0.3);
                                        color: white;
                                        font-size: 0.9rem;
                                        resize: vertical;
                                        min-height: 80px;
                                      " rows="3" required></textarea>
                            <small style="color: #f44336; display: flex; align-items: center; gap: 5px; margin-top: 8px; font-size: 0.8rem;">
                              <i class="bi bi-exclamation-triangle-fill"></i>
                              Alasan ini akan ditampilkan kepada user sebagai feedback
                            </small>
                          </div>
                          
                          <div style="display: flex; gap: 10px;">
                            <button onclick="confirmAction(${request.id}, 'reject')" style="
                              background: linear-gradient(45deg, #f44336, #d32f2f);
                              border: none;
                              color: white;
                              padding: 10px 20px;
                              border-radius: 8px;
                              font-weight: 600;
                              cursor: pointer;
                              transition: all 0.2s ease;
                            ">
                              <i class="bi bi-x-lg"></i> Konfirmasi Tolak
                            </button>
                            <button onclick="cancelAction(${request.id})" style="
                              background: #6c757d;
                              border: none;
                              color: white;
                              padding: 10px 20px;
                              border-radius: 8px;
                              font-weight: 500;
                              cursor: pointer;
                              transition: all 0.2s ease;
                            ">
                              <i class="bi bi-arrow-left"></i> Batal
                            </button>
                          </div>
                        </div>
                      </div>
                    ` : ''}
                    
                    ${request.admin_notes ? `
                      <!-- Admin Decision -->
                      <div style="
                        margin-top: 20px;
                        padding: 20px;
                        background: linear-gradient(135deg, ${request.status === 'rejected' ? 'rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.05)' : 'rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05)'});
                        border: 2px solid ${request.status === 'rejected' ? 'rgba(244, 67, 54, 0.3)' : 'rgba(76, 175, 80, 0.3)'};
                        border-radius: 15px;
                        backdrop-filter: blur(5px);
                      ">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                          <div style="
                            background: ${request.status === 'rejected' ? '#f44336' : '#4caf50'};
                            border-radius: 50%;
                            width: 40px;
                            height: 40px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 4px 15px ${request.status === 'rejected' ? 'rgba(244, 67, 54, 0.4)' : 'rgba(76, 175, 80, 0.4)'};
                          ">
                            <i class="bi bi-${request.status === 'rejected' ? 'x-circle-fill' : 'check-circle-fill'}" style="color: white; font-size: 1.2rem;"></i>
                          </div>
                          <div>
                            <h6 style="color: ${request.status === 'rejected' ? '#f44336' : '#4caf50'}; margin: 0; font-weight: 600; font-size: 1.1rem;">
                              ${request.status === 'rejected' ? 'Perubahan Ditolak' : 'Perubahan Disetujui'}
                            </h6>
                            <small style="color: #b0b0b0; font-size: 0.85rem;">
                              <i class="bi bi-person-badge"></i> ${request.admin_email || 'Admin'} • 
                              <i class="bi bi-calendar3"></i> ${request.admin_action_date ? new Date(request.admin_action_date).toLocaleDateString('id-ID', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }) : 'N/A'}
                            </small>
                          </div>
                        </div>
                        
                        <div style="
                          background: rgba(255, 255, 255, 0.05);
                          border-radius: 10px;
                          padding: 15px;
                          border-left: 4px solid ${request.status === 'rejected' ? '#f44336' : '#4caf50'};
                        ">
                          <div style="color: #e0e0e0; font-weight: 500; margin-bottom: 8px; font-size: 0.9rem;">
                            <i class="bi bi-${request.status === 'rejected' ? 'exclamation-triangle' : 'chat-quote'}"></i>
                            ${request.status === 'rejected' ? 'Alasan Penolakan:' : 'Catatan Admin:'}
                          </div>
                          <div style="color: white; line-height: 1.5; font-size: 0.95rem;">
                            "${request.admin_notes}"
                          </div>
                                                 </div>
                       </div>
                     ` : ''}
                     
                     <!-- Delete Button for ALL Requests -->
                     <div style="margin-top: 20px; text-align: right;">
                       <button onclick="deleteEmailRequest(${request.id})" style="
                         background: linear-gradient(45deg, #6c757d, #5a6268);
                         border: none;
                         color: white;
                         padding: 8px 16px;
                         border-radius: 8px;
                         font-weight: 500;
                         font-size: 0.8rem;
                         transition: all 0.3s ease;
                         display: inline-flex;
                         align-items: center;
                         gap: 6px;
                         cursor: pointer;
                         box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
                       " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(108, 117, 125, 0.4)';" 
                          onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(108, 117, 125, 0.3)';">
                         <i class="bi bi-trash3"></i>
                         Hapus Request
                       </button>
                     </div>
                   </div>
                `;
              });
            } else {
              approvalCards = `
                <div style="
                  text-align: center;
                  padding: 60px 20px;
                  background: rgba(255, 255, 255, 0.03);
                  border-radius: 20px;
                  border: 2px dashed rgba(255, 255, 255, 0.1);
                ">
                  <div style="
                    background: rgba(79, 195, 247, 0.1);
                    border-radius: 50%;
                    width: 80px;
                    height: 80px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 20px auto;
                  ">
                    <i class="bi bi-inbox" style="font-size: 2.5rem; color: #4fc3f7;"></i>
                  </div>
                  <h4 style="color: #4fc3f7; margin-bottom: 10px; font-weight: 600;">Tidak Ada Permintaan</h4>
                  <p style="color: #b0b0b0; margin: 0; font-size: 1rem; line-height: 1.5;">
                    Belum ada permintaan perubahan email yang perlu diproses.<br>
                    <small style="color: #888;">Permintaan baru akan muncul di sini secara otomatis.</small>
                  </p>
                </div>
              `;
            }

            detailsContent.innerHTML = `
              <!-- Header Section -->
              <div style="
                background: linear-gradient(135deg, rgba(79, 195, 247, 0.1), rgba(79, 195, 247, 0.05));
                border: 1px solid rgba(79, 195, 247, 0.2);
                border-radius: 20px;
                padding: 25px;
                margin-bottom: 30px;
                text-align: center;
              ">
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
                  ">
                    <i class="bi bi-envelope-check-fill" style="color: white; font-size: 1.8rem;"></i>
                  </div>
                  <div>
                    <h2 style="color: #4fc3f7; margin: 0; font-weight: 700; font-size: 1.8rem;">
                      Email Change Approvals
                    </h2>
                    <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                      Kelola persetujuan perubahan email pengguna
                    </p>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                  <button onclick="refreshEmailApprovals()" style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border: none;
                    color: white;
                    padding: 12px 24px;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
                  " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(79, 195, 247, 0.4)';" 
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(79, 195, 247, 0.3)';">
                    <i class="bi bi-arrow-clockwise"></i>
                    Refresh Data
                  </button>
                  
                  <div style="
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 12px 20px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  ">
                    <i class="bi bi-info-circle" style="color: #4fc3f7;"></i>
                    <span style="color: #e0e0e0; font-size: 0.9rem;">
                      Total: <strong style="color: #4fc3f7;">${data.requests ? data.requests.length : 0}</strong> permintaan
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- Requests List -->
              ${approvalCards}
            `;
            detailsContent.classList.remove('hidden');
            detailsContent.classList.add('visible');
          }, 500);
        })
        .catch(error => {
          console.error('Error fetching email approvals:', error);
          setTimeout(() => {
            detailsContent.innerHTML = `
              <h3 style="margin-bottom: 20px;">Email Change Approvals</h3>
              <div class="alert alert-danger">
                Error loading email approvals. Please try again.
              </div>
            `;
            detailsContent.classList.remove('hidden');
            detailsContent.classList.add('visible');
          }, 500);
        });
      }, 1800); // Close the setTimeout we added

      // Update active button states handled by setActiveMenu function
    }

    function showSettings() {
      // Stop QRIS auto-refresh when leaving QRIS page
      handleMenuChange();
      
      // Save active menu state
      localStorage.setItem('activeMenu', 'settings');
      setActiveMenu('settings-card');

      const detailsContent = document.getElementById('details-content');

      detailsContent.classList.remove('visible');
      detailsContent.classList.add('hidden');

      // Show loading immediately
      detailsContent.innerHTML = showLoadingWithProgress(
        'Loading Settings',
        'Memuat konfigurasi sistem...',
        '#9c27b0'
      );
      
      detailsContent.classList.remove('hidden');
      detailsContent.classList.add('visible');
      
      // Start progress animation with settings-specific messages
      setTimeout(() => animateProgress(1200, [
        'Memuat konfigurasi sistem...',
        'Memeriksa pengaturan keamanan...',
        'Menyiapkan panel kontrol...',
        'Settings panel siap!'
      ]), 100);

      setTimeout(() => {

        detailsContent.innerHTML = `
          <!-- Header Section -->
          <div style="
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(156, 39, 176, 0.05));
            border: 1px solid rgba(156, 39, 176, 0.2);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
          ">
            <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
              <div style="
                background: linear-gradient(45deg, #9c27b0, #7b1fa2);
                border-radius: 50%;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
              ">
                <i class="bi bi-gear-fill" style="color: white; font-size: 1.8rem;"></i>
              </div>
              <div>
                <h2 style="color: #9c27b0; margin: 0; font-weight: 700; font-size: 1.8rem;">
                  System Settings
                </h2>
                <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                  Konfigurasi sistem dan preferensi admin
                </p>
              </div>
            </div>
          </div>

          <!-- Settings Cards -->
          <div class="row">
            <div class="col-md-6 mb-4">
              <div style="
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 25px;
                height: 100%;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(79, 195, 247, 0.2)'; this.style.borderColor='#4fc3f7';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
                    border-radius: 50%;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-database-gear" style="color: white; font-size: 1.2rem;"></i>
                  </div>
                  <h5 style="color: #4fc3f7; margin: 0; font-weight: 600;">Database Configuration</h5>
                </div>
                <p style="color: #b0b0b0; font-size: 0.9rem; line-height: 1.5;">
                  Kelola koneksi database, backup otomatis, dan pengaturan performa database sistem.
                </p>
                <button style="
                  background: rgba(79, 195, 247, 0.1);
                  border: 1px solid #4fc3f7;
                  color: #4fc3f7;
                  padding: 8px 16px;
                  border-radius: 8px;
                  font-size: 0.85rem;
                  margin-top: 10px;
                  cursor: pointer;
                  transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(79, 195, 247, 0.2)';" 
                   onmouseout="this.style.background='rgba(79, 195, 247, 0.1)';">
                  Configure
                </button>
              </div>
            </div>

            <div class="col-md-6 mb-4">
              <div style="
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 25px;
                height: 100%;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(76, 175, 80, 0.2)'; this.style.borderColor='#4caf50';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #4caf50, #45a049);
                    border-radius: 50%;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-shield-check" style="color: white; font-size: 1.2rem;"></i>
                  </div>
                  <h5 style="color: #4caf50; margin: 0; font-weight: 600;">Security Settings</h5>
                </div>
                <p style="color: #b0b0b0; font-size: 0.9rem; line-height: 1.5;">
                  Atur keamanan sistem, rate limiting, firewall, dan pengaturan autentikasi pengguna.
                </p>
                <button style="
                  background: rgba(76, 175, 80, 0.1);
                  border: 1px solid #4caf50;
                  color: #4caf50;
                  padding: 8px 16px;
                  border-radius: 8px;
                  font-size: 0.85rem;
                  margin-top: 10px;
                  cursor: pointer;
                  transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(76, 175, 80, 0.2)';" 
                   onmouseout="this.style.background='rgba(76, 175, 80, 0.1)';">
                  Configure
                </button>
              </div>
            </div>

            <div class="col-md-6 mb-4">
              <div style="
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 25px;
                height: 100%;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(255, 152, 0, 0.2)'; this.style.borderColor='#ff9800';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #ff9800, #f57c00);
                    border-radius: 50%;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-envelope-gear" style="color: white; font-size: 1.2rem;"></i>
                  </div>
                  <h5 style="color: #ff9800; margin: 0; font-weight: 600;">Email Configuration</h5>
                </div>
                <p style="color: #b0b0b0; font-size: 0.9rem; line-height: 1.5;">
                  Konfigurasi SMTP, template email, notifikasi otomatis, dan pengaturan email sistem.
                </p>
                <button style="
                  background: rgba(255, 152, 0, 0.1);
                  border: 1px solid #ff9800;
                  color: #ff9800;
                  padding: 8px 16px;
                  border-radius: 8px;
                  font-size: 0.85rem;
                  margin-top: 10px;
                  cursor: pointer;
                  transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255, 152, 0, 0.2)';" 
                   onmouseout="this.style.background='rgba(255, 152, 0, 0.1)';">
                  Configure
                </button>
              </div>
            </div>

            <div class="col-md-6 mb-4">
              <div style="
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 25px;
                height: 100%;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(156, 39, 176, 0.2)'; this.style.borderColor='#9c27b0';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.3)'; this.style.borderColor='rgba(255, 255, 255, 0.1)';">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                  <div style="
                    background: linear-gradient(45deg, #9c27b0, #7b1fa2);
                    border-radius: 50%;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-sliders" style="color: white; font-size: 1.2rem;"></i>
                  </div>
                  <h5 style="color: #9c27b0; margin: 0; font-weight: 600;">System Preferences</h5>
                </div>
                <p style="color: #b0b0b0; font-size: 0.9rem; line-height: 1.5;">
                  Pengaturan umum sistem, timezone, bahasa, tema, dan preferensi tampilan admin.
                </p>
                <button style="
                  background: rgba(156, 39, 176, 0.1);
                  border: 1px solid #9c27b0;
                  color: #9c27b0;
                  padding: 8px 16px;
                  border-radius: 8px;
                  font-size: 0.85rem;
                  margin-top: 10px;
                  cursor: pointer;
                  transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(156, 39, 176, 0.2)';" 
                   onmouseout="this.style.background='rgba(156, 39, 176, 0.1)';">
                  Configure
                </button>
              </div>
            </div>
          </div>
        `;

        detailsContent.classList.remove('hidden');

        detailsContent.classList.add('visible');

      }, 1400);

          }

    // Restore active menu on page load
    // Cleanup when page is about to unload
    window.addEventListener('beforeunload', function() {
      stopQrisAutoRefresh();
    });

    // Cleanup when page loses focus (optional)
    document.addEventListener('visibilitychange', function() {
      if (document.hidden) {
        // Page is hidden, reduce refresh frequency or stop
        if (qrisAutoRefreshInterval && isQrisPageActive) {
          clearInterval(qrisAutoRefreshInterval);
          // Restart with longer interval when page is hidden
          qrisAutoRefreshInterval = setInterval(function() {
            if (isQrisPageActive) {
              checkForNewQrisTransactions();
            }
          }, 10000); // Check every 10 seconds when hidden
        }
      } else {
        // Page is visible again, restore normal frequency
        if (isQrisPageActive) {
          startQrisAutoRefresh();
        }
      }
    });

    document.addEventListener('DOMContentLoaded', function() {
      const activeMenu = localStorage.getItem('activeMenu');
      if (activeMenu) {
        switch(activeMenu) {
          case 'user-management':
            showUserManagement();
            break;
          case 'server-management':
            showServerManagement();
            break;
          case 'order-management':
            showOrderManagement();
            break;
          case 'email-approvals':
            showEmailApprovals();
            break;
          case 'qris-history':
            loadQrisHistory();
            break;
          case 'settings':
            showSettings();
            break;
        }
      }
    });

    // Email Approvals Functions
    function approveEmailRequest(requestId) {
      // Hide reject form if open
      document.getElementById('reject-' + requestId).style.display = 'none';
      // Show approve form
      document.getElementById('approve-' + requestId).style.display = 'block';
    }

    function rejectEmailRequest(requestId) {
      // Hide approve form if open
      document.getElementById('approve-' + requestId).style.display = 'none';
      // Show reject form
      document.getElementById('reject-' + requestId).style.display = 'block';
    }

    function cancelAction(requestId) {
      document.getElementById('approve-' + requestId).style.display = 'none';
      document.getElementById('reject-' + requestId).style.display = 'none';
    }

    function confirmAction(requestId, action) {
      let adminNotes = '';
      
      if (action === 'approve') {
        adminNotes = document.getElementById('approve-notes-' + requestId).value;
      } else if (action === 'reject') {
        adminNotes = document.getElementById('reject-notes-' + requestId).value;
        
        // Validate rejection reason
        if (!adminNotes.trim()) {
          alert('Alasan penolakan wajib diisi!');
          return;
        }
      }
      
      fetch('api/get_email_approvals.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          request_id: requestId,
          action: action,
          admin_notes: adminNotes
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert(data.message);
          refreshEmailApprovals();
        } else {
          alert('Error: ' + (data.error || 'Unknown error'));
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Network error occurred');
      });
    }

    function refreshEmailApprovals() {
      showEmailApprovals();
    }

    function deleteEmailRequest(requestId) {
      if (confirm('Apakah Anda yakin ingin menghapus permintaan ini?\n\nTindakan ini tidak dapat dibatalkan dan akan menghapus permintaan secara permanen.')) {
        fetch('api/get_email_approvals.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            request_id: requestId,
            action: 'delete'
          })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('Permintaan berhasil dihapus!');
            refreshEmailApprovals();
          } else {
            alert('Gagal menghapus permintaan: ' + (data.error || 'Unknown error'));
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Terjadi kesalahan saat menghapus permintaan.');
        });
      }
    }







     

    // Global User Management Search and Filter Functions
    window.filterUsers = function() {
      const searchValue = document.getElementById('user-search-input').value.toLowerCase();
      
      if (!window.allUsers) return;
      
      // Filter users based on search
      window.filteredUsers = window.allUsers.filter(user => {
        const email = user.email.toLowerCase();
        const group = user.group.toLowerCase();
        const formattedDate = new Date(user.created_at).toLocaleDateString('id-ID', {
          weekday: 'short',
          year: 'numeric', 
          month: 'short',
          day: 'numeric'
        }).toLowerCase();
        
        const searchableText = `${email} ${group} ${formattedDate}`;
        return searchableText.includes(searchValue);
      });

      // Reset to page 1 and re-render
      window.currentUserPage = 1;
      if (typeof window.renderUserCards === 'function') {
        window.renderUserCards();
      }
      
      // Update user count
      const userCountElement = document.getElementById('user-count');
      if (userCountElement) {
        userCountElement.textContent = window.filteredUsers.length;
      }
    }

    window.filterUsersByGroup = function(group) {
      const searchInput = document.getElementById('user-search-input');
      const searchValue = searchInput ? searchInput.value.toLowerCase() : '';
      
      if (!window.allUsers) return;

      // Reset filter button styles
      document.querySelectorAll('[id^="filter-"][id*="users"]').forEach(btn => {
        btn.style.background = 'rgba(255, 255, 255, 0.1)';
        btn.style.border = '1px solid rgba(255, 255, 255, 0.2)';
        btn.style.color = '#e0e0e0';
      });

      // Highlight active filter
      const activeBtn = document.getElementById(`filter-${group.toLowerCase()}-users`);
      if (activeBtn) {
        activeBtn.style.background = 'linear-gradient(45deg, #4fc3f7, #29b6f6)';
        activeBtn.style.border = 'none';
        activeBtn.style.color = 'white';
      }

      // Filter users based on group and search
      window.filteredUsers = window.allUsers.filter(user => {
        const groupMatch = (group === 'ALL') || (user.group === group);
        
        const email = user.email.toLowerCase();
        const searchMatch = !searchValue || email.includes(searchValue);
        
        return groupMatch && searchMatch;
      });

      // Reset to page 1 and re-render
      window.currentUserPage = 1;
      if (typeof window.renderUserCards === 'function') {
        window.renderUserCards();
      }
      
      // Update user count
      const userCountElement = document.getElementById('user-count');
      if (userCountElement) {
        userCountElement.textContent = window.filteredUsers.length;
      }
    }

    window.clearUserFilters = function() {
      // Clear search input
      const searchInput = document.getElementById('user-search-input');
      if (searchInput) {
        searchInput.value = '';
      }
      
      // Reset filtered users to all users
      if (window.allUsers) {
        window.filteredUsers = window.allUsers;
        window.currentUserPage = 1;
      }
      
      // Reset to show all users
      window.filterUsersByGroup('ALL');
    }

    // Global Server Management Search and Filter Functions
    window.filterServersBySearch = function() {
      const searchValue = document.getElementById('server-search-input').value.toLowerCase();
      
      if (!window.allServers) return;
      
      // Filter servers based on search
      window.filteredServers = window.allServers.filter(server => {
        const serverName = (server.nama_server || '').toLowerCase();
        const owner = (server.pemilik || '').toLowerCase();
        const packageName = (server.paket || '').toLowerCase();
        const ip = (server.ip_server || '').toLowerCase();
        const typeGame = (server.type_game || '').toLowerCase();
        const status = (server.status || '').toLowerCase();
        
        const searchableText = `${serverName} ${owner} ${packageName} ${ip} ${typeGame} ${status}`;
        return searchableText.includes(searchValue);
      });

      // Reset to page 1 and re-render
      window.currentServerPage = 1;
      if (typeof window.renderServerCards === 'function') {
        window.renderServerCards();
      }
      
      // Update server count
      const serverCountElement = document.getElementById('server-count');
      if (serverCountElement) {
        serverCountElement.textContent = window.filteredServers.length;
      }
    }
    
    window.filterServersEnhanced = function(typeGame, buttonId) {
      const searchInput = document.getElementById('server-search-input');
      const searchValue = searchInput ? searchInput.value.toLowerCase() : '';
      
      if (!window.allServers) return;

      // Reset filter button styles
      document.querySelectorAll('button[id^="filter-"]').forEach(btn => {
        btn.style.background = 'rgba(255, 255, 255, 0.1)';
        btn.style.border = '1px solid rgba(255, 255, 255, 0.2)';
        btn.style.color = '#e0e0e0';
      });

      // Highlight active filter
      const activeBtn = document.getElementById(buttonId);
      if (activeBtn) {
        activeBtn.style.background = 'linear-gradient(45deg, #4fc3f7, #29b6f6)';
        activeBtn.style.border = 'none';
        activeBtn.style.color = 'white';
      }

      // Filter servers based on type and search
      window.filteredServers = window.allServers.filter(server => {
        const typeMatch = (typeGame === 'ALL') || (server.type_game === typeGame);
        
        const serverName = (server.nama_server || '').toLowerCase();
        const owner = (server.pemilik || '').toLowerCase();
        const packageName = (server.paket || '').toLowerCase();
        const ip = (server.ip_server || '').toLowerCase();
        const status = (server.status || '').toLowerCase();
        const typeGameLower = (server.type_game || '').toLowerCase();
        
        const searchableText = `${serverName} ${owner} ${packageName} ${ip} ${typeGameLower} ${status}`;
        const searchMatch = !searchValue || searchableText.includes(searchValue);
        
        return typeMatch && searchMatch;
      });

      // Reset to page 1 and re-render
      window.currentServerPage = 1;
      if (typeof window.renderServerCards === 'function') {
        window.renderServerCards();
      }

      // Update server count
      const serverCountElement = document.getElementById('server-count');
      if (serverCountElement) {
        serverCountElement.textContent = window.filteredServers.length;
      }
    }
    
    window.clearServerFilters = function() {
      // Clear search input
      const searchInput = document.getElementById('server-search-input');
      if (searchInput) {
        searchInput.value = '';
      }
      
      // Reset filtered servers to all servers
      if (window.allServers) {
        window.filteredServers = window.allServers;
        window.currentServerPage = 1;
      }
      
      // Show all servers
      window.filterServersEnhanced('ALL', 'filter-all');
    }

    function updateTimeLeft() {

      // Add your update time left logic here

    }



    setInterval(updateTimeLeft, 1000);

    updateTimeLeft();
    
    // Global variables for QRIS real-time updates
    let qrisAutoRefreshInterval = null;
    let lastQrisTransactionId = null;
    let isQrisPageActive = false;
    let currentQrisPage = 1;

    // Function to load QRIS History (initial load with animation)
    function loadQrisHistory(page = 1, skipAnimation = false) {
      // Save active menu state
      localStorage.setItem('activeMenu', 'qris-history');
      setActiveMenu('qris-history-card');
      
      // Mark QRIS page as active and save current page
      isQrisPageActive = true;
      currentQrisPage = page;

      const detailsContent = document.getElementById('details-content');

      if (!skipAnimation) {
        detailsContent.classList.remove('visible');
        detailsContent.classList.add('hidden');

        // Show loading immediately
        detailsContent.innerHTML = showLoadingWithProgress(
          'Loading Riwayat QRIS',
          'Memuat data transaksi QRIS...',
          '#4caf50'
        );
        
        detailsContent.classList.remove('hidden');
        detailsContent.classList.add('visible');
        
        // Start progress animation
        setTimeout(() => animateProgress(1200, [
          'Memuat data transaksi...',
          'Menyiapkan statistik...',
          'Menghitung total pembayaran...',
          'Riwayat QRIS siap!'
        ]), 100);
      }

      // Fetch QRIS data
      const fetchDelay = skipAnimation ? 0 : 1300;
      setTimeout(() => {
        fetch(`api/get_qris_history.php?page=${page}&limit=4`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              displayQrisHistory(data);
              // Store the latest transaction ID for comparison
              if (data.data && data.data.length > 0) {
                lastQrisTransactionId = data.data[0].id;
              }
              // Start auto-refresh only on initial load
              if (!skipAnimation) {
                startQrisAutoRefresh();
              }
            } else {
              detailsContent.innerHTML = `
                <div style="text-align: center; color: #f44336; padding: 40px;">
                  <i class="bi bi-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                  <h3>Error Loading Data</h3>
                  <p>${data.error || 'Unknown error occurred'}</p>
                </div>
              `;
            }
          })
          .catch(error => {
            console.error('Error:', error);
            detailsContent.innerHTML = `
              <div style="text-align: center; color: #f44336; padding: 40px;">
                <i class="bi bi-wifi-off" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <h3>Connection Error</h3>
                <p>Failed to load QRIS history data</p>
              </div>
            `;
          });
      }, fetchDelay);
    }

    // Function for quick pagination navigation (no loading animation)
    function navigateQrisPage(page) {
      currentQrisPage = page;
      
      // Show minimal loading indicator
      const detailsContent = document.getElementById('details-content');
      const loadingOverlay = document.createElement('div');
      loadingOverlay.id = 'pagination-loading';
      loadingOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: 20px;
      `;
      loadingOverlay.innerHTML = `
        <div style="
          background: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 15px 25px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          gap: 10px;
          font-weight: 500;
        ">
          <div class="spinner-border spinner-border-sm" role="status"></div>
          Memuat halaman ${page}...
        </div>
      `;
      
      detailsContent.style.position = 'relative';
      detailsContent.appendChild(loadingOverlay);

      // Fetch data
      fetch(`api/get_qris_history.php?page=${page}&limit=4`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            displayQrisHistory(data);
          } else {
            console.error('Error loading page:', data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        })
        .finally(() => {
          // Remove loading overlay
          const overlay = document.getElementById('pagination-loading');
          if (overlay) {
            overlay.remove();
          }
        });
    }

    // Function to display QRIS history data
    function displayQrisHistory(data) {
      const detailsContent = document.getElementById('details-content');
      const stats = data.statistics;
      const logs = data.data;
      const pagination = data.pagination;
      
      let content = `
        <!-- Header Section -->
        <div style="
          background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
          border: 1px solid rgba(76, 175, 80, 0.2);
          border-radius: 20px;
          padding: 25px;
          margin-bottom: 30px;
          text-align: center;
        ">
          <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
            <div style="
              background: linear-gradient(45deg, #4caf50, #388e3c);
              border-radius: 50%;
              width: 60px;
              height: 60px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            ">
              <i class="bi bi-credit-card-2-front" style="color: white; font-size: 1.8rem;"></i>
            </div>
            <div>
              <h2 style="color: #4caf50; margin: 0; font-weight: 700; font-size: 1.8rem;">
                Riwayat QRIS
              </h2>
              <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 1rem;">
                Monitoring transaksi pembayaran QRIS
              </p>
            </div>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <div style="background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 15px; padding: 20px; text-align: center;">
              <i class="bi bi-arrow-down-circle" style="color: #4caf50; font-size: 2rem; margin-bottom: 10px;"></i>
              <h4 style="color: #4caf50; margin: 0;">Rp ${new Intl.NumberFormat('id-ID').format(stats.total_income || 0)}</h4>
              <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 0.9rem;">Total Masuk (${stats.income_count || 0} transaksi)</p>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <div style="background: rgba(79, 195, 247, 0.1); border: 1px solid rgba(79, 195, 247, 0.3); border-radius: 15px; padding: 20px; text-align: center;">
              <i class="bi bi-list-check" style="color: #4fc3f7; font-size: 2rem; margin-bottom: 10px;"></i>
              <h4 style="color: #4fc3f7; margin: 0;">${stats.total_transactions || 0}</h4>
              <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 0.9rem;">Total Transaksi</p>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <div style="background: rgba(156, 39, 176, 0.1); border: 1px solid rgba(156, 39, 176, 0.3); border-radius: 15px; padding: 20px; text-align: center;">
              <i class="bi bi-wallet2" style="color: #9c27b0; font-size: 2rem; margin-bottom: 10px;"></i>
              <h4 style="color: #9c27b0; margin: 0;">Rp ${new Intl.NumberFormat('id-ID').format(stats.current_balance || 0)}</h4>
              <p style="color: #b0b0b0; margin: 5px 0 0 0; font-size: 0.9rem;">Saldo Akhir</p>
            </div>
          </div>
        </div>

        <!-- Transaction History -->
        <div style="
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 20px;
          padding: 25px;
        ">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="color: #e0e0e0; margin: 0;">
              <i class="bi bi-clock-history" style="margin-right: 10px;"></i>
              Transaksi Terbaru
            </h3>
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="
                width: 8px;
                height: 8px;
                background: #4caf50;
                border-radius: 50%;
                animation: pulse 2s infinite;
              "></div>
              <small style="color: #4caf50; font-weight: 500;">Live Update</small>
            </div>
          </div>
      `;

      if (logs && logs.length > 0) {
        logs.forEach(log => {
          const typeColor = log.type === 'CR' ? '#4caf50' : '#f44336';
          const typeIcon = log.type === 'CR' ? 'arrow-down-circle' : 'arrow-up-circle';
          const typeText = log.type === 'CR' ? 'Masuk' : 'Keluar';
          
          content += `
            <div style="
              background: rgba(255, 255, 255, 0.03);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 15px;
              padding: 20px;
              margin-bottom: 15px;
              transition: all 0.3s ease;
            " onmouseover="this.style.borderColor='${typeColor}'; this.style.background='rgba(${log.type === 'CR' ? '76, 175, 80' : '244, 67, 54'}, 0.05)';" 
               onmouseout="this.style.borderColor='rgba(255, 255, 255, 0.1)'; this.style.background='rgba(255, 255, 255, 0.03)';">
              <div class="row align-items-center">
                <div class="col-md-1">
                  <div style="
                    background: ${typeColor};
                    border-radius: 50%;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  ">
                    <i class="bi bi-${typeIcon}" style="color: white; font-size: 1.2rem;"></i>
                  </div>
                </div>
                <div class="col-md-3">
                  <h5 style="color: ${typeColor}; margin: 0; font-weight: 600;">Rp ${new Intl.NumberFormat('id-ID').format(log.amount)}</h5>
                  <small style="color: #b0b0b0;">${typeText} • ${log.brand_name || 'Unknown Bank'}</small>
                </div>
                <div class="col-md-3">
                  <p style="color: #e0e0e0; margin: 0; font-size: 0.9rem;">${log.buyer_reff || '-'}</p>
                  <small style="color: #b0b0b0;">Pengirim</small>
                </div>
                <div class="col-md-3">
                  <p style="color: #e0e0e0; margin: 0; font-size: 0.9rem;">${log.payment_id}</p>
                  <small style="color: #b0b0b0;">Payment ID</small>
                </div>
                <div class="col-md-2">
                  <p style="color: #b0b0b0; margin: 0; font-size: 0.85rem;">${new Date(log.transaction_date).toLocaleString('id-ID')}</p>
                  <small style="color: #777;">Waktu Transaksi</small>
                </div>
              </div>
            </div>
          `;
        });
      } else {
        content += `
          <div style="text-align: center; padding: 40px; color: #b0b0b0;">
            <i class="bi bi-inbox" style="font-size: 3rem; margin-bottom: 20px;"></i>
            <h4>Belum Ada Transaksi</h4>
            <p>Data transaksi QRIS akan muncul di sini</p>
          </div>
        `;
      }

      // Add pagination controls
      if (pagination && pagination.last_page > 1) {
        content += `
          <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
          ">
            <small style="color: #b0b0b0; margin-right: 15px;">
              Halaman ${pagination.current_page} dari ${pagination.last_page} 
              (${pagination.total} total transaksi)
            </small>
        `;

        // Previous button
        if (pagination.current_page > 1) {
          content += `
            <button onclick="navigateQrisPage(${pagination.current_page - 1})" style="
              background: rgba(76, 175, 80, 0.2);
              border: 1px solid #4caf50;
              color: #4caf50;
              padding: 8px 12px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.3s ease;
              font-size: 0.9rem;
            " onmouseover="this.style.background='rgba(76, 175, 80, 0.3)'" 
               onmouseout="this.style.background='rgba(76, 175, 80, 0.2)'">
              <i class="bi bi-chevron-left"></i> Sebelumnya
            </button>
          `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
          const isActive = i === pagination.current_page;
          content += `
            <button onclick="navigateQrisPage(${i})" style="
              background: ${isActive ? '#4caf50' : 'rgba(255, 255, 255, 0.05)'};
              border: 1px solid ${isActive ? '#4caf50' : 'rgba(255, 255, 255, 0.2)'};
              color: ${isActive ? 'white' : '#e0e0e0'};
              padding: 8px 12px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.3s ease;
              font-weight: ${isActive ? 'bold' : 'normal'};
              min-width: 40px;
            " ${!isActive ? `onmouseover="this.style.background='rgba(255, 255, 255, 0.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'"` : ''}>
              ${i}
            </button>
          `;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
          content += `
            <button onclick="navigateQrisPage(${pagination.current_page + 1})" style="
              background: rgba(76, 175, 80, 0.2);
              border: 1px solid #4caf50;
              color: #4caf50;
              padding: 8px 12px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.3s ease;
              font-size: 0.9rem;
            " onmouseover="this.style.background='rgba(76, 175, 80, 0.3)'" 
               onmouseout="this.style.background='rgba(76, 175, 80, 0.2)'">
              Selanjutnya <i class="bi bi-chevron-right"></i>
            </button>
          `;
        }

        content += `</div>`;
      }

      content += `
        </div>
      `;

      detailsContent.innerHTML = content;
    }

    // Function to start auto-refresh for QRIS data
    function startQrisAutoRefresh() {
      // Clear existing interval if any
      if (qrisAutoRefreshInterval) {
        clearInterval(qrisAutoRefreshInterval);
      }
      
      // Set up auto-refresh every 3 seconds
      qrisAutoRefreshInterval = setInterval(function() {
        if (isQrisPageActive) {
          checkForNewQrisTransactions();
        }
      }, 3000);
    }

    // Function to stop auto-refresh
    function stopQrisAutoRefresh() {
      if (qrisAutoRefreshInterval) {
        clearInterval(qrisAutoRefreshInterval);
        qrisAutoRefreshInterval = null;
      }
      isQrisPageActive = false;
    }

    // Function to check for new transactions
    function checkForNewQrisTransactions() {
      fetch('api/get_qris_history.php?limit=1')
        .then(response => response.json())
        .then(data => {
          if (data.success && data.data && data.data.length > 0) {
            const latestTransaction = data.data[0];
            
            // Check if there's a new transaction
            if (lastQrisTransactionId === null || latestTransaction.id > lastQrisTransactionId) {
              console.log('New QRIS transaction detected:', latestTransaction);
              
              // Update the last transaction ID
              lastQrisTransactionId = latestTransaction.id;
              
              // Show notification
              showNewTransactionNotification(latestTransaction);
              
              // Refresh the data
              refreshQrisData();
            }
          }
        })
        .catch(error => {
          console.error('Error checking for new transactions:', error);
        });
    }

    // Function to refresh QRIS data without loading animation
    function refreshQrisData() {
      // Only refresh if we're on the first page to avoid disrupting pagination
      if (currentQrisPage === 1) {
        fetch(`api/get_qris_history.php?page=${currentQrisPage}&limit=4`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              displayQrisHistory(data);
            }
          })
          .catch(error => {
            console.error('Error refreshing QRIS data:', error);
          });
      }
    }

    // Function to show new transaction notification
    function showNewTransactionNotification(transaction) {
      const typeColor = transaction.type === 'CR' ? '#4caf50' : '#f44336';
      const typeText = transaction.type === 'CR' ? 'Masuk' : 'Keluar';
      const amount = new Intl.NumberFormat('id-ID').format(transaction.amount);
      
      // Create notification element
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, ${typeColor}22, ${typeColor}11);
        border: 1px solid ${typeColor};
        border-radius: 12px;
        padding: 15px 20px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        transform: translateX(400px);
        transition: all 0.3s ease;
        min-width: 300px;
      `;
      
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 12px;">
          <div style="
            background: ${typeColor};
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <i class="bi bi-${transaction.type === 'CR' ? 'arrow-down-circle' : 'arrow-up-circle'}" style="color: white; font-size: 1rem;"></i>
          </div>
          <div>
            <div style="font-weight: 600; margin-bottom: 2px;">Transaksi ${typeText} Baru</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">Rp ${amount} • ${transaction.brand_name || 'Unknown'}</div>
          </div>
        </div>
      `;
      
      document.body.appendChild(notification);
      
      // Animate in
      setTimeout(() => {
        notification.style.transform = 'translateX(0)';
      }, 100);
      
      // Auto remove after 5 seconds
      setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 5000);
    }

    // Function to handle menu changes (stop auto-refresh when leaving QRIS page)
    function handleMenuChange() {
      stopQrisAutoRefresh();
    }

    // Load saved menu state on page load
    document.addEventListener('DOMContentLoaded', function() {
      const activeMenu = localStorage.getItem('activeMenu');
      if (activeMenu) {
        switch(activeMenu) {
          case 'user-management':
            showUserManagement();
            break;
          case 'server-management':
            showServerManagement();
            break;
          case 'email-approvals':
            showEmailApprovals();
            break;
          case 'settings':
            showSettings();
            break;
          case 'qris-history':
            loadQrisHistory();
            break;
          default:
            // Keep default state
            break;
        }
      }
    });

  </script>

  <style>
    /* Bootstrap Spinner for pagination loading */
    .spinner-border {
      display: inline-block;
      width: 1rem;
      height: 1rem;
      vertical-align: text-bottom;
      border: 0.125em solid currentColor;
      border-right-color: transparent;
      border-radius: 50%;
      animation: spinner-border 0.75s linear infinite;
    }
    
    .spinner-border-sm {
      width: 0.875rem;
      height: 0.875rem;
      border-width: 0.125em;
    }
    
    @keyframes spinner-border {
      to { transform: rotate(360deg); }
    }

    /* Active menu item styles */
    .nav-item.active {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1)) !important;
      border-color: #4caf50 !important;
      box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3) !important;
      transform: translateX(5px) !important;
    }
    
    /* User Management active style */
    #user-management-card.active {
      background: linear-gradient(135deg, rgba(79, 195, 247, 0.2), rgba(79, 195, 247, 0.1)) !important;
      border-color: #4fc3f7 !important;
      box-shadow: 0 4px 20px rgba(79, 195, 247, 0.3) !important;
    }
    
    /* Server Management active style */
    #server-management-card.active {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1)) !important;
      border-color: #4caf50 !important;
      box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3) !important;
    }
    
    /* Email Approvals active style */
    #email-approvals-card.active {
      background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(255, 152, 0, 0.1)) !important;
      border-color: #ff9800 !important;
      box-shadow: 0 4px 20px rgba(255, 152, 0, 0.3) !important;
    }
    
    /* Settings active style */
    #settings-card.active {
      background: linear-gradient(135deg, rgba(156, 39, 176, 0.2), rgba(156, 39, 176, 0.1)) !important;
      border-color: #9c27b0 !important;
      box-shadow: 0 4px 20px rgba(156, 39, 176, 0.3) !important;
    }
    
    /* QRIS History active style */
    #qris-history-card.active {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1)) !important;
      border-color: #4caf50 !important;
      box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3) !important;
    }
    
    /* Pulse animation for live indicator */
    @keyframes pulse {
      0% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.5;
        transform: scale(1.2);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    
    /* Email Approvals Inline Styles */
    .admin-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      transition: all 0.3s ease;
    }
    
    .admin-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(79, 195, 247, 0.15);
      border-color: #4fc3f7;
    }
    
    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
      text-transform: uppercase;
      display: inline-block;
    }
    
    .status-pending {
      background: rgba(255, 152, 0, 0.2);
      color: #ff9800;
      border: 1px solid #ff9800;
    }
    
    .status-approved {
      background: rgba(76, 175, 80, 0.2);
      color: #4caf50;
      border: 1px solid #4caf50;
    }
    
    .status-rejected {
      background: rgba(244, 67, 54, 0.2);
      color: #f44336;
      border: 1px solid #f44336;
    }
    
    .btn-approve {
      background: linear-gradient(45deg, #4caf50, #45a049);
      border: none;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-approve:hover {
      background: linear-gradient(45deg, #45a049, #3d8b40);
      transform: translateY(-1px);
      color: white;
    }
    
    .btn-reject {
      background: linear-gradient(45deg, #f44336, #d32f2f);
      border: none;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-reject:hover {
      background: linear-gradient(45deg, #d32f2f, #b71c1c);
      transform: translateY(-1px);
      color: white;
    }
  </style>



  <div style="position: relative; bottom: 0; width: 100%; margin-top: auto;">

    <?php include '../components/footer.php'; ?>

  </div>



  



  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <!-- Vendor JS Files -->

  <script src="../assets/vendor/aos/aos.js"></script>

  <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

  <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>

  <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

  <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>

  <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>

  <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>

  <script src="../assets/vendor/php-email-form/validate.js"></script>



  <!-- Template Main JS File -->

  <script src="../assets/js/main.js"></script>



  <!-- Sweetalert2 -->

  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



  <script>

    // Fungsi konfirmasi reusable dengan Sweetalert2

    function showDeleteConfirmation(title, text, callback) {

        const swalConfig = {

            title: title,

            text: text,

            icon: 'warning',

            showCancelButton: true,

            confirmButtonColor: '#d33',

            cancelButtonColor: 'transparent',

            confirmButtonText: 'Ya, Hapus!',

            cancelButtonText: 'Batal',

            allowOutsideClick: true,

            allowEscapeKey: true,

            allowEnterKey: true,

            stopKeydownPropagation: false,

            showCloseButton: true,

            closeButtonHtml: '&times;',

            focusConfirm: false,

            returnFocus: false,

            heightAuto: false,

            customClass: {

                container: 'swal2-container',

                popup: 'swal2-popup animated fadeInDown',

                header: 'swal2-header',

                title: 'swal2-title',

                closeButton: 'swal2-close',

                icon: 'swal2-icon',

                image: 'swal2-image',

                content: 'swal2-content',

                input: 'swal2-input',

                actions: 'swal2-actions',

                confirmButton: 'swal2-confirm',

                cancelButton: 'swal2-cancel',

                footer: 'swal2-footer'

            }

        };



        Swal.fire(swalConfig).then((result) => {

            if (result.isConfirmed) {

                callback();

            }

        });

    }



    // Fungsi untuk menghapus user

    function deleteUser(userId) {

        showDeleteConfirmation(

            'Hapus User?',

            'Apakah Anda yakin ingin menghapus user ini? Tindakan ini tidak dapat dibatalkan.',

            function() {

                fetch('api/delete_user.php', {

                    method: 'POST',

                    headers: {

                        'Content-Type': 'application/json',

                    },

                    body: JSON.stringify({ user_id: userId })

                })

                .then(response => response.json())

                .then(data => {

                    if (data.success) {

                        Swal.fire({

                            title: 'Berhasil!',

                            text: 'User telah dihapus.',

                            icon: 'success',

                            background: 'rgba(0, 0, 0, 0.9)',

                            color: '#fff'

                        }).then(() => {

                            location.reload();

                        });

                    } else {

                        Swal.fire({

                            title: 'Gagal!',

                            text: data.message || 'Terjadi kesalahan saat menghapus user.',

                            icon: 'error',

                            background: 'rgba(0, 0, 0, 0.9)',

                            color: '#fff'

                        });

                    }

                });

            }

        );

    }



    // Fungsi untuk menghapus server

    function deleteServer(serverId) {

        showDeleteConfirmation(

            'Hapus Server?',

            'Apakah Anda yakin ingin menghapus server ini? Tindakan ini tidak dapat dibatalkan.',

            function() {

                fetch('api/delete_server.php', {

                    method: 'POST',

                    headers: {

                        'Content-Type': 'application/json',

                    },

                    body: JSON.stringify({ server_id: serverId })

                })

                .then(response => response.json())

                .then(data => {

                    if (data.success) {

                        Swal.fire({

                            title: 'Berhasil!',

                            text: 'Server telah dihapus.',

                            icon: 'success',

                            background: 'rgba(0, 0, 0, 0.9)',

                            color: '#fff'

                        }).then(() => {

                            location.reload();

                        });

                    } else {

                        Swal.fire({

                            title: 'Gagal!',

                            text: data.message || 'Terjadi kesalahan saat menghapus server.',

                            icon: 'error',

                            background: 'rgba(0, 0, 0, 0.9)',

                            color: '#fff'

                        });

                    }

                });

            }

        );

    }



    // Fungsi untuk menghapus permintaan

    function deleteRequest(requestId) {

        showDeleteConfirmation(

            'Hapus Permintaan?',

            'Apakah Anda yakin ingin menghapus permintaan ini? Tindakan ini tidak dapat dibatalkan dan akan menghapus permintaan secara permanen.',

            function() {

                // Implementasi penghapusan permintaan

                // Sesuaikan dengan endpoint API yang sesuai

            }

        );

    }

  </script>



  <script>

    // Replace onclick handlers in HTML

    document.addEventListener('DOMContentLoaded', function() {

        // For user deletion buttons

        document.querySelectorAll('[onclick*="confirm"][onclick*="delete this user"]').forEach(button => {

            const userId = button.getAttribute('data-user-id');

            button.onclick = (e) => {

                e.preventDefault();

                deleteUser(userId);

            };

        });



        // For server deletion buttons

        document.querySelectorAll('[onclick*="confirm"][onclick*="delete this server"]').forEach(button => {

            const serverId = button.getAttribute('data-server-id');

            button.onclick = (e) => {

                e.preventDefault();

                deleteServer(serverId);

            };

        });



        // For request deletion buttons

        document.querySelectorAll('[onclick*="confirm"][onclick*="menghapus permintaan"]').forEach(button => {

            const requestId = button.getAttribute('data-request-id');

            button.onclick = (e) => {

                e.preventDefault();

                deleteRequest(requestId);

            };

        });

    });

  </script>



</body>



</html>
