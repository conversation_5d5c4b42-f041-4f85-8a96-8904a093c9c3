# Force correct MIME types for assets
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType font/ttf .ttf
    AddType font/otf .otf
    AddType font/eot .eot
</IfModule>

# Disable PHP execution in assets folder for security
<Files "*.php">
    Order allow,deny
    Deny from all
</Files>

# Allow access to static files
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|otf|eot|map)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Enable GZIP compression for this folder
<IfModule mod_deflate.c>
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|ico)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# Assets - Force Correct MIME Types
# Ensure CSS and font files are served with correct headers

# CSS Files
<FilesMatch "\.(css)$">
    ForceType text/css
    Header set Content-Type "text/css; charset=utf-8"
    Header unset X-Content-Type-Options
</FilesMatch>

# Font Files
<FilesMatch "\.(woff2)$">
    ForceType font/woff2
    Header set Content-Type "font/woff2"
</FilesMatch>

<FilesMatch "\.(woff)$">
    ForceType font/woff
    Header set Content-Type "font/woff"
</FilesMatch>

<FilesMatch "\.(ttf)$">
    ForceType font/ttf
    Header set Content-Type "font/ttf"
</FilesMatch>

<FilesMatch "\.(eot)$">
    ForceType application/vnd.ms-fontobject
    Header set Content-Type "application/vnd.ms-fontobject"
</FilesMatch>

# JavaScript Files
<FilesMatch "\.(js)$">
    ForceType application/javascript
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

# Global CORS Headers for Assets
<IfModule mod_headers.c>
    # Enable CORS for all asset files
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Accept, Origin, X-Requested-With"
    Header always set Access-Control-Max-Age "3600"
    
    # Specific headers for font files
    <FilesMatch "\.(woff|woff2|ttf|eot|svg)$">
        Header set Cache-Control "public, max-age=31536000"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
    
    # Headers for CSS files
    <FilesMatch "\.css$">
        Header set Content-Type "text/css; charset=utf-8"
        Header set Cache-Control "public, max-age=3600"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
    
    # Headers for JS files
    <FilesMatch "\.js$">
        Header set Content-Type "application/javascript; charset=utf-8"
        Header set Cache-Control "public, max-age=3600"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# Handle OPTIONS requests for CORS preflight
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle CORS preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
    
    # Ensure proper file serving
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . - [R=404,L]
</IfModule> 