// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading indicator and show content
    setTimeout(function() {
        document.getElementById('loading-indicator').style.display = 'none';
        document.querySelector('.server-content').classList.add('loaded');
        
        // Initialize AOS after content is shown
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });
    }, 500); // Small delay to show loading indicator briefly
});

// Filter functionality
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        this.classList.add('active');
        
        const filter = this.dataset.filter;
        const serverItems = document.querySelectorAll('.server-item');
        
        serverItems.forEach(item => {
            if (filter === 'all' || item.dataset.status === filter) {
                item.style.display = 'block';
                item.style.opacity = '0';
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transition = 'opacity 0.4s ease-in-out';
                }, 50);
            } else {
                item.style.display = 'none';
            }
        });
    });
});

// Refresh status function
function refreshStatus() {
    // Add rotation animation to refresh button
    const refreshBtn = document.querySelector('.refresh-btn');
    refreshBtn.style.transform = 'scale(1.1) rotate(360deg)';
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    
    // Use AJAX refresh instead of page reload
    refreshServerStatus();
}

// AJAX refresh function
function refreshServerStatus() {
    fetch(window.location.href)
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const newDoc = parser.parseFromString(html, 'text/html');
            
            // Update server cards
            const newServerContainer = newDoc.querySelector('#server-container');
            const currentServerContainer = document.querySelector('#server-container');
            if (newServerContainer && currentServerContainer) {
                // Remove AOS attributes from new content to prevent animation replay
                const newCards = newServerContainer.querySelectorAll('[data-aos]');
                newCards.forEach(card => {
                    card.removeAttribute('data-aos');
                    card.removeAttribute('data-aos-delay');
                });
                
                // Update content instantly without any animation
                currentServerContainer.innerHTML = newServerContainer.innerHTML;
            }
            
            // Update overall statistics
            const newStats = newDoc.querySelector('.overall-stats');
            const currentStats = document.querySelector('.overall-stats');
            if (newStats && currentStats) {
                currentStats.innerHTML = newStats.innerHTML;
            }
            
            // Show refresh indicator
            showRefreshIndicator();
            
            console.log('Server status updated at:', new Date().toLocaleTimeString());
        })
        .catch(error => {
            console.error('Error refreshing server status:', error);
        });
}

// Show refresh indicator
function showRefreshIndicator() {
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-check"></i>';
        refreshBtn.style.transform = 'scale(1)';
        refreshBtn.style.background = 'linear-gradient(135deg, #4caf50, #45a049)';
        
        setTimeout(() => {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshBtn.style.background = 'linear-gradient(135deg, #4fc3f7, #29b6f6)';
        }, 2000);
    }
}

// Auto refresh every 30 seconds with visual indicator
setInterval(function() {
    // Show subtle background refresh indicator
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.style.opacity = '0.7';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
        
        setTimeout(() => {
            refreshBtn.style.opacity = '1';
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
        }, 1000);
    }
    
    refreshServerStatus();
    console.log('Background refresh at:', new Date().toLocaleTimeString());
}, 30000);