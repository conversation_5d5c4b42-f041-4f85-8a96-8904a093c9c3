<?php
header('Content-Type: application/json');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../../database/database.php';
include '../../includes/config.php';

// Function to send Discord webhook
function sendDiscordWebhook($webhook_url, $title, $description, $color = 3447003, $fields = [], $file_path = null) {
    $embed = [
        'title' => $title,
        'description' => $description,
        'color' => $color,
        'timestamp' => date('c'),
        'footer' => [
            'text' => 'Payment System • ' . date('d/m/Y H:i:s')
        ]
    ];
    
    if (!empty($fields)) {
        $embed['fields'] = $fields;
    }
    
    // If there's an image file, add it to the embed
    if ($file_path) {
        // Create absolute path for file existence check
        $absolute_file_path = $file_path;
        if (!file_exists($absolute_file_path)) {
            // Try with document root prefix if relative path
            $document_root = $_SERVER['DOCUMENT_ROOT'] ?? 'c:/xampp/htdocs';
            if (empty($document_root)) {
                $document_root = 'c:/xampp/htdocs';
            }
            $absolute_file_path = rtrim($document_root, '/\\') . '/' . ltrim($file_path, '/\\');
        }
        
        if (file_exists($absolute_file_path)) {
            // Create a public URL for the image
            $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
            $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $host;
            
            // Use the relative path directly for URL
            $image_url = $base_url . '/' . ltrim(str_replace('\\', '/', $file_path), '/');
            $embed['image'] = ['url' => $image_url];
        }
    }
    
    $data = [
        'embeds' => [$embed]
    ];
    
    $ch = curl_init($webhook_url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $http_code >= 200 && $http_code < 300;
}

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $product_id = $_POST['product_id'] ?? null;
    $server_name = $_POST['server_name'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $payment_method = $_POST['payment_method'] ?? null;
    $paket = $_POST['paket'] ?? null;
    $email = $_SESSION['email'];

    // Validate required fields
    if (!$product_id || !$server_name || !$amount || !$payment_method) {
        throw new Exception('Data tidak lengkap');
    }

    // Check if server name already exists
    $check_server = $conn->prepare("SELECT id FROM `order` WHERE nama_server = ?");
    $check_server->bind_param("s", $server_name);
    $check_server->execute();
    $result = $check_server->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Nama server sudah digunakan. Silakan pilih nama lain.');
    }

    // Handle file upload for manual payment
    $proof_file_path = null;
    if ($payment_method === 'MANUAL PAYMENT' && isset($_FILES['proof_file'])) {
        $file = $_FILES['proof_file'];
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../../uploads/payment_proofs/';
            
            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Generate unique filename
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '_' . time() . '.' . $file_extension;
            $proof_file_path = $upload_dir . $unique_filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $proof_file_path)) {
                throw new Exception('Gagal mengupload bukti pembayaran');
            }
            
            // Store relative path for database
            $proof_file_path = 'uploads/payment_proofs/' . $unique_filename;
        } else {
            throw new Exception('Error uploading file');
        }
    }

    // Determine initial status
    if ($payment_method === 'QRIS') {
        $status_bayar = 'Menunggu Pembayaran';
    } else {
        $status_bayar = $proof_file_path ? 'Menunggu Verifikasi' : 'Menunggu Pembayaran';
    }

    // Generate random order ID
    $random_order_id = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);

    // Insert order into database
    $insert_order = $conn->prepare("
        INSERT INTO `order` (id_order, id_produk, email, nama_server, paket, status_bayar, pembayaran, bukti_pembayaran, jumlah_bayar, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $insert_order->bind_param("sissssssi", $random_order_id, $product_id, $email, $server_name, $paket, $status_bayar, $payment_method, $proof_file_path, $amount);
    
    if (!$insert_order->execute()) {
        throw new Exception('Gagal menyimpan pesanan: ' . $conn->error);
    }
    
    $order_id = $conn->insert_id;

    // Simpan order_id ke session
    $_SESSION['order_id'] = $order_id;

    // Send Discord webhook for manual payment upload
    if ($payment_method === 'MANUAL PAYMENT' && $proof_file_path) {
        $webhook_url = 'https://discordapp.com/api/webhooks/1390102004143226921/IJep37TO0nhBpDLRgDtOXzcq6m8v5JfhlYMHexX10CLmOjd4C0-WfCUoSROReBMvMwpT';
        
        // Get product name
        $product_query = $conn->prepare("SELECT nama FROM produk WHERE id_produk = ?");
        $product_query->bind_param("s", $product_id);
        $product_query->execute();
        $product_result = $product_query->get_result();
        $product_name = $product_result->fetch_assoc()['nama'] ?? 'Unknown Product';
        
        $fields = [
            [
                'name' => '💳 Order ID',
                'value' => '#' . $order_id,
                'inline' => true
            ],
            [
                'name' => '📦 Produk',
                'value' => $product_name,
                'inline' => true
            ],
            [
                'name' => '🖥️ Server Name',
                'value' => $server_name,
                'inline' => true
            ],
            [
                'name' => '💰 Jumlah',
                'value' => 'Rp ' . number_format($amount, 0, ',', '.'),
                'inline' => true
            ],
            [
                'name' => '👤 Email',
                'value' => $email,
                'inline' => true
            ],
            [
                'name' => '📄 Status',
                'value' => 'Menunggu Verifikasi',
                'inline' => true
            ]
        ];
        
        if ($paket) {
            $fields[] = [
                'name' => '🗳️ Paket',
                'value' => $paket,
                'inline' => true
            ];
        }
        
        sendDiscordWebhook(
            $webhook_url,
            '📤 Bukti Pembayaran Manual Diterima',
            'Pelanggan telah mengupload bukti pembayaran dan menunggu verifikasi admin.',
            3447003, // Blue color
            $fields,
            $proof_file_path // Pass the file path for image attachment
        );
    }

    // For QRIS payment, the status will be updated automatically by QRIS webhook
    // No need to insert into log_qris table here as it will be handled by the QRIS system

    echo json_encode([
        'success' => true, 
        'message' => 'Pesanan berhasil dibuat',
        'order_id' => $order_id
    ]);

} catch (Exception $e) {
    error_log("Process order error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>