<?php
// Include dynamic path configuration
require_once __DIR__ . '/../includes/config.php';

// Check if user is logged in (session already started in config.php)
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Database connection and stats
require_once '../database/database.php';
$email = $_SESSION['email'];

// Initialize variables
$hosting_count = 0;
$active_count = 0;
$offline_count = 0;

// Debug: Check if database connection exists
if ($conn) {
    try {
        // Get total hosting count from data_client table (same as myhosting)
        $query1 = "SELECT COUNT(*) as total FROM data_client WHERE email = ?";
        $stmt1 = $conn->prepare($query1);
        $stmt1->bind_param("s", $email);
        $stmt1->execute();
        $result1 = $stmt1->get_result();
        if ($row = $result1->fetch_assoc()) {
            $hosting_count = intval($row['total']);
        }
        
        // Get active hosting count (expired > NOW())
        $query2 = "SELECT COUNT(*) as active FROM data_client WHERE email = ? AND expired > NOW()";
        $stmt2 = $conn->prepare($query2);
        $stmt2->bind_param("s", $email);
        $stmt2->execute();
        $result2 = $stmt2->get_result();
        if ($row = $result2->fetch_assoc()) {
            $active_count = intval($row['active']);
        }
        
        // Calculate offline/expired count
        $offline_count = $hosting_count - $active_count;
        
        // Debug: Log the values
        error_log("Dashboard Debug - Email: $email, Total: $hosting_count, Active: $active_count, Expired: $offline_count");
        
    } catch (Exception $e) {
        error_log("Dashboard Error: " . $e->getMessage());
        // If there's an error, keep counts at 0
        $hosting_count = 0;
        $active_count = 0;
        $offline_count = 0;
    }
} else {
    error_log("Dashboard Error: No database connection");
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Dashboard - Dopminer</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="<?php echo asset('vendor/aos/aos.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/bootstrap/css/bootstrap.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/bootstrap-icons/bootstrap-icons.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/boxicons/css/boxicons.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/glightbox/css/glightbox.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/remixicon/remixicon.css'); ?>" rel="stylesheet">
    <link href="<?php echo asset('vendor/swiper/swiper-bundle.min.css'); ?>" rel="stylesheet">
    
    <!-- Template Main CSS File -->
    <link href="<?php echo asset('css/style.css'); ?>" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Open Sans", sans-serif;
            background: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: "Jost", sans-serif;
        }
        
        .dashboard-container {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .welcome-card h1 {
            color: #4fc3f7;
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .welcome-card p {
            color: #b0b0b0;
            font-size: 1.1rem;
        }
        
        .stats-section {
            margin-bottom: 3rem;
        }
        
        .section-title {
            color: #4fc3f7;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: #4fc3f7;
            box-shadow: 0 10px 30px rgba(79, 195, 247, 0.2);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #4fc3f7;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .action-card:hover {
            transform: translateY(-3px);
            border-color: #4fc3f7;
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.15);
            color: inherit;
            text-decoration: none;
        }
        
        .action-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.2rem;
        }
        
        .action-title {
            color: #4fc3f7;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .action-desc {
            color: #b0b0b0;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        
        .info-section {
            backdrop-filter: blur(10px);
            margin-top: 3rem;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 2rem;
        }
        
        .info-title {
            color: #4fc3f7;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .info-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            flex-shrink: 0;
        }
        
        .info-content h6 {
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }
        
        .info-content small {
            color: #b0b0b0;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 100px 0 1rem 0;
            }
            
            .welcome-card {
                padding: 1.5rem;
                margin: 0 1rem 2rem 1rem;
            }
            
            .welcome-card h1 {
                font-size: 1.5rem;
            }
            
            .stats-grid,
            .actions-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .info-section {
                margin: 3rem 1rem 0 1rem;
            }
        }
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>

    <div class="dashboard-container">
        <div class="container">
            
            <!-- Welcome Section -->
            <div class="welcome-card" data-aos="fade-up" data-aos-delay="100">
                <h1>👋 Halo, <?php echo explode('@', $email)[0]; ?>!</h1>
                <p>Selamat datang di dashboard Dopminer. Kelola hosting Anda dengan mudah.</p>
            </div>

            <!-- Statistics -->
            <div class="stats-section" data-aos="fade-up" data-aos-delay="200">
                <h2 class="section-title">📊 Statistik Hosting</h2>
                
                <div class="stats-grid">
                    <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon">
                            <i class="bi bi-server"></i>
                        </div>
                        <div class="stat-number"><?php echo $hosting_count; ?></div>
                        <div class="stat-label">Total Hosting</div>
                    </div>
                    
                    <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-number"><?php echo $active_count; ?></div>
                        <div class="stat-label">Hosting Aktif</div>
                    </div>
                    
                    <div class="stat-card" data-aos="fade-up" data-aos-delay="500">
                        <div class="stat-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number"><?php echo $offline_count; ?></div>
                        <div class="stat-label">Hosting Expired</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-section" data-aos="fade-up" data-aos-delay="600">
                <h2 class="section-title">🚀 Menu Utama</h2>
                
                <div class="actions-grid">
                    <a href="myhosting" class="action-card" data-aos="zoom-in" data-aos-delay="700">
                        <div class="action-icon">
                            <i class="bi bi-hdd-stack"></i>
                        </div>
                        <div class="action-title">Hosting Saya</div>
                        <div class="action-desc">Kelola semua layanan hosting Anda</div>
                    </a>
                    
                    <a href="order_history" class="action-card" data-aos="zoom-in" data-aos-delay="800">
                        <div class="action-icon">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="action-title">Orderan Saya</div>
                        <div class="action-desc">Lihat riwayat dan status pesanan</div>
                    </a>
                    
                    <a href="serverlist" class="action-card" data-aos="zoom-in" data-aos-delay="900">
                        <div class="action-icon">
                            <i class="bi bi-activity"></i>
                        </div>
                        <div class="action-title">Status Server</div>
                        <div class="action-desc">Cek status semua server</div>
                    </a>
                    
                    <a href="games" class="action-card" data-aos="zoom-in" data-aos-delay="1000">
                        <div class="action-icon">
                            <i class="bi bi-controller"></i>
                        </div>
                        <div class="action-title">Game Server</div>
                        <div class="action-desc">Kelola server game FiveM, SAMP, dll</div>
                    </a>
                    
                </div>
            </div>

            <!-- Info Section -->
            <div class="info-section" data-aos="fade-up" data-aos-delay="1100">
                <div class="info-title">
                    <i class="bi bi-info-circle"></i>
                    Informasi Layanan
                </div>
                
                <div class="info-item">
                    <div class="info-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="info-content">
                        <h6>Keamanan Terjamin</h6>
                        <small>Server dilindungi dengan sistem keamanan berlapis dan monitoring 24/7</small>
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-icon">
                        <i class="bi bi-headset"></i>
                    </div>
                    <div class="info-content">
                        <h6>Support Professional</h6>
                        <small>Tim support siap membantu Anda kapan saja melalui live chat</small>
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-icon">
                        <i class="bi bi-lightning"></i>
                    </div>
                    <div class="info-content">
                        <h6>Performa Optimal</h6>
                        <small>Teknologi server terdepan untuk performa hosting yang maksimal</small>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="<?php echo asset('vendor/aos/aos.js'); ?>"></script>
    <script src="<?php echo asset('vendor/bootstrap/js/bootstrap.bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('vendor/glightbox/js/glightbox.min.js'); ?>"></script>
    <script src="<?php echo asset('vendor/isotope-layout/isotope.pkgd.min.js'); ?>"></script>
    <script src="<?php echo asset('vendor/swiper/swiper-bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('vendor/waypoints/noframework.waypoints.js'); ?>"></script>
    <script src="<?php echo asset('vendor/purecounter/purecounter_vanilla.js'); ?>"></script>
    <script src="<?php echo asset('vendor/php-email-form/validate.js'); ?>"></script>

    <!-- Template Main JS File -->
    <script src="<?php echo asset('js/main.js'); ?>"></script>

</body>

</html>