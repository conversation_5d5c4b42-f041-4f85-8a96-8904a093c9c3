<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Jika ada product_id di GET, simpan ke session lalu redirect tanpa parameter
if (isset($_GET['product_id'])) {
    $product_id = filter_input(INPUT_GET, 'product_id', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $paket = filter_input(INPUT_GET, 'paket', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $price_index = filter_input(INPUT_GET, 'price_index', FILTER_VALIDATE_INT);
    if ($product_id) {
        $_SESSION['product_id'] = $product_id;
        if ($paket) {
            $_SESSION['selected_paket'] = $paket;
        }
        if ($price_index !== false && $price_index !== null) {
            $_SESSION['price_index'] = $price_index;
        }
        header('Location: configure');
        exit;
    } else {
        header('Location: products');
        exit;
    }
}

// Ambil product_id dari session
$product_id = $_SESSION['product_id'] ?? null;
$email = $_SESSION['email'] ?? '';
if (!$product_id) {
    header('Location: products');
    exit;
}

// Fetch product details
try {
    $query = "SELECT * FROM produk WHERE id_produk = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $product_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Location: products');
        exit;
    }
    
    $product = $result->fetch_assoc();
} catch (Exception $e) {
    error_log("Configure error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat memuat data produk.";
    header('Location: products');
    exit;
}

// Note: Removed general product validation to allow multiple orders for different servers
// Specific server validation will be done when user submits the form

// Generate unique amount (add random 3 digits to price)
$unique_code = rand(100, 999);

// Get price based on selected paket index
$price_index = $_SESSION['price_index'] ?? 0;
$normalPrices = explode(',', $product['harga_normal']);
$discountPrices = $product['harga_diskon'] ? explode(',', $product['harga_diskon']) : null;

// Get the price for the selected paket
$selectedNormalPrice = isset($normalPrices[$price_index]) ? intval(trim($normalPrices[$price_index])) : intval(trim($normalPrices[0]));
$selectedDiscountPrice = null;
if ($discountPrices && isset($discountPrices[$price_index])) {
    $selectedDiscountPrice = intval(trim($discountPrices[$price_index]));
}

$unique_amount = $selectedDiscountPrice ? $selectedDiscountPrice + $unique_code : $selectedNormalPrice + $unique_code;

// Proses submit konfigurasi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $server_name = trim($_POST['server_name'] ?? '');
    $unique_amount = intval($_POST['unique_amount'] ?? 0);
    $selected_paket = $_SESSION['selected_paket'] ?? null;
    
    if ($server_name && $unique_amount && $selected_paket) {
        // Check again if there's already a paid or pending verification order for this specific server
        try {
            $check_server_query = "SELECT status_bayar FROM `order` WHERE email = ? AND id_produk = ? AND nama_server = ? AND (status_bayar = 'Sudah Dibayar' OR status_bayar = 'Menunggu Verifikasi') ORDER BY created_at DESC LIMIT 1";
            $check_server_stmt = $conn->prepare($check_server_query);
            $check_server_stmt->bind_param("sss", $email, $product_id, $server_name);
            $check_server_stmt->execute();
            $check_server_result = $check_server_stmt->get_result();
            
            if ($check_server_result->num_rows > 0) {
                $existing_server_order = $check_server_result->fetch_assoc();
                $_SESSION['error'] = "Server '{$server_name}' sudah memiliki order dengan status '{$existing_server_order['status_bayar']}'. Silakan gunakan nama server lain atau cek riwayat order Anda.";
                header('Location: configure');
                exit;
            }
        } catch (Exception $e) {
            error_log("Configure server validation error: " . $e->getMessage());
        }
        
        $_SESSION['server_name'] = $server_name;
        $_SESSION['unique_amount'] = $unique_amount;
        header('Location: payment');
        exit;
    } else {
        $_SESSION['error'] = 'Nama server, paket, dan jumlah pembayaran tidak valid.';
        header('Location: configure');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Configure Order - Dopminer</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
        }
        
        .configure-container {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        
        .configure-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }
        
        .configure-card h2 {
            color: #4fc3f7;
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .product-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-label {
            color: #e0e0e0;
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4fc3f7;
            box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.25);
            color: white;
        }
        
        .form-control::placeholder {
            color: #b0b0b0;
        }
        
        .btn-order {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
            width: 100%;
        }
        
        .btn-order:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
        }
        
        .price-display {
            background: rgba(79, 195, 247, 0.1);
            border: 1px solid rgba(79, 195, 247, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .unique-code-info {
            background: rgba(79, 195, 247, 0.1);
            border: 1px solid rgba(79, 195, 247, 0.2);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            color: #4fc3f7;
        }
        
        .spec-item {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .spec-item:last-child {
            border-bottom: none;
        }
        
        .spec-item i {
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .configure-container {
                padding: 100px 0 1rem 0;
            }
            
            .configure-card {
                padding: 1.5rem;
                margin: 0 1rem;
            }
            
            .configure-card h2 {
                font-size: 1.5rem;
            }
        }
        
        #header {
            transition: background-color 0.3s ease;
        }
        
        #header.scrolled {
            background-color: #171717c9;
        }
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="configure-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="configure-card">
                        <h2 class="text-center text-white mb-4">
                            <i class="fas fa-cog"></i> Configure Your Order
                        </h2>
                        
                        <!-- Product Info -->
                        <div class="product-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <?php if ($product['image_links']): ?>
                                        <?php $images = explode('|', $product['image_links']); ?>
                                        <img src="<?php echo htmlspecialchars(trim($images[0])); ?>" 
                                             alt="<?php echo htmlspecialchars($product['nama']); ?>"
                                             class="img-fluid rounded">
                                    <?php else: ?>
                                        <?php $gambar_array = explode('|', $product['gambar']); ?>
                                        <img src="<?php echo htmlspecialchars(trim($gambar_array[0])); ?>" 
                                             alt="<?php echo htmlspecialchars($product['nama']); ?>"
                                             class="img-fluid rounded">
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-8">
                                    <h4 class="text-white"><?php echo htmlspecialchars($product['nama']); ?></h4>
                                    <p class="text-light"><?php echo htmlspecialchars($product['type']); ?></p>
                                    <?php if (isset($_SESSION['selected_paket'])): ?>
                                        <div class="mb-3">
                                            <span class="badge" style="background: #4fc3f7; color: white; padding: 8px 15px; border-radius: 20px; font-size: 12px;">
                                                <i class="fas fa-users"></i> <?php echo htmlspecialchars($_SESSION['selected_paket']); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Specifications Section -->
                        <div class="product-info">
                            <h5 class="text-white mb-3">
                                <i class="fas fa-list-ul"></i> Spesifikasi
                            </h5>
                            <div class="row">
                                <?php
                                if (isset($product['spesifikasi'])) {
                                    $specs = explode("\n", $product['spesifikasi']);
                                    $spec_icons = [
                                        'CPU' => 'fas fa-microchip',
                                        'RAM' => 'fas fa-memory', 
                                        'Bandwidth' => 'fas fa-wifi',
                                        'DDOS' => 'fas fa-shield-alt',
                                        'Speed' => 'fas fa-tachometer-alt'
                                    ];
                                    
                                    foreach ($specs as $spec) {
                                        $spec = trim($spec);
                                        if (!empty($spec)) {
                                            $icon = 'fas fa-check';
                                            foreach ($spec_icons as $key => $spec_icon) {
                                                if (stripos($spec, $key) !== false) {
                                                    $icon = $spec_icon;
                                                    break;
                                                }
                                            }
                                            echo '<div class="col-md-6 mb-2">';
                                            echo '<div class="spec-item d-flex align-items-center">';
                                            echo '<i class="' . $icon . ' text-primary me-2" style="width: 20px;"></i>';
                                            echo '<span class="text-light">' . htmlspecialchars($spec) . '</span>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                    }
                                }
                                ?>
                            </div>
                            </div>
                        </div>
                        
                        <!-- Order Form -->
                        <form id="orderForm" method="POST">
                            <input type="hidden" name="unique_amount" value="<?php echo $unique_amount; ?>">
                            <div class="mb-4">
                                <label for="server_name" class="form-label text-white">
                                    <i class="fas fa-server"></i> Nama Server
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="server_name" 
                                       name="server_name"
                                       placeholder="Masukkan nama server yang diinginkan"
                                       required>
                                <small class="text-light">Nama server harus unik dan akan digunakan sebagai identitas server Anda</small>
                            </div>
                            
                            <!-- Price Display -->
                            <div class="price-display">
                                <h5 class="text-white mb-2">Total Pembayaran</h5>
                                <?php if ($selectedDiscountPrice): ?>
                                    <div class="text-muted mb-2">
                                        <del>Rp <?php echo number_format($selectedNormalPrice, 0, ',', '.'); ?></del>
                                    </div>
                                <?php endif; ?>
                                <div class="h4 text-primary mb-0">
                                    Rp <?php echo number_format($unique_amount, 0, ',', '.'); ?>
                                </div>
                                
                                <div class="unique-code-info">
                                    <small class="text-warning">
                                        <i class="fas fa-info-circle"></i>
                                        Kode unik: +<?php echo $unique_code; ?> (untuk memudahkan verifikasi pembayaran)
                                    </small>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-order">
                                    <i class="fas fa-shopping-cart"></i> Lanjutkan ke Pembayaran
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Navbar Scroll Effect -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('header');
            
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });
    </script>
</body>
</html>