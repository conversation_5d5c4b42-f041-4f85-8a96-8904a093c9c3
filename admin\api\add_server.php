<?php
// Suppress all PHP errors and warnings to prevent HTML output
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Clear any output that might have been generated
ob_clean();

// Set JSON header
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    ob_end_flush();
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    ob_clean();
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    ob_end_flush();
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    ob_clean();
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    ob_end_flush();
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input']);
    ob_end_flush();
    exit;
}

// Extract and sanitize input data (all fields optional except id which is auto-increment)
$server_email = isset($input['email']) ? trim($input['email']) : '';
$namakota = isset($input['namakota']) ? trim($input['namakota']) : '';
$typehosting = isset($input['typehosting']) ? trim($input['typehosting']) : '';
$iphosting = isset($input['iphosting']) ? trim($input['iphosting']) : '';
$pakethosting = isset($input['pakethosting']) ? trim($input['pakethosting']) : '';
$expired = isset($input['expired']) ? trim($input['expired']) : null;
$harganormal = isset($input['harganormal']) ? intval($input['harganormal']) : 0;
$hargabulanan = isset($input['hargabulanan']) ? intval($input['hargabulanan']) : 0;
$catatan = isset($input['catatan']) ? trim($input['catatan']) : '';
$order_id = isset($input['order_id']) ? trim($input['order_id']) : '';

// Optional validation for email format (only if email is provided)
if (!empty($server_email) && !filter_var($server_email, FILTER_VALIDATE_EMAIL)) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['error' => 'Invalid email format']);
    ob_end_flush();
    exit;
}

// Optional validation for date format (only if date is provided)
if (!empty($expired) && !DateTime::createFromFormat('Y-m-d', $expired)) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['error' => 'Invalid date format. Use YYYY-MM-DD']);
    ob_end_flush();
    exit;
}

try {
    // Check if server with same id_order already exists (if order_id is provided)
    if (!empty($order_id)) {
        $check_stmt = $conn->prepare("SELECT id FROM data_client WHERE id_order = ?");
        $check_stmt->bind_param("s", $order_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            ob_clean();
            echo json_encode(['success' => false, 'message' => 'Server untuk order ini sudah ada']);
            ob_end_flush();
            exit;
        }
    } else {
        // Fallback: Check if server with same email and name already exists
        $check_stmt = $conn->prepare("SELECT id FROM data_client WHERE email = ? AND namakota = ?");
        $check_stmt->bind_param("ss", $server_email, $namakota);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            ob_clean();
            echo json_encode(['success' => false, 'message' => 'Server dengan email dan nama yang sama sudah ada']);
            ob_end_flush();
            exit;
        }
    }
    
    // Menggunakan angka random sederhana untuk id
    $unique_id = (string)mt_rand(1000000, 9999999); // Angka random sederhana
    
    // Generate token (optional, can be empty)
    $token = '';
    
    // Insert new server into data_client table
    $insert_stmt = $conn->prepare("
        INSERT INTO data_client 
        (id, email, typehosting, harganormal, hargabulanan, namakota, expired, pakethosting, iphosting, catatan, token, id_order) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $insert_stmt->bind_param(
        "sssiisssssss", 
        $unique_id,
        $server_email, 
        $typehosting, 
        $harganormal, 
        $hargabulanan, 
        $namakota, 
        $expired, 
        $pakethosting, 
        $iphosting, 
        $catatan, 
        $token,
        $order_id
    );
    
    if ($insert_stmt->execute()) {
        $server_id = $unique_id; // Use the generated unique ID
        
        // Log the action
        error_log("Server added by admin {$email}: ID {$server_id}, Email: {$server_email}, Name: {$namakota}, Order ID: {$order_id}");
        
        // Clear output buffer and send clean JSON response
        ob_clean();
        echo json_encode([
            'success' => true, 
            'message' => 'Server berhasil ditambahkan',
            'server_id' => $server_id,
            'order_id' => $order_id
        ]);
        ob_end_flush();
        exit;
    } else {
        // Enhanced error logging
        $error_msg = $conn->error;
        error_log("Database insert error in add_server.php: " . $error_msg);
        error_log("Data being inserted: email={$server_email}, typehosting={$typehosting}, harganormal={$harganormal}, hargabulanan={$hargabulanan}, namakota={$namakota}, expired={$expired}, order_id={$order_id}");
        throw new Exception('Failed to insert server: ' . $error_msg);
    }
    
} catch (Exception $e) {
    error_log("Add server error: " . $e->getMessage());
    ob_clean();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan saat menambahkan server']);
    ob_end_flush();
    exit;
}

// Clean up output buffer
ob_end_clean();
?>