<?php
session_start();

// Check if the GD library is loaded
if (!extension_loaded('gd')) {
    // Fallback to arithmetic captcha
    $num1 = rand(1, 10);
    $num2 = rand(1, 10);
    $operators = ['+', '-'];
    $operator = $operators[rand(0, 1)];
    
    // Calculate the correct answer
    switch ($operator) {
        case '+':
            $answer = $num1 + $num2;
            break;
        case '-':
            // Ensure positive result
            if ($num1 < $num2) {
                $temp = $num1;
                $num1 = $num2;
                $num2 = $temp;
            }
            $answer = $num1 - $num2;
            break;
    }
    
    // Store the CAPTCHA answer in session
    $_SESSION['captcha'] = $answer;
    
    // Generate SVG captcha as fallback
    $svg_captcha = '
    <svg width="150" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="150" height="40" fill="#212529" stroke="#454545" stroke-width="2"/>
      <text x="75" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" 
            fill="#46aedf" text-anchor="middle" dominant-baseline="middle">
        ' . $num1 . ' ' . $operator . ' ' . $num2 . ' = ?
      </text>
      <!-- Add some noise lines for security -->
      <line x1="10" y1="5" x2="25" y2="15" stroke="#666" stroke-width="1" opacity="0.5"/>
      <line x1="125" y1="10" x2="140" y2="20" stroke="#666" stroke-width="1" opacity="0.5"/>
      <line x1="30" y1="35" x2="45" y2="25" stroke="#666" stroke-width="1" opacity="0.5"/>
      <line x1="105" y1="30" x2="120" y2="35" stroke="#666" stroke-width="1" opacity="0.5"/>
    </svg>';
    
    // Set content type to SVG
    header('Content-Type: image/svg+xml');
    echo $svg_captcha;
    exit;
}

// Generate a random number for CAPTCHA (GD version)
$captcha_code = rand(1000, 9999);

// Store the CAPTCHA code in session
$_SESSION['captcha'] = $captcha_code;

// Create an image
$image = imagecreatetruecolor(120, 40);

// Check if image creation was successful
if (!$image) {
    die('Failed to create image.');
}

// Set colors
$bg_color = imagecolorallocate($image, 33, 37, 41);
$text_color = imagecolorallocate($image, 70, 174, 223);
$noise_color = imagecolorallocate($image, 102, 102, 102);

// Check if colors were allocated successfully
if ($bg_color === false || $text_color === false || $noise_color === false) {
    imagedestroy($image);
    die('Failed to allocate colors.');
}

// Fill the background
if (!imagefilledrectangle($image, 0, 0, 120, 40, $bg_color)) {
    imagedestroy($image);
    die('Failed to fill the background.');
}

// Add some noise for security
for ($i = 0; $i < 50; $i++) {
    imagesetpixel($image, rand(0, 120), rand(0, 40), $noise_color);
}

// Add noise lines
for ($i = 0; $i < 5; $i++) {
    imageline($image, rand(0, 120), rand(0, 40), rand(0, 120), rand(0, 40), $noise_color);
}

// Add the text
if (!imagestring($image, 5, 30, 12, $captcha_code, $text_color)) {
    imagedestroy($image);
    die('Failed to add text to the image.');
}

// Output the image
header('Content-type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

if (!imagepng($image)) {
    imagedestroy($image);
    die('Failed to output the image.');
}

// Free memory
imagedestroy($image);
?>
