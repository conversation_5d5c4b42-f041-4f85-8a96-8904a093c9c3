<?php
header('Content-Type: application/json');

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../../database/database.php';
include '../../includes/config.php';

// Function to send Discord webhook
function sendDiscordWebhook($webhook_url, $title, $description, $color = 3447003, $fields = [], $file_path = null) {
    $embed = [
        'title' => $title,
        'description' => $description,
        'color' => $color,
        'timestamp' => date('c'),
        'footer' => [
            'text' => 'Payment System • ' . date('d/m/Y H:i:s')
        ]
    ];
    
    if (!empty($fields)) {
        $embed['fields'] = $fields;
    }
    
    // If there's an image file, add it to the embed
    if ($file_path && file_exists($file_path)) {
        // Create a public URL for the image
        $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
        $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $host;
        
        // Convert absolute path to relative URL path
        $document_root = $_SERVER['DOCUMENT_ROOT'] ?? 'c:/xampp/htdocs';
        if (empty($document_root)) {
            $document_root = 'c:/xampp/htdocs';
        }
        $document_root = str_replace('\\', '/', $document_root);
        $file_path_normalized = str_replace('\\', '/', $file_path);
        $relative_path = str_replace($document_root, '', $file_path_normalized);
        $relative_path = ltrim($relative_path, '/');
        
        $image_url = $base_url . '/' . $relative_path;
        $embed['image'] = ['url' => $image_url];
    }
    
    $data = [
        'embeds' => [$embed]
    ];
    
    $ch = curl_init($webhook_url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $http_code >= 200 && $http_code < 300;
}

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $order_id = $_GET['order_id'] ?? null;
    $email = $_SESSION['email'];

    if (!$order_id) {
        throw new Exception('Order ID tidak ditemukan');
    }

    $order_query = $conn->prepare("SELECT * FROM `order` WHERE id = ? AND email = ?");
    $order_query->bind_param("is", $order_id, $email);
    $order_query->execute();
    $order_result = $order_query->get_result();
    
    if ($order_result->num_rows === 0) {
        throw new Exception('Pesanan tidak ditemukan');
    }
    
    $order = $order_result->fetch_assoc();

    if ($order['pembayaran'] === 'QRIS') {
        // Check if there's a successful payment in log_qris with matching amount
        $qris_query = $conn->prepare("
            SELECT * FROM log_qris 
            WHERE amount = ? AND type = 'CR' 
            ORDER BY created_at DESC LIMIT 1
        ");
        $qris_query->bind_param("d", $order['jumlah_bayar']);
        $qris_query->execute();
        $qris_result = $qris_query->get_result();
        
        if ($qris_result->num_rows > 0) {
            $qris_data = $qris_result->fetch_assoc();
            
            // Check if this payment hasn't been used by another order
            $check_used = $conn->prepare("
                SELECT id FROM `order` 
                WHERE pembayaran = 'QRIS' AND status_bayar = 'Sudah Dibayar' AND jumlah_bayar = ? AND id != ?
            ");
            $check_used->bind_param("di", $order['jumlah_bayar'], $order_id);
            $check_used->execute();
            $used_result = $check_used->get_result();
            
            if ($used_result->num_rows === 0) {
                // Payment not used by another order, mark this order as paid
                $update_order = $conn->prepare("UPDATE `order` SET status_bayar = 'Sudah Dibayar' WHERE id = ?");
                $update_order->bind_param("i", $order_id);
                $update_order->execute();
                
                $order['status_bayar'] = 'Sudah Dibayar';
                
                // Send Discord webhook for successful QRIS payment
                $webhook_url = 'https://discordapp.com/api/webhooks/1390102004143226921/IJep37TO0nhBpDLRgDtOXzcq6m8v5JfhlYMHexX10CLmOjd4C0-WfCUoSROReBMvMwpT';
                
                // Get product name
                $product_query = $conn->prepare("SELECT nama FROM produk WHERE id_produk = ?");
                $product_query->bind_param("s", $order['id_produk']);
                $product_query->execute();
                $product_result = $product_query->get_result();
                $product_name = $product_result->fetch_assoc()['nama'] ?? 'Unknown Product';
                
                $fields = [
                    [
                        'name' => '💳 Order ID',
                        'value' => '#' . $order_id,
                        'inline' => true
                    ],
                    [
                        'name' => '📦 Produk',
                        'value' => $product_name,
                        'inline' => true
                    ],
                    [
                        'name' => '🖥️ Server Name',
                        'value' => $order['nama_server'],
                        'inline' => true
                    ],
                    [
                        'name' => '💰 Jumlah',
                        'value' => 'Rp ' . number_format($order['jumlah_bayar'], 0, ',', '.'),
                        'inline' => true
                    ],
                    [
                        'name' => '👤 Email',
                        'value' => $order['email'],
                        'inline' => true
                    ],
                    [
                        'name' => '💳 Metode Pembayaran',
                        'value' => 'QRIS',
                        'inline' => true
                    ],
                    [
                        'name' => '🔗 Referensi',
                        'value' => $qris_data['buyer_reff'] ?? 'N/A',
                        'inline' => true
                    ],
                    [
                        'name' => '🏦 Bank',
                        'value' => $qris_data['brand_name'] ?? 'N/A',
                        'inline' => true
                    ],
                    [
                        'name' => '📄 Status',
                        'value' => '✅ Pembayaran Berhasil',
                        'inline' => true
                    ]
                ];
                
                if ($order['paket']) {
                    $fields[] = [
                        'name' => '🎮 Paket',
                        'value' => $order['paket'],
                        'inline' => true
                    ];
                }
                
                sendDiscordWebhook(
                    $webhook_url,
                    '💰 Pembayaran QRIS Berhasil!',
                    'Pembayaran QRIS telah berhasil dikonfirmasi dan pesanan telah diproses.',
                    3447003, // Blue color
                    $fields
                );
            }
        }
    }

    echo json_encode([
        'success' => true,
        'status' => $order['status_bayar'],
        'payment_method' => $order['pembayaran']
    ]);

} catch (Exception $e) {
    error_log("Check payment status error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>