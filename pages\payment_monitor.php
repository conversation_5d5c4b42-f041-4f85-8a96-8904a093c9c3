<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Ambil order_id dari URL parameter atau session
$order_id = $_GET['order_id'] ?? $_SESSION['order_id'] ?? null;
if (!$order_id) {
    header('Location: order_history');
    exit;
}

// Jika order_id dari URL parameter, simpan ke session untuk konsistensi
if (isset($_GET['order_id'])) {
    $_SESSION['order_id'] = intval($_GET['order_id']);
    $order_id = intval($_GET['order_id']);
}
$email = $_SESSION['email'];

// Fetch order details
try {
    $query = "SELECT o.*, p.nama as product_name FROM `order` o 
              JOIN produk p ON o.id_produk = p.id_produk 
              WHERE o.id = ? AND o.email = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("is", $order_id, $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Location: order_history');
        exit;
    }
    
    $order = $result->fetch_assoc();
    
    // Check if order is already paid - redirect to order history
    if ($order['status_bayar'] === 'Sudah Dibayar') {
        $_SESSION['error'] = "Order ini sudah dibayar. Silakan cek riwayat order Anda untuk detail lebih lanjut.";
        header('Location: order_history');
        exit;
    }
} catch (Exception $e) {
    error_log("Payment monitor error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat memuat data pesanan.";
    header('Location: order_history');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Payment Monitor - Dopminer</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
        }
        
        .monitor-container {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        
        .monitor-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }
        
        .monitor-card h2 {
            color: #4fc3f7;
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .status-pending {
            color: #ffc107;
        }
        
        .status-paid {
            color: #28a745;
        }
        
        .status-failed {
            color: #dc3545;
        }
        
        .qr-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin: 1.5rem 0;
        }
        
        .countdown {
            font-size: 2rem;
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .order-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .payment-success {
            background: rgba(79, 195, 247, 0.1);
            border: 1px solid rgba(79, 195, 247, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            display: none;
        }
        
        .payment-success.show {
            display: block;
        }
        
        .spinner {
            animation: spin 1s linear infinite;
            color: #4fc3f7;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-outline-light {
            border-color: #4fc3f7;
            color: #4fc3f7;
            transition: all 0.3s ease;
        }
        
        .btn-outline-light:hover {
            background-color: #4fc3f7;
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }
        
        .btn-success:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
        }
        
        @media (max-width: 768px) {
            .monitor-container {
                padding: 100px 0 1rem 0;
            }
            
            .monitor-card {
                padding: 1.5rem;
                margin: 0 1rem;
            }
            
            .monitor-card h2 {
                font-size: 1.5rem;
            }
        }
        
        #header {
            transition: background-color 0.3s ease;
        }
        
        #header.scrolled {
            background-color: #171717c9;
        }
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="monitor-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="monitor-card">
                        <h2 class="text-center text-white mb-4">
                            <i class="fas fa-credit-card"></i> Monitor Pembayaran
                        </h2>
                        
                        <!-- Order Info -->
                        <div class="order-info">
                            <h5 class="text-white mb-3">
                                <i class="fas fa-info-circle"></i> Informasi Pesanan
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-light mb-1"><strong>Produk:</strong> <?php echo htmlspecialchars($order['product_name']); ?></p>
                                    <p class="text-light mb-1"><strong>Server:</strong> <?php echo htmlspecialchars($order['nama_server']); ?></p>
                                    <?php if (!empty($order['paket'])): ?>
                                        <p class="text-light mb-1">
                                            <strong>Paket:</strong> 
                                            <span class="badge" style="background: #4fc3f7; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px;">
                                                <i class="fas fa-users"></i> <?php echo htmlspecialchars($order['paket']); ?>
                                            </span>
                                        </p>
                                    <?php endif; ?>
                                    <p class="text-light mb-1"><strong>Order ID:</strong> #<?php echo $order['id']; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-light mb-1"><strong>Total:</strong> Rp <?php echo number_format($order['jumlah_bayar'], 0, ',', '.'); ?></p>
                                    <p class="text-light mb-1"><strong>Status:</strong> 
                                        <span class="status-pending" id="paymentStatus"><?php echo htmlspecialchars($order['status_bayar']); ?></span>
                                    </p>
                                    <p class="text-light mb-1"><strong>Metode:</strong> <?php echo htmlspecialchars($order['pembayaran']); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Monitor -->
                        <div id="paymentMonitor">
                            <div class="text-center">
                                <h5 class="text-white mb-3">
                                    <i class="fas fa-qrcode"></i> Scan QR Code untuk Pembayaran
                                </h5>
                                
                                <div class="qr-container">
                                    <div id="qris-code">
                                        <img src="../assets/img/QRIS.png" alt="QRIS Code" style="max-width: 300px; height: auto;">
                                    </div>
                                    <p class="mt-3 mb-2 text-dark">Scan dengan aplikasi pembayaran digital Anda</p>
                                    <div class="alert alert-info mt-2">
                                        <strong>Total: Rp <?php echo number_format($order['jumlah_bayar'], 0, ',', '.'); ?></strong><br>
                                        <small>Pastikan nominal sesuai dengan jumlah di atas</small>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <p class="text-white mb-2">
                                        <i class="fas fa-clock"></i> Waktu tersisa:
                                    </p>
                                    <div class="countdown" id="countdown">15:00</div>
                                </div>
                                
                                <div class="mt-4">
                                    <p class="text-light">
                                        <i class="fas fa-spinner spinner"></i> Menunggu pembayaran...
                                    </p>
                                    <small class="text-muted">Halaman akan otomatis update ketika pembayaran berhasil</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Success -->
                        <div id="paymentSuccess" class="payment-success">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h3 class="text-success mb-3">Pembayaran Berhasil!</h3>
                            <p class="text-white mb-4">
                                Pembayaran Anda telah terkonfirmasi. Tim kami akan segera memproses server Anda.
                            </p>
                            <a href="order_history.php" class="btn btn-success btn-lg">
                                <i class="fas fa-history"></i> Cek Riwayat Order
                            </a>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="order_history.php" class="btn btn-outline-light">
                                <i class="fas fa-history"></i> Cek Riwayat Order
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        const orderId = <?php echo $order_id; ?>;
        const amount = <?php echo $order['jumlah_bayar']; ?>;
        const serverName = '<?php echo htmlspecialchars($order['nama_server']); ?>';
        
        let countdownTime = 15 * 60; // 15 minutes in seconds
        let checkInterval;
        let countdownInterval;
        
        // QR Code is now static image, no need to generate
        
        // Start countdown
        function startCountdown() {
            countdownInterval = setInterval(() => {
                const minutes = Math.floor(countdownTime / 60);
                const seconds = countdownTime % 60;
                
                document.getElementById('countdown').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                countdownTime--;
                
                if (countdownTime < 0) {
                    clearInterval(countdownInterval);
                    clearInterval(checkInterval);
                    
                    Swal.fire({
                        title: 'Waktu Habis!',
                        text: 'Waktu pembayaran telah habis. Silakan buat pesanan baru.',
                        icon: 'warning',
                        background: 'rgba(13, 18, 26, 0.95)',
                        color: '#fff',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = 'order_history';
                    });
                }
            }, 1000);
        }
        
        // Check payment status
        function checkPaymentStatus() {
            fetch(`api/check_payment_status.php?order_id=${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.status === 'Sudah Dibayar') {
                            // Payment successful
                            clearInterval(checkInterval);
                            clearInterval(countdownInterval);
                            
                            document.getElementById('paymentStatus').textContent = 'Sudah Dibayar';
                            document.getElementById('paymentStatus').className = 'status-paid';
                            
                            document.getElementById('paymentMonitor').style.display = 'none';
                            document.getElementById('paymentSuccess').classList.add('show');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking payment status:', error);
                });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            startCountdown();
            
            // Check payment status every 5 seconds
            checkInterval = setInterval(checkPaymentStatus, 5000);
        });
    </script>
    
    <!-- Navbar Scroll Effect -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('header');
            
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });
    </script>
</body>
</html>