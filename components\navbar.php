<?php
// Include dynamic path configuration
require_once __DIR__ . '/../includes/config.php';

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<!-- ======= Header ======= -->
<header id="header" class="fixed-top ">
    <div class="container d-flex align-items-center">
        <a href="<?php echo root_url(''); ?>" class="logo me-auto">
            <img src="https://dopminer.com/Gambar/logo&text.png" alt="logo" class="">
        </a>

        <nav id="navbar" class="navbar">
            <ul>
            <a href="<?php echo root_url('pages/products'); ?>"><span><h5><i class="fa fa-shopping-bag" style="font-size: 18px; background: #47b2e400;"></i> <PERSON><PERSON></h5></span></a>

                <!-- <li class="dropdown">
                    <a href="#"><span><h5>Hosting</h5></span><i class="bi bi-chevron-down"></i></a>
                    <ul>
                        <li><a href="<?php echo root_url('pages/vps'); ?>">
                                <h6><i class="fa fa-server" style="font-size: 18px; background: #47b2e400;"></i> VPS</h6>
                            </a></li>
                        <li><a href="<?php echo root_url('pages/games'); ?>">
                                <h6><i class="bi bi-joystick" style="font-size: 20px; background: #47b2e400;"></i> Game Hosting</h6>
                            </a></li>
                        <li><a href="<?php echo root_url('pages/app'); ?>">
                                <h6><i class="bi bi-phone-flip" style="font-size: 20px; background: #47b2e400;"></i> App Hosting</h6>
                            </a></li>
                    </ul>
                </li> -->
                <!-- <li class="dropdown"><a href="#"><span><h5> Services</h5></span><i class="bi bi-chevron-down"></i></a>
                    <ul>
                        <li><a href="#" onclick="alert('Coming Soon!'); return false;">
                                <h6><i class="bi bi-bar-chart" style="font-size: 18px; background: #47b2e400;"></i> SMM Panel</h6>
                            </a></li>
                    </ul>
                </li> -->
                <li class="dropdown" style="margin-right: 20px;"><a href="#"><span><h5><i class="bi bi-telephone" style="font-size: 18px; background: #47b2e400;"></i> Contact</h5></span><i class="bi bi-chevron-down"></i></a>
                    <ul>
                        <li><a href="https://discord.com/invite/dopminer">
                                <h6><i class="bi bi-discord" style="font-size: 18px; background: #47b2e400;"></i> Discord</h6>
                            </a></li>
                        <li><a href="https://www.tiktok.com/@dopminer">
                                <h6><i class="bi bi-tiktok" style="font-size: 18px; background: #47b2e400;"></i> Tiktok</h6>
                            </a></li>
                        <li><a href="https://www.instagram.com/@dopminer_official">
                                <h6><i class="bi bi-instagram" style="font-size: 18px; background: #47b2e400;"></i> Instagram</h6>
                            </a></li>
                    </ul>
                </li>
                <?php if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true): ?>
                    <li class="dropdown">
                        <a href="#"><span>
                                <h5 class="Account-btn" style="border: 3px solid #46aedf; color: #fff; padding: 10px 28px; border-radius: 20px; font-size: 16px;">
                                    Account<i class="bi bi-chevron-down"></i></h5>
                            </span>
                        </a>
                        <ul>
                            <li><a href="<?php echo root_url('pages/dashboard'); ?>" class="dropdown-item">
                                    <h6><i class="fa fa-home" style="font-size: 18px; background: #47b2e400;"></i> Dashboard</h6>
                                </a></li>
                            <li><a href="<?php echo root_url('pages/myhosting'); ?>" class="dropdown-item">
                                    <h6><i class="fa fa-server" style="font-size: 18px; background: #47b2e400;"></i> My Hosting</h6>
                                </a></li>
                            <li><a href="<?php echo root_url('pages/order_history'); ?>" class="dropdown-item">
                                    <h6><i class="fa fa-shopping-cart" style="font-size: 18px; background: #47b2e400;"></i> My Order</h6>
                                </a></li>
                            <li><a href="<?php echo root_url('pages/profile'); ?>" class="dropdown-item">
                                    <h6><i class="fa fa-user" style="font-size: 18px; background: #47b2e400;"></i> Profile</h6>
                                </a></li>

                            <?php if (isset($_SESSION['group']) && $_SESSION['group'] === 'ADMIN'): ?>
                                <li><a href="<?php echo root_url('admin/admin'); ?>" class="dropdown-item">
                                        <h6><i class="fa fa-cogs" style="font-size: 18px; background: #47b2e400;"></i> Admin</h6>
                                    </a></li>
                            <?php endif; ?>
                            <li><a href="<?php echo root_url('auth/logout'); ?>" class="dropdown-item">
                                    <h6><i class="fa fa-sign-out" style="font-size: 18px; background: #47b2e400;"></i> Logout</h6>
                                </a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li>
                        <a class="login-btn" href="<?php echo root_url('auth/login'); ?>" 
                           style="border: 3px solid #46aedf; color: #fff; padding: 10px 28px; border-radius: 20px; margin-left: 50px;">Login</a>
                    </li>
                <?php endif; ?>
            </ul>
            <i class="bi bi-list mobile-nav-toggle"></i>
        </nav><!-- .navbar -->
    </div>
</header><!-- End Header -->

<!-- Optimized CSS -->
<style>
    /* Critical CSS - load immediately */
    .dropdown ul li a {
        display: flex;
        align-items: center;
        color: #000;
        text-decoration: none;
    }

    .dropdown ul li a:hover {
        color: #007bff;
    }

    .dropdown ul li a h6 {
        display: flex;
        align-items: center;
        margin: 0;
    }

    .dropdown ul li a h6 i {
        margin-right: 10px;
        transition: color 0.3s;
    }

    .dropdown ul li a:hover h6 i {
        color: #47b2e4;
    }

    .Account-btn:hover,
    .Account-btn:active {
        background-color: #46aedf;
    }

    .login-btn:hover,
    .login-btn:active {
        background-color: #46aedf;
    }
</style>

<!-- Optimized JavaScript -->
<script>
    // Defer non-critical JavaScript
    window.addEventListener('load', function() {
        // Menu items click handler
        var menuItems = document.querySelectorAll('.coming-soon');
        menuItems.forEach(function(item) {
            item.addEventListener('click', function(event) {
                event.preventDefault();
                alert('Coming Soon!');
            });
        });
    });
</script>



