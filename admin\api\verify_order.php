<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing order_id parameter']);
    exit;
}

$order_id = intval($input['order_id']);

if ($order_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid order_id']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();
    
    // Check if order exists and get current status
    $check_stmt = $conn->prepare("SELECT id, status_bayar, email, nama_server FROM `order` WHERE id = ?");
    if (!$check_stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $check_stmt->bind_param("i", $order_id);
    $check_stmt->execute();
    $order_result = $check_stmt->get_result();
    $order = $order_result->fetch_assoc();
    
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    if ($order['status_bayar'] === 'Sudah Dibayar') {
        throw new Exception('Order already verified');
    }
    
    // Update order status to verified
    $update_stmt = $conn->prepare("UPDATE `order` SET status_bayar = 'Sudah Dibayar', updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    if (!$update_stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $update_stmt->bind_param("i", $order_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception('Failed to update order status: ' . $update_stmt->error);
    }
    
    // Log the verification action
    error_log("Order #{$order_id} verified by admin {$email} for customer {$order['email']} (server: {$order['nama_server']})");
    
    // Commit transaction
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Order verified successfully',
        'order_id' => $order_id
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
} catch (Exception $e) {
    error_log('verify_order.php error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Clean output buffer
ob_end_flush();
?>