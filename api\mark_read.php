<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Turn off error reporting for clean JSON output
error_reporting(0);
ini_set('display_errors', 0);

// Clean any output that might have been sent
if (ob_get_level()) {
    ob_clean();
}

include '../database/database.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['idchat'])) {
    echo json_encode(['success' => false, 'error' => 'ID Chat not provided']);
    exit;
}

$idchat = intval($input['idchat']);
$email = $_SESSION['email'];

// Mark messages as read (except current user's messages)
$stmt = $conn->prepare("UPDATE chats SET dibaca = 1 WHERE idchat = ? AND email != ?");
$stmt->bind_param("is", $idchat, $email);
$stmt->execute();

echo json_encode(['success' => true]);
?> 