<?php
session_start();

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || $_SESSION['group'] !== 'ADMIN') {
    header('Location: ../auth/login');
    exit;
}

include '../database/database.php';

// Function to get real user IP
if (!function_exists('getUserIP')) {
function getUserIP() {
    // Check for IP from shared internet
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    }
    // Check for IP passed from proxy
    elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    }
    // Check for IP from remote address
    elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    } else {
        $ip = 'Unknown IP';
    }
    
    // If localhost (::1 or 127.0.1), try to get public IP
    if ($ip === '::1' || $ip === '127.0.0.1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        $publicIP = getPublicIP();
        if ($publicIP && $publicIP !== 'Unknown') {
            return $publicIP; // Return only public IP
        }
    }
    
    return $ip;
}

// Function to get public IP from external service
function getPublicIP() {
    $services = [
        'https://api.ipify.org',
        'https://ifconfig.me/ip',
        'https://icanhazip.com',
        'https://ident.me'
    ];
    
    foreach ($services as $service) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (compatible; IP Detection)'
            ]
        ]);
        
        $ip = @file_get_contents($service, false, $context);
        if ($ip && filter_var(trim($ip), FILTER_VALIDATE_IP)) {
            return trim($ip);
        }
    }
    
    return 'Unknown';
}
}

$success_message = '';
$error_message = '';

// Check for session messages
if (isset($_SESSION['admin_success'])) {
    $success_message = $_SESSION['admin_success'];
    unset($_SESSION['admin_success']);
}

if (isset($_SESSION['admin_error'])) {
    $error_message = $_SESSION['admin_error'];
    unset($_SESSION['admin_error']);
}

// Handle approval actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['request_id'])) {
        $request_id = intval($_POST['request_id']);
        $action = $_POST['action'];
        $admin_notes = trim($_POST['admin_notes']);
        $admin_email = $_SESSION['email'];
        
        if ($action === 'approve') {
            // Get request details
            $get_request = $conn->prepare("SELECT * FROM email_change_requests WHERE id = ? AND status = 'pending'");
            $get_request->bind_param("i", $request_id);
            $get_request->execute();
            $request_result = $get_request->get_result();
            
            if ($request_result->num_rows > 0) {
                $request_data = $request_result->fetch_assoc();
                
                // Update user's email in users table
                $update_user = $conn->prepare("UPDATE users SET email = ? WHERE email = ?");
                $update_user->bind_param("ss", $request_data['new_email'], $request_data['old_email']);
                
                if ($update_user->execute()) {
                    // Update request status
                    $update_request = $conn->prepare("UPDATE email_change_requests SET status = 'approved', admin_email = ?, admin_action_date = NOW(), admin_notes = ? WHERE id = ?");
                    $update_request->bind_param("ssi", $admin_email, $admin_notes, $request_id);
                    $update_request->execute();
                    
                    // Update history with admin IP
                    $admin_ip = getUserIP();
                    $admin_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown Agent';
                    $update_history = $conn->prepare("UPDATE profile_history SET status = 'approved', admin_approved_by = ?, admin_ip = ?, admin_user_agent = ? WHERE user_email = ? AND change_type = 'email_change' AND new_value = ? AND status = 'pending'");
                    $update_history->bind_param("sssss", $admin_email, $admin_ip, $admin_user_agent, $request_data['old_email'], $request_data['new_value']);
                    $update_history->execute();
                    
                    // Update data_client table if exists
                    $update_client = $conn->prepare("UPDATE data_client SET email = ? WHERE email = ?");
                    $update_client->bind_param("ss", $request_data['new_email'], $request_data['old_email']);
                    $update_client->execute();
                    
                    $_SESSION['admin_success'] = "Email change request approved successfully!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                } else {
                    $_SESSION['admin_error'] = "Failed to update user email!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                }
            }
        } elseif ($action === 'reject') {
            // Update request status to rejected
            $update_request = $conn->prepare("UPDATE email_change_requests SET status = 'rejected', admin_email = ?, admin_action_date = NOW(), admin_notes = ? WHERE id = ?");
            $update_request->bind_param("ssi", $admin_email, $admin_notes, $request_id);
            
            if ($update_request->execute()) {
                // Update history
                $get_request = $conn->prepare("SELECT user_email, new_email FROM email_change_requests WHERE id = ?");
                $get_request->bind_param("i", $request_id);
                $get_request->execute();
                $request_result = $get_request->get_result();
                $request_data = $request_result->fetch_assoc();
                
                $admin_ip = getUserIP();
                $admin_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown Agent';
                $update_history = $conn->prepare("UPDATE profile_history SET status = 'rejected', admin_approved_by = ?, admin_ip = ?, admin_user_agent = ? WHERE user_email = ? AND change_type = 'email_change' AND new_value = ? AND status = 'pending'");
                $update_history->bind_param("sssss", $admin_email, $admin_ip, $admin_user_agent, $request_data['user_email'], $request_data['new_email']);
                $update_history->execute();
                
                $_SESSION['admin_success'] = "Email change request rejected successfully!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            } else {
                $_SESSION['admin_error'] = "Failed to reject request!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            }
        } elseif ($action === 'delete') {
            // Get request details before deleting
            $get_request = $conn->prepare("SELECT user_email, new_email, old_email FROM email_change_requests WHERE id = ?");
            $get_request->bind_param("i", $request_id);
            $get_request->execute();
            $request_result = $get_request->get_result();
            
            if ($request_result->num_rows > 0) {
                $request_data = $request_result->fetch_assoc();
                
                // Delete from email_change_requests
                $delete_request = $conn->prepare("DELETE FROM email_change_requests WHERE id = ?");
                $delete_request->bind_param("i", $request_id);
                
                if ($delete_request->execute()) {
                    // Delete corresponding history entry
                    $delete_history = $conn->prepare("DELETE FROM profile_history WHERE user_email = ? AND change_type = 'email_change' AND new_value = ?");
                    $delete_history->bind_param("ss", $request_data['user_email'], $request_data['new_email']);
                    $delete_history->execute();
                    
                    $_SESSION['admin_success'] = "Email change request deleted successfully!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                } else {
                    $_SESSION['admin_error'] = "Failed to delete request!";
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit;
                }
            } else {
                $_SESSION['admin_error'] = "Request not found!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            }
        }
    }
}

// Get all email change requests
$requests_query = "SELECT * FROM email_change_requests ORDER BY request_date DESC";
$requests_result = $conn->query($requests_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Admin - Email Change Approvals</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Sweetalert Custom CSS -->
    <link rel="stylesheet" href="../assets/css/sweetalert-custom.css">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
        
        .admin-container {
            padding: 2rem 0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .admin-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(79, 195, 247, 0.15);
            border-color: #4fc3f7;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
        }
        
        .status-pending {
            background: rgba(255, 152, 0, 0.2);
            color: #ff9800;
            border: 1px solid #ff9800;
        }
        
        .status-approved {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        
        .status-rejected {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }
        
        .btn-approve {
            background: linear-gradient(45deg, #4caf50, #45a049);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-approve:hover {
            background: linear-gradient(45deg, #45a049, #3d8b40);
            transform: translateY(-1px);
            color: white;
        }
        
        .btn-reject {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-reject:hover {
            background: linear-gradient(45deg, #d32f2f, #b71c1c);
            transform: translateY(-1px);
            color: white;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            padding: 0.5rem 1rem;
        }
        
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4fc3f7;
            box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.25);
            color: white;
        }
        
        .form-control::placeholder {
            color: #b0b0b0;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        
        .alert-danger {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }
        
        .btn-back {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 style="color: #4fc3f7; margin: 0;">
                        <i class="bi bi-envelope-check"></i> Email Change Approvals
                    </h1>
                    <p style="color: #b0b0b0; margin: 0.5rem 0 0 0;">
                        Manage user email change requests
                    </p>
                </div>
                <a href="admin.php" class="btn-back">
                    <i class="bi bi-arrow-left"></i> Back to Admin
                </a>
            </div>
        </div>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($requests_result->num_rows > 0): ?>
            <?php while ($request = $requests_result->fetch_assoc()): ?>
                <div class="admin-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 style="color: #4fc3f7; margin-bottom: 1rem;">
                                <i class="bi bi-person"></i> Email Change Request #<?php echo $request['id']; ?>
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>User:</strong></div>
                                <div class="col-sm-9"><?php echo htmlspecialchars($request['user_email']); ?></div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>From:</strong></div>
                                <div class="col-sm-9"><?php echo htmlspecialchars($request['old_email']); ?></div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>To:</strong></div>
                                <div class="col-sm-9"><?php echo htmlspecialchars($request['new_email']); ?></div>
                            </div>
                            
                            <?php if ($request['reason']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Reason:</strong></div>
                                    <div class="col-sm-9"><?php echo htmlspecialchars($request['reason']); ?></div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>Request Date:</strong></div>
                                <div class="col-sm-9"><?php echo date('d M Y H:i', strtotime($request['request_date'])); ?></div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>Status:</strong></div>
                                <div class="col-sm-9">
                                    <span class="status-badge status-<?php echo $request['status']; ?>">
                                        <?php echo ucfirst($request['status']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <?php if ($request['admin_email']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Admin:</strong></div>
                                    <div class="col-sm-9"><?php echo htmlspecialchars($request['admin_email']); ?></div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($request['admin_action_date']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Action Date:</strong></div>
                                    <div class="col-sm-9"><?php echo date('d M Y H:i', strtotime($request['admin_action_date'])); ?></div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($request['admin_notes']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Admin Notes:</strong></div>
                                    <div class="col-sm-9"><?php echo htmlspecialchars($request['admin_notes']); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-4">
                            <?php if ($request['status'] === 'pending'): ?>
                                <!-- Approve Form -->
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label">Admin Notes (Optional):</label>
                                        <textarea name="admin_notes" class="form-control" rows="2" placeholder="Optional approval notes..."></textarea>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="action" value="approve" class="btn btn-approve">
                                            <i class="bi bi-check-lg"></i> Approve
                                        </button>
                                    </div>
                                </form>
                                
                                <!-- Reject Form -->
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label">Alasan Penolakan *:</label>
                                        <textarea name="admin_notes" class="form-control" rows="3" placeholder="Jelaskan alasan penolakan..." required></textarea>
                                        <small class="form-text" style="color: #b0b0b0;">Alasan ini akan ditampilkan kepada user</small>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="action" value="reject" class="btn btn-reject">
                                            <i class="bi bi-x-lg"></i> Reject
                                        </button>
                                    </div>
                                </form>
                                
                                <!-- Delete Form for Pending Requests -->
                                <form method="POST" class="mt-3" onsubmit="return confirm('Apakah Anda yakin ingin menghapus permintaan ini? Tindakan ini tidak dapat dibatalkan.');">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    <input type="hidden" name="action" value="delete">
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn" style="
                                            background: linear-gradient(45deg, #6c757d, #5a6268);
                                            border: none;
                                            color: white;
                                            padding: 0.4rem 0.8rem;
                                            border-radius: 6px;
                                            font-weight: 500;
                                            font-size: 0.85rem;
                                            transition: all 0.3s ease;
                                        ">
                                            <i class="bi bi-trash3"></i> Delete Request
                                        </button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> 
                                    This request has been <?php echo $request['status']; ?>.
                                </div>
                                
                                <!-- Delete Form for Processed Requests -->
                                <form method="POST" class="mt-3" onsubmit="return confirm('Apakah Anda yakin ingin menghapus permintaan ini? Tindakan ini tidak dapat dibatalkan.');">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    <input type="hidden" name="action" value="delete">
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn" style="
                                            background: linear-gradient(45deg, #6c757d, #5a6268);
                                            border: none;
                                            color: white;
                                            padding: 0.4rem 0.8rem;
                                            border-radius: 6px;
                                            font-weight: 500;
                                            font-size: 0.85rem;
                                            transition: all 0.3s ease;
                                        ">
                                            <i class="bi bi-trash3"></i> Delete Request
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="admin-card text-center">
                <i class="bi bi-inbox" style="font-size: 4rem; color: #666;"></i>
                <h4 style="color: #999; margin-top: 1rem;">No Email Change Requests</h4>
                <p style="color: #666;">There are no email change requests at this time.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

    <!-- Sweetalert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        function confirmDelete(formElement) {
            event.preventDefault();
            
            Swal.fire({
                title: 'Hapus Permintaan?',
                text: 'Apakah Anda yakin ingin menghapus permintaan ini? Tindakan ini tidak dapat dibatalkan.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                background: 'rgba(0, 0, 0, 0.9)',
                color: '#fff'
            }).then((result) => {
                if (result.isConfirmed) {
                    formElement.submit();
                }
            });
            
            return false;
        }
    </script>
</body>
</html> 