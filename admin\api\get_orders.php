<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

try {
    // Get orders with product information
    $query = "
        SELECT 
            o.id,
            o.id_order,
            o.id_produk,
            o.email,
            o.nama_server,
            o.paket,
            o.status_bayar,
            o.pembayaran,
            o.bukti_pembayaran,
            o.jumlah_bayar,
            o.created_at,
            o.updated_at,
            p.nama as nama_produk
        FROM `order` o
        LEFT JOIN produk p ON o.id_produk = p.id_produk
        ORDER BY o.created_at DESC
    ";
    
    $result = $conn->query($query);
    
    if (!$result) {
        throw new Exception('Query error: ' . $conn->error);
    }
    
    $orders = [];
    while ($row = $result->fetch_assoc()) {
        // Extract filename from bukti_pembayaran path if it exists
        if ($row['bukti_pembayaran']) {
            $row['bukti_pembayaran'] = basename($row['bukti_pembayaran']);
        }
        $orders[] = $row;
    }
    
    echo json_encode($orders);
    
} catch (Exception $e) {
    error_log('get_orders.php error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

// Clean output buffer
ob_end_flush();
?>