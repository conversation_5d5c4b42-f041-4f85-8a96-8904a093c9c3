<?php

ini_set('display_errors', 1);

ini_set('display_startup_errors', 1);

// Error reporting disabled for production security
error_reporting(0);
ini_set('display_errors', 0);



// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}



include '../database/database.php';



// Cek apakah pengguna sudah login

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {

      header('Location: ../auth/login');

  exit;

}



// Pastikan email ada di session

if (!isset($_SESSION['email'])) {

  echo "Email tidak ditemukan di session.";

  exit;

}



$email = $_SESSION['email'];



// Cek apakah pengguna adalah admin

$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");

$stmt->bind_param("s", $email);

$stmt->execute();

$result = $stmt->get_result();

$user = $result->fetch_assoc();

$is_admin = ($user['group'] === 'ADMIN');



// Cek apakah email sudah ada di tabel chats

if ($is_admin && isset($_GET['idchat']) && intval($_GET['idchat']) > 0) {

  // Jika admin dan idchat diberikan melalui URL, gunakan idchat tersebut

  $idchat = intval($_GET['idchat']);

  $_SESSION['idchat'] = $idchat;

} else {

  $stmt = $conn->prepare("SELECT idchat FROM chats WHERE email = ? LIMIT 1");

  $stmt->bind_param("s", $email);

  $stmt->execute();

  $result = $stmt->get_result();

  $chat = $result->fetch_assoc();



  if ($chat) {

    // Jika email ditemukan, gunakan idchat yang ada

    $idchat = $chat['idchat'];

    $_SESSION['idchat'] = $idchat;

  } else {

    // Jika email tidak ditemukan, buat idchat baru

    if (isset($_GET['idchat']) && intval($_GET['idchat']) > 0) {

      $idchat = intval($_GET['idchat']);

      $_SESSION['idchat'] = $idchat;

    } elseif (isset($_SESSION['idchat'])) {

      $idchat = $_SESSION['idchat'];

    } else {

      $idchat = rand(100000, 999999); // Generate idchat secara acak

      $_SESSION['idchat'] = $idchat;

    }

  }

}



// Periksa apakah pengguna memiliki akses ke chat ini

if (!$is_admin) {

  $stmt = $conn->prepare("SELECT email FROM chats WHERE idchat = ? LIMIT 1");

  $stmt->bind_param("i", $idchat);

  $stmt->execute();

  $result = $stmt->get_result();

  $chat = $result->fetch_assoc();

  if ($chat && $chat['email'] !== $email) {

    header('Location: dashboard');

    exit;

  }

}



// Fungsi untuk menandai pesan sebagai dibaca

function tandaiPesanSebagaiDibaca($idchat, $email, $conn) {

    $stmt = $conn->prepare("UPDATE chats SET dibaca = 1 WHERE idchat = ? AND email != ?");

    $stmt->bind_param("is", $idchat, $email);

    $stmt->execute();

}



// Panggil fungsi ini saat pengguna mengunjungi halaman livechat

if (isset($idchat) && isset($email)) {

    tandaiPesanSebagaiDibaca($idchat, $email, $conn);

}



// Proses pengiriman pesan

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

  if (isset($_POST['delete_message']) && $is_admin) {

    $message_id = filter_var($_POST['delete_message'], FILTER_SANITIZE_NUMBER_INT);

    $stmt = $conn->prepare("DELETE FROM chats WHERE id = ? AND idchat = ?");

    $stmt->bind_param("ii", $message_id, $idchat);

    if ($stmt->execute()) {

      echo json_encode(['success' => true, 'message' => 'Pesan berhasil dihapus!']);

      exit;

    } else {

      echo json_encode(['error' => 'Gagal menghapus pesan!']);

      exit;

    }

  } else {

    $message = filter_var($_POST['message'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);



    // Validasi pesan untuk memastikan tidak ada kode berbahaya

    if ($message !== strip_tags($message)) {

      echo json_encode(['error' => 'Pesan mengandung konten yang tidak diizinkan!']);

      exit;

    }



    // Simpan pesan ke database

    $stmt = $conn->prepare("INSERT INTO chats (idchat, email, message, is_admin, dibaca) VALUES (?, ?, ?, ?, 0)");

    $stmt->bind_param("issi", $idchat, $email, $message, $is_admin);

    if ($stmt->execute()) {

      // Cek apakah ini adalah pesan pertama dari pengguna

      $stmt = $conn->prepare("SELECT COUNT(*) as message_count FROM chats WHERE idchat = ?");

      $stmt->bind_param("i", $idchat);

      $stmt->execute();

      $result = $stmt->get_result();

      $row = $result->fetch_assoc();

      $message_count = $row['message_count'];



      if ($message_count == 1) {

        // Kirim pesan otomatis dari admin hanya jika ini adalah pesan pertama

        $admin_email = '<EMAIL>';

        $admin_message = 'Terima kasih telah menghubungi kami. 

Kami akan segera merespon pesan Anda.';

        $is_admin = 1; // Set is_admin ke 1 untuk pesan dari admin

        $stmt = $conn->prepare("INSERT INTO chats (idchat, email, message, is_admin, dibaca) VALUES (?, ?, ?, ?, 1)");

        $stmt->bind_param("issi", $idchat, $admin_email, $admin_message, $is_admin);

        $stmt->execute();

      }



      echo json_encode(['success' => true, 'message' => 'Pesan berhasil dikirim!']);

      exit;

    } else {

      echo json_encode(['error' => 'Gagal mengirim pesan!']);

      exit;

    }

  }

}



// Ambil semua pesan dari database untuk idchat tertentu

$chats = $conn->prepare("SELECT * FROM chats WHERE idchat = ? ORDER BY created_at ASC");

$chats->bind_param("i", $idchat);

$chats->execute();

$chat_results = $chats->get_result();



?>



<!DOCTYPE html>

<html lang="en">



<head>

  <meta charset="utf-8">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">



  <title>Dopminer.com - Live Chat</title>

  <meta content="" name="description">

  <meta content="" name="keywords">

  <!-- Favicon -->
  <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">

  <link href="../assets/img/Dopminer.png" rel="apple-touch-icon">



  <!-- Google Fonts -->

  <link

    href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"

    rel="stylesheet">



  <!-- Vendor CSS Files -->

  <link href="../assets/vendor/aos/aos.css" rel="stylesheet">

  <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

  <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

  <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">

  <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">

  <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">



  <!-- Template Main CSS File -->

  <link href="../assets/css/style.css" rel="stylesheet">



  <!-- Sweetalert Custom CSS -->

  <link rel="stylesheet" href="../assets/css/sweetalert-custom.css">



  <style>

    .chat-section {

      display: flex;

      align-items: center;

      justify-content: center;

      padding: 20px;

    }



    .chat-card {

      max-width: 600px;

      width: 100%;

    }



    .chat-message {

      margin-bottom: 15px;

      padding: 10px;

      border-radius: 10px;

      position: relative;

    }



    .chat-message.admin {

      background-color: #f1f1f1;

      color: #333;

    }



    .chat-message.user {

      background-color: #007bff;

      color: #fff;

    }



    .delete-btn {

      position: absolute;

      top: 5px;

      right: 5px;

      background: none;

      border: none;

      color: #ff0000;

      cursor: pointer;

    }



    #chat-messages {

      max-height: 400px;

      overflow-y: auto;

    }



    /* CSS untuk membuat scrollbar transparan */

    #chat-messages::-webkit-scrollbar {

      width: 8px;

    }



    #chat-messages::-webkit-scrollbar-track {

      background: transparent;

    }



    #chat-messages::-webkit-scrollbar-thumb {

      background: rgba(0, 0, 0, 0.2);

      border-radius: 4px;

    }



    #chat-messages::-webkit-scrollbar-thumb:hover {

      background: rgba(0, 0, 0, 0.4);

    }

  </style>

</head>



<body>



  <?php include '../components/navbar.php'; ?>



  <!-- ======= Chat Section ======= -->

  <section id="chat" class="chat chat-section"

    style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg'); background-size: cover; background-position: center;">

    <div class="container" data-aos="fade-up">



      <div class="section-title" style="margin-bottom: 15px; margin-top: 190px;">

        <h2 style="margin-bottom: 15px;">Live Chat</h2>

      </div>



      <div class="row justify-content-center">

        <div class="col-lg-6">

          <div class="card shadow-lg p-3 mb-5 bg-dark text-white rounded chat-card"

            style="border-top: 6px solid #46aedf;">

            <style>

              .chat-card {

                max-width: 600px;

                width: 100%;

              }



              .rounded {

                border-radius: 30px !important;

              }

            </style>

            <div class="card-body" style="border-top: 1px solid rgba(0, 0, 0, 0);">

              <div id="error-message" class="text-center mt-3 mb-3 p-2 rounded" style="background-color: rgba(255, 255, 255, 0.1); border-left: 4px solid #46aedf; font-weight: bold; display: none;"></div>

              <div id="notification" class="text-center mt-3 mb-3 p-2 rounded" style="background-color: rgba(255, 255, 255, 0.1); border-left: 4px solid #46aedf;">

                <marquee behavior="scroll" direction="left" scrollamount="5">

                  Mohon maaf jika masih terjadi bug pada chat ini

                </marquee>

              </div>

              <div id="chat-messages" class="mb-3" style="margin-top: 20px;">

                <?php while ($chat = $chat_results->fetch_assoc()): ?>

                  <div class="chat-message <?php echo $chat['is_admin'] ? 'admin' : 'user'; ?>" data-message-id="<?php echo $chat['id']; ?>">

                    <strong><?php echo $chat['is_admin'] ? 'Admin Dopminer:' : htmlspecialchars($chat['email']); ?></strong>

                    <p style="color: white;"><?php echo nl2br(htmlspecialchars($chat['message'])); ?></p>

                    <small><?php echo $chat['created_at']; ?></small>

                    <?php if ($is_admin): ?>

                      <button class="delete-btn" onclick="deleteMessage(<?php echo $chat['id']; ?>)">X</button>

                    <?php endif; ?>

                  </div>

                <?php endwhile; ?>

              </div>

              <form id="chatForm" method="post" action="livechat?idchat=<?php echo $idchat; ?>" onsubmit="return validateForm()">

                <div class="form-group mt-3">

                  <label style="color: #c9c9c9;" for="message">Message</label>

                  <textarea name="message" class="form-control bg-dark text-white" style="border: 2px solid #46aedf; border-radius: 15px; padding: 15px; resize: vertical; transition: all 0.3s ease; box-shadow: 0 0 10px rgba(70, 174, 223, 0.1);"

                    id="message" placeholder="Enter your message" required></textarea>

                </div>

                <script>

                  function validateForm() {

                    var form = document.getElementById('chatForm');

                    var formData = new FormData(form);

                    var xhr = new XMLHttpRequest();

                    xhr.open('POST', 'livechat?idchat=<?php echo $idchat; ?>', true);

                    xhr.onload = function () {



                      if (xhr.status === 200) {

                        try {

                          var response = JSON.parse(xhr.responseText);



                          if (response.error) {

                            document.getElementById('error-message').innerText = response.error;

                            document.getElementById('error-message').style.display = 'block';

                            document.getElementById('notification').style.display = 'none';

                          } else if (response.success) {



                            var successMessage = document.getElementById('error-message');

                            successMessage.innerText = 'Message sent successfully!';

                            successMessage.style.color = 'green';

                            successMessage.style.display = 'block';

                            document.getElementById('notification').style.display = 'none';

                            setTimeout(function() {

                                successMessage.style.display = 'none';

                                document.getElementById('notification').style.display = 'block';

                            }, 5000);

                            // Clear the message input

                            document.getElementById('message').value = '';

                            // Reload chat messages immediately



                            setTimeout(updateMessages, 500);

                          }

                        } catch (e) {

                          console.error('JSON Parse Error in send:', e);

                          console.error('Response text:', xhr.responseText);

                          document.getElementById('error-message').innerText = 'An error occurred. Please try again.';

                          document.getElementById('error-message').style.display = 'block';

                          document.getElementById('notification').style.display = 'none';

                        }

                      }

                    };

                    xhr.send(formData);

                    document.getElementById('message').value = '';

                    return false; // Prevent form from submitting the default way

                  }



                  // This function is now replaced by updateMessages()
                  function loadMessages() {
                    updateMessages();
                  }



                  function deleteMessage(messageId) {
                    Swal.fire({
                        title: 'Hapus Pesan?',
                        text: 'Apakah Anda yakin ingin menghapus pesan ini?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: 'transparent',
                        confirmButtonText: 'Ya, Hapus!',
                        cancelButtonText: 'Batal',
                        allowOutsideClick: true,
                        allowEscapeKey: true,
                        showCloseButton: true,
                        didOpen: () => {
                            // Ensure close button is clickable
                            const closeButton = Swal.getPopup().querySelector('.swal2-close');
                            if (closeButton) {
                                closeButton.style.pointerEvents = 'auto';
                            }
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Existing delete logic here
                            fetch('api/delete_message.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ message_id: messageId })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    Swal.fire({
                                        title: 'Berhasil!',
                                        text: 'Pesan telah dihapus.',
                                        icon: 'success',
                                        background: 'rgba(0, 0, 0, 0.9)',
                                        color: '#fff',
                                        showConfirmButton: false,
                                        timer: 1500
                                    }).then(() => {
                                        location.reload();
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Gagal!',
                                        text: data.message || 'Terjadi kesalahan saat menghapus pesan.',
                                        icon: 'error',
                                        background: 'rgba(0, 0, 0, 0.9)',
                                        color: '#fff'
                                    });
                                }
                            });
                        }
                    });
                  }



                  // Simple and reliable auto-refresh system
                  var lastUpdateTime = new Date().toISOString();
                  var isInitialLoad = true;
                  var hasUnreadMessages = false;
                  var notificationInterval = null;
                  
                  function playNotificationSound() {
                    if (audioEnabled) {
                      try {
                        var audio = new Audio('../assets/notifpesan.mp3');
                        audio.volume = 0.5;
                        var playPromise = audio.play();
                        if (playPromise !== undefined) {
                          playPromise.catch(e => {
                            console.log('Audio play failed:', e);
                            // Try alternative path
                            try {
                              var altAudio = new Audio('/assets/notifpesan.mp3');
                              altAudio.volume = 0.5;
                              altAudio.play().catch(err => console.log('Alt audio also failed:', err));
                            } catch (altE) {
                              console.log('Alternative audio error:', altE);
                            }
                          });
                        }
                      } catch (e) {
                        console.log('Audio error:', e);
                      }
                    }
                  }
                  
                  function startNotificationLoop() {
                    if (notificationInterval) {
                      clearInterval(notificationInterval);
                    }
                    
                    // Play notification immediately
                    playNotificationSound();
                    
                    // Then play every 20 seconds
                    notificationInterval = setInterval(function() {
                      if (hasUnreadMessages) {
                        playNotificationSound();
                      } else {
                        clearInterval(notificationInterval);
                        notificationInterval = null;
                      }
                    }, 20000); // 20 seconds
                  }
                  
                  function stopNotificationLoop() {
                    hasUnreadMessages = false;
                    if (notificationInterval) {
                      clearInterval(notificationInterval);
                      notificationInterval = null;
                    }
                  }
                  
                  function updateMessages() {
                    fetch('api/get_messages.php?idchat=<?php echo $idchat; ?>&_=' + Date.now())
                      .then(response => {
                        return response.json();
                      })
                      .then(data => {
                        
                        if (data.success) {
                          var chatContainer = document.getElementById('chat-messages');
                          var currentMessages = chatContainer.querySelectorAll('.chat-message');
                          var currentCount = currentMessages.length;
                          var newCount = data.messages.length;
                          
                          // Check if there are new messages
                          if (newCount > currentCount && !isInitialLoad) {
                            hasUnreadMessages = true;
                            startNotificationLoop();
                          }
                          
                          // Always update the display
                          chatContainer.innerHTML = '';
                          data.messages.forEach(message => {
                            var messageDiv = document.createElement('div');
                            messageDiv.className = 'chat-message ' + (message.is_admin ? 'admin' : 'user');
                            messageDiv.setAttribute('data-message-id', message.id);
                            
                            var messageColor = message.is_admin ? 'black' : 'white';
                            var senderName = message.is_admin ? 'Admin Dopminer:' : message.email;
                            
                            messageDiv.innerHTML = `
                              <strong>${senderName}</strong>
                              <p style="color: ${messageColor};">${message.message.replace(/\n/g, '<br>')}</p>
                              <small>${message.created_at}</small>
                              ${<?php echo $is_admin ? 'true' : 'false'; ?> ? `<button class="delete-btn" onclick="deleteMessage(${message.id})">X</button>` : ''}
                            `;
                            
                            chatContainer.appendChild(messageDiv);
                          });
                          
                          // Scroll to bottom
                          chatContainer.scrollTop = chatContainer.scrollHeight;
                          
                          isInitialLoad = false;

                        } else {
                          console.error('API Error:', data.error);
                        }
                      })
                      .catch(error => {
                        console.error('Fetch error:', error);
                      });
                  }

                  // Enable audio after user interaction
                  var audioEnabled = false;
                  document.addEventListener('click', function() {
                    audioEnabled = true;
                    // Stop notification when user clicks anywhere
                    stopNotificationLoop();
                  }, { once: true });
                  
                  document.addEventListener('keydown', function() {
                    audioEnabled = true;
                    // Stop notification when user types
                    stopNotificationLoop();
                  }, { once: true });
                  
                  // Stop notification when user sends a message
                  document.querySelector('form').addEventListener('submit', function() {
                    stopNotificationLoop();
                  });
                  
                  // Stop notification when user scrolls the chat
                  document.getElementById('chat-messages').addEventListener('scroll', function() {
                    stopNotificationLoop();
                  });
                  
                  // Stop notification when user focuses on the message input
                  document.querySelector('textarea[name="message"]').addEventListener('focus', function() {
                    stopNotificationLoop();
                  });

                  // Start auto-refresh every 2 seconds

                  updateMessages(); // Initial load
                  setInterval(updateMessages, 2000);

                </script>

                <div class="text-center mt-4">

                  <button type="submit" class="submit-btn btn-lg w-100">Send</button>

                  <style>

                    .submit-btn {

                      border: 3px solid #46aedf;

                      color: #fff;

                      background-color: #2b3035;

                      padding: 10px 28px;

                      border-radius: 15px;

                      font-size: 16px;

                    }



                    .submit-btn:hover {

                      background-color: #46aedf00;

                      color: #fff;

                      border: 3px solid #46aedf;

                    }

                  </style>

                </div>

              </form>

            </div>



          </div>

        </div>

      </div>



    </div>

  </section>



  <div style="position: relative; bottom: 0; width: 100%;">

    <?php include '../components/footer.php'; ?>

  </div>







  <!-- Vendor JS Files -->

  <script src="../assets/vendor/aos/aos.js"></script>

  <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

  <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>

  <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

  <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>

  <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>

  <script src="../assets/vendor/php-email-form/validate.js"></script>



  <!-- Template Main JS File -->

  <script src="../assets/js/main.js"></script>



  <!-- Sweetalert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

</body>



</html>