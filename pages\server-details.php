<?php

session_start();

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {

  header('Location: ../auth/login');

  exit;

}



include '../database/database.php';



$hosting_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($hosting_id <= 0) {

  echo "ID hosting tidak valid.";

  exit;

}



$email = $_SESSION['email'];

$stmt = $conn->prepare("SELECT * FROM data_client WHERE id = ? AND email = ?");

$stmt->bind_param("is", $hosting_id, $email);

$stmt->execute();

$result = $stmt->get_result();

$hosting = $result->fetch_assoc();



if (!$hosting) {

  header('Location: myhosting');

  exit;

}



function timeLeft($expired)

{

  $now = new DateTime();

  $expiry = new DateTime($expired);

  $interval = $now->diff($expiry);

  

  if ($expiry < $now) {

    return "Expired";

  }

  

  $days = $interval->days;

  if ($days > 30) {

    return $interval->format('%m bulan %d hari');

  } elseif ($days > 0) {

    return $interval->format('%d hari %h jam');

  } else {

    return $interval->format('%h jam %i menit');

  }

}



function getStatus($expired)

{

  $now = new DateTime();

  $expiry = new DateTime($expired);

  

  if ($expiry < $now) {

    return ['status' => '❌ Expired ❌', 'class' => 'status-expired', 'color' => '#dc3545'];

  } elseif ($now->diff($expiry)->days <= 7) {

    return ['status' => '⚠️ Akan Berakhir ⚠️', 'class' => 'status-soon', 'color' => '#ffc107'];

  } else {

    return ['status' => '✅ Aktif ✅', 'class' => 'status-active', 'color' => '#28a745'];

  }

}



// Fungsi untuk menghilangkan emoji dan karakter khusus
function cleanText($text) {
    // Remove all 4-byte unicode characters (most emojis)
    $text = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $text);
    
    // Remove common emoji ranges
    $text = preg_replace('/[\x{1F000}-\x{1FFFF}]/u', '', $text);
    $text = preg_replace('/[\x{2600}-\x{27BF}]/u', '', $text);
    
    // Remove question marks (invalid characters)
    $text = str_replace('?', '', $text);
    
    // Remove other problematic characters
    $text = preg_replace('/[^\p{L}\p{N}\s\-\.]/u', '', $text);
    
    // Clean up spaces
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    return $text;
}

$statusInfo = getStatus($hosting['expired']);

$timeRemaining = timeLeft($hosting['expired']);

?>



<!DOCTYPE html>

<html lang="en">



<head>

  <meta charset="utf-8">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">



  <title>Dopminer.com - Detail Hosting</title>

  <meta content="" name="description">

  <meta content="" name="keywords">

  <!-- Favicon -->
  <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">



  <!-- Vendor CSS Files -->

  <link href="../assets/vendor/aos/aos.css" rel="stylesheet">

  <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

  <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

  <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">

  <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">

  <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">



  <!-- Template Main CSS File -->

  <link href="../assets/css/style.css" rel="stylesheet">



  <style>
    body {
      font-family: "Open Sans", sans-serif;
      background-color: #0a0e27;
      color: #ffffff;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: "Jost", sans-serif;
    }

    .myhosting-section {
      padding: 120px 0 2rem 0;
      min-height: 100vh;
      background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      position: relative;
    }

    .hosting-details-container {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: white;
      border-radius: 20px;
      padding: 30px;
      margin: 20px auto;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      max-width: 900px;
      transition: all 0.3s ease;
    }

    .hosting-details-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(79, 195, 247, 0.2);
      border-color: #4fc3f7;
    }

    .hosting-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #4fc3f7;
    }

    .hosting-header h2 {
      color: #4fc3f7;
      margin-bottom: 10px;
      font-size: 2rem;
      font-weight: 700;
      text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
    }



    .type-badge {
      display: inline-block;
      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
      color: white;
      padding: 10px 18px;
      border-radius: 25px;
      font-size: 0.85rem;
      font-weight: 700;
      margin-bottom: 25px;
      box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
      text-transform: uppercase;
      letter-spacing: 1px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-badge {
      display: inline-block;
      padding: 10px 18px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .status-active {
      background: rgba(76, 175, 80, 0.2);
      color: #4caf50;
      border: 1px solid #4caf50;
      box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }

    .status-expired {
      background: rgba(244, 67, 54, 0.2);
      color: #f44336;
      border: 1px solid #f44336;
      box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    }

    .status-soon {
      background: rgba(255, 152, 0, 0.2);
      color: #ff9800;
      border: 1px solid #ff9800;
      box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
    }



    .hosting-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .info-card {
      background: rgba(255, 255, 255, 0.05);
      padding: 20px;
      border-radius: 15px;
      border-left: 4px solid #4fc3f7;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;
    }

    .info-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(79, 195, 247, 0.15);
      border-left-color: #29b6f6;
    }

    .info-card h4 {
      color: #4fc3f7;
      margin-bottom: 15px;
      font-size: 1.1rem;
      font-weight: 600;
    }



    .info-item {

      display: flex;

      justify-content: space-between;

      margin-bottom: 8px;

      padding: 6px 0;

      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    }



    .info-item:last-child {

      border-bottom: none;

    }



    .info-label {

      font-weight: 600;

      color: #ccc;

    }



    .info-value {

      color: white;

      font-weight: 500;

    }



    .ip-address {

      background: rgba(70, 174, 223, 0.1);

      padding: 5px 10px;

      border-radius: 5px;

      font-family: monospace;

      font-size: 14px;

    }



    .notes-section {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(5px);
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .notes-section h4 {
      color: #4fc3f7;
      margin-bottom: 15px;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .action-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-top: 30px;
      flex-wrap: wrap;
    }



    .btn-primary {
      background: linear-gradient(45deg, #4fc3f7, #29b6f6);
      border: none;
      padding: 14px 28px;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
      color: white;
      text-decoration: none;
      box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
    }

    .btn-primary:hover {
      background: linear-gradient(45deg, #3a9bc1, #1976d2);
      transform: translateY(-3px);
      color: white;
      text-decoration: none;
      box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 14px 28px;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
      text-decoration: none;
      backdrop-filter: blur(5px);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: #4fc3f7;
      color: white;
      text-decoration: none;
      transform: translateY(-2px);
    }



    .expired-warning {
      background: rgba(244, 67, 54, 0.1);
      border: 1px solid #f44336;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      text-align: center;
      backdrop-filter: blur(5px);
      box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
    }

    .expired-warning h4 {
      color: #f44336;
      margin-bottom: 15px;
    }

    .section-title {
      color: #4fc3f7;
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 20px;
      text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
    }

    .section-subtitle {
      color: #b0b0b0;
      font-size: 1.1rem;
      text-align: center;
      margin-bottom: 3rem;
    }

    .ip-address {
      background: rgba(79, 195, 247, 0.1);
      padding: 8px 12px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      border: 1px solid rgba(79, 195, 247, 0.3);
    }

    @media (max-width: 768px) {
      .myhosting-section {
        padding: 100px 0 1rem 0;
      }

      .hosting-details-container {
        padding: 20px;
        margin: 1rem;
      }
      
      .hosting-info {
        grid-template-columns: 1fr;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .action-buttons .btn {
        width: 100%;
        max-width: 300px;
      }
    }

  </style>



</head>



<body>



  <?php include '../components/navbar.php'; ?>



  <!-- ======= Server Details Section ======= -->

  <section id="server-details" class="myhosting-section">

    <div class="container" data-aos="fade-up">

      <div class="section-title">
        DETAIL HOSTING
      </div>
      <div class="section-subtitle">
        Informasi lengkap tentang layanan hosting Anda
      </div>



      <div class="hosting-details-container">

        

        <!-- Hosting Header -->

        <div class="hosting-header">

          <div class="type-badge"><?php echo htmlspecialchars($hosting['typehosting']); ?></div>

                                                                                                                           <h2><?php echo htmlspecialchars(cleanText($hosting['namakota'])); ?></h2>

          <div class="status-badge <?php echo $statusInfo['class']; ?>">

            <?php echo $statusInfo['status']; ?>

          </div>

        </div>



        <!-- Expired Warning -->

        <?php if ($statusInfo['status'] == 'Expired'): ?>

          <div class="expired-warning">

            <h4>⚠️ Hosting Telah Berakhir</h4>

            <p>Layanan hosting Anda telah berakhir. Silakan hubungi admin untuk perpanjangan atau pembaruan.</p>

          </div>

        <?php elseif ($statusInfo['status'] == 'Akan Berakhir'): ?>

          <div class="expired-warning" style="background: rgba(255, 193, 7, 0.1); border-color: #ffc107;">

            <h4 style="color: #ffc107;">⚠️ Hosting Akan Berakhir</h4>

            <p>Layanan hosting Anda akan berakhir dalam waktu dekat. Silakan perpanjang sebelum tanggal expired.</p>

          </div>

        <?php endif; ?>



        <!-- Hosting Information -->

        <div class="hosting-info">

          <div class="info-card">

            <h4>📋 Informasi Umum</h4>

            <div class="info-item">

              <span class="info-label">ID Hosting:</span>

              <span class="info-value">#<?php echo htmlspecialchars($hosting['id']); ?></span>

            </div>

            <div class="info-item">

              <span class="info-label">Email:</span>

              <span class="info-value"><?php echo htmlspecialchars($hosting['email']); ?></span>

            </div>

            <div class="info-item">

              <span class="info-label">Tipe Hosting:</span>

              <span class="info-value"><?php echo htmlspecialchars($hosting['typehosting']); ?></span>

            </div>

            <div class="info-item">
              <span class="info-label">Paket:</span>
              <span class="info-value"><?php 
                  $paket = strtoupper($hosting['pakethosting']);
                  $excludeTerms = ['FIVEM', 'SAMP', 'VPS', 'PMA', 'REDM'];
                  foreach($excludeTerms as $term) {
                      $paket = str_replace($term, '', $paket);
                  }
                  echo htmlspecialchars(trim($paket));
              ?></span>
            </div>



          </div>



          <div class="info-card">

            <h4>💰 Informasi Pembayaran</h4>

            <div class="info-item">

              <span class="info-label">Harga Normal:</span>

              <span class="info-value">Rp <?php echo number_format($hosting['harganormal'], 0, ',', '.'); ?></span>

            </div>

            <div class="info-item">

              <span class="info-label">Harga Bulanan:</span>

              <span class="info-value">Rp <?php echo number_format($hosting['hargabulanan'], 0, ',', '.'); ?></span>

            </div>

            <div class="info-item">

              <span class="info-label">Tanggal Expired:</span>

              <span class="info-value"><?php echo date('d F Y', strtotime($hosting['expired'])); ?></span>

            </div>

            <div class="info-item">

              <span class="info-label">Sisa Waktu:</span>

              <span class="info-value" style="color: <?php echo $statusInfo['color']; ?>;">

                <?php echo $timeRemaining; ?>

              </span>

            </div>

          </div>

        </div>



        <!-- IP Hosting -->

        <?php if (!empty($hosting['iphosting'])): ?>

          <div class="info-card" style="margin-bottom: 20px;">

            <h4>🌐 Informasi Server</h4>

            <div class="info-item">

              <span class="info-label">IP Address:</span>

              <span class="info-value ip-address"><?php echo htmlspecialchars($hosting['iphosting']); ?></span>

            </div>

          </div>

        <?php endif; ?>



        <!-- Notes Section -->

        <?php if (!empty($hosting['catatan'])): ?>

          <div class="notes-section">

            <h4>📝 Catatan</h4>

            <p><?php echo nl2br(htmlspecialchars($hosting['catatan'])); ?></p>

          </div>

        <?php endif; ?>



        <!-- Action Buttons -->

        <div class="action-buttons">

          <?php if ($statusInfo['status'] == 'Expired'): ?>

            <a href="#" class="btn btn-primary" onclick="contactAdmin()">

              <i class="bi bi-whatsapp"></i> Hubungi Admin

            </a>

          <?php else: ?>

            <a href="#" class="btn btn-primary" onclick="renewHosting()">

              <i class="bi bi-arrow-clockwise"></i> Perpanjang Hosting

            </a>

          <?php endif; ?>

          

          <a href="myhosting" class="btn btn-secondary">

            <i class="bi bi-arrow-left"></i> Kembali ke My Hosting

          </a>

        </div>

      </div>

    </div>

  </section>



  <?php include '../components/footer.php'; ?>



  





  <!-- Vendor JS Files -->

  <script src="../assets/vendor/aos/aos.js"></script>

  <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

  <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>

  <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

  <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>

  <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>

  <script src="../assets/vendor/php-email-form/validate.js"></script>



  <!-- Template Main JS File -->

  <script src="../assets/js/main.js"></script>



  <script>

    function contactAdmin() {

      // Redirect to WhatsApp with pre-filled message

      const message = encodeURIComponent(`Halo admin, saya ingin memperpanjang hosting dengan ID: #<?php echo $hosting['id']; ?> (<?php echo $hosting['namakota']; ?>). Hosting saya telah expired pada tanggal <?php echo date('d F Y', strtotime($hosting['expired'])); ?>.`);

      window.open(`https://wa.me/6281281159899?text=${message}`, '_blank');

    }



    function renewHosting() {

      // Redirect to WhatsApp with pre-filled message

      const message = encodeURIComponent(`Halo admin, saya ingin memperpanjang hosting dengan ID: #<?php echo $hosting['id']; ?> (<?php echo $hosting['namakota']; ?>). Hosting saya akan expired pada tanggal <?php echo date('d F Y', strtotime($hosting['expired'])); ?>.`);

      window.open(`https://wa.me/6281281159899?text=${message}`, '_blank');

    }

  </script>



</body>



</html>