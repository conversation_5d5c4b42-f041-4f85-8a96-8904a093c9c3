// Slideshow functionality for product cards
function initProductSlideshows() {
    document.querySelectorAll('.product-image-slideshow').forEach(slideshow => {
        const slides = slideshow.querySelectorAll('.product-slide');
        if (slides.length <= 1) return; // Skip if only one image

        let currentSlide = 0;
        function showNextSlide() {
            slides[currentSlide].classList.remove('active');
            currentSlide = (currentSlide + 1) % slides.length;
            slides[currentSlide].classList.add('active');
        }
        setInterval(showNextSlide, 3000);
    });
}

// Update showProductDetail function to handle image zoom
function showProductDetail(productData) {
    // Parse the product data if it's a string (from htmlspecialchars)
    let product;
    if (typeof productData === 'string') {
        try {
            product = JSON.parse(productData);
        } catch (e) {
            console.error('Error parsing product data:', e);
            return;
        }
    } else {
        product = productData;
    }
    // Handle image links
    let images = [];
    if (product.image_links) {
        images = product.image_links.split('|').filter(img => img.trim() !== '');
    }
    if (images.length === 0) {
        images = [product.image]; // Fallback to single image
    }

    let currentImageIndex = 0;
    const showNavigation = images.length > 1;

    // Function to create the main product detail modal
    function createProductDetailModal() {
        const imageSliderHtml = `
            <div class="product-modal-slider">
                <img src="${images[currentImageIndex]}" alt="${product.name}" class="modal-image" style="width: 100%; border-radius: 10px; cursor: zoom-in;">
                ${showNavigation ? `
                    <div class="slider-nav slider-prev">❮</div>
                    <div class="slider-nav slider-next">❯</div>
                ` : ''}
            </div>
        `;

        // Convert specifications string to HTML with icons
        const specLines = product.spesifikasi.split('\n');
        const specHtml = specLines.map(spec => {
            spec = spec.trim();
            if (!spec) return '';
            
            if (spec.includes('CPU:')) {
                return `<div class="modal-spec-item"><i class="fas fa-microchip"></i> ${spec}</div>`;
            } else if (spec.includes('RAM:')) {
                return `<div class="modal-spec-item"><i class="fas fa-memory"></i> ${spec}</div>`;
            } else if (spec.includes('Bandwith:') || spec.includes('Bandwidth:')) {
                return `<div class="modal-spec-item"><i class="fas fa-network-wired"></i> ${spec}</div>`;
            } else if (spec.includes('DDOS')) {
                return `<div class="modal-spec-item"><i class="fas fa-shield-alt"></i> ${spec}</div>`;
            } else if (spec.includes('Speed')) {
                return `<div class="modal-spec-item"><i class="fas fa-tachometer-alt"></i> ${spec}</div>`;
            } else {
                return `<div class="modal-spec-item"><i class="fas fa-check"></i> ${spec}</div>`;
            }
        }).join('');

        return Swal.fire({
            title: product.name,
            html: `
                <div class="product-detail-modal animate__animated animate__fadeIn animate__slow">
                    <div class="row">
                        <div class="col-md-5">
                            ${imageSliderHtml}
                            <div class="product-detail-badge animate__animated animate__slow" style="color: #47b2e4; background: rgba(71, 178, 228, 0.1); padding: 5px 15px; border-radius: 20px; display: inline-block; margin: 15px 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">${product.type}</div>
                            
                            <div class="animate__animated animate__fadeInUp animate__slow" style="margin: 20px 0; text-align: center;">
                                <div id="priceDisplay">
                                    ${product.discountPrices ? `
                                        <div id="normalPriceDisplay" style="color: #ff4c4c; font-size: 16px; text-decoration: line-through; opacity: 0.7; margin-bottom: 5px;">
                                            Rp ${product.normalPrices ? product.normalPrices[0] : product.normalPrice}
                                        </div>
                                        <div id="discountPriceDisplay" style="color: #47e461; font-size: 28px; font-weight: 700;">
                                            Rp ${product.discountPrices[0]}
                                        </div>
                                    ` : `
                                        <div id="normalPriceDisplay" style="color: #47e461; font-size: 28px; font-weight: 700;">
                                            Rp ${product.normalPrices ? product.normalPrices[0] : product.normalPrice}
                                        </div>
                                    `}
                                </div>
                            </div>
                            <!-- Paket Selection -->
                            <div class="animate__animated animate__fadeInUp animate__slow" style="margin-top: 20px;">
                                <div style="color: #fff; font-size: 16px; font-weight: 600; margin-bottom: 10px; text-align: center;">
                                    Pilih Paket
                                </div>
                                <div class="paket-selection">
                                    ${product.pakets ? product.pakets.map((paket, index) => {
                                        const stockStatus = product.stock && product.stock[index] ? product.stock[index].trim() : 'READY';
                                        const isOutOfStock = stockStatus === 'KOSONG';
                                        const buttonClass = isOutOfStock ? 'paket-btn paket-disabled' : 'paket-btn';
                                        const buttonStyle = isOutOfStock ? 'background-color: #ff4c4c !important; color: #fff !important; cursor: not-allowed; opacity: 0.7;' : '';
                                        return `<button class="${buttonClass}" data-paket="${paket.trim()}" data-index="${index}" data-stock="${stockStatus}" style="${buttonStyle}" ${isOutOfStock ? 'disabled' : ''}>${paket.trim()}</button>`;
                                    }).join('') : ''}
                                </div>
                            </div>
                            <div class="animate__animated animate__fadeInUp animate__slow" style="margin-top: 15px; text-align: center;">
                                <a href="#" id="orderButton" class="modal-order-button" style="pointer-events: none; opacity: 0.5;">
                                    <i class="fas fa-shopping-cart"></i> Order Sekarang
                                </a>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="animate__animated animate__slow" style="color: #fff; font-size: 20px; font-weight: 600; margin-bottom: 15px;">
                                Spesifikasi
                            </div>
                            <div class="animate__animated animate__fadeIn animate__slow modal-specs-container">
                                ${specHtml}
                            </div>
                            
                            <div class="animate__animated animate__slow" style="color: #fff; font-size: 20px; font-weight: 600; margin: 25px 0 15px;">
                                Deskripsi
                            </div>
                            <div class="animate__animated animate__fadeIn animate__slow" style="color: #aaa; font-size: 14px; line-height: 1.6; white-space: pre-wrap;">${product.description}</div>
                        </div>
                    </div>
                </div>`,
            width: '900px',
            showCloseButton: true,
            showConfirmButton: false,
            background: 'rgba(13, 18, 26, 0.95)',
            didOpen: () => {
                const modalSlider = document.querySelector('.product-modal-slider');
                const modalImage = modalSlider.querySelector('.modal-image');
                
                // Handle paket selection
                let selectedPaket = null;
                const paketButtons = document.querySelectorAll('.paket-btn');
                const orderButton = document.getElementById('orderButton');
                const normalPriceDisplay = document.getElementById('normalPriceDisplay');
                const discountPriceDisplay = document.getElementById('discountPriceDisplay');
                
                paketButtons.forEach(btn => {
                    btn.addEventListener('click', () => {
                        // Check if button is disabled (out of stock)
                        const stockStatus = btn.getAttribute('data-stock');
                        if (stockStatus === 'KOSONG' || btn.disabled) {
                            return; // Don't proceed if out of stock
                        }
                        
                        // Remove active class from all buttons
                        paketButtons.forEach(b => {
                            b.classList.remove('active');
                        });
                        
                        // Add active class to clicked button
                        btn.classList.add('active');
                        
                        selectedPaket = btn.getAttribute('data-paket');
                        const priceIndex = parseInt(btn.getAttribute('data-index'));
                        
                        // Update price display based on selected paket
                        if (product.normalPrices && product.normalPrices[priceIndex]) {
                            normalPriceDisplay.textContent = `Rp ${product.normalPrices[priceIndex]}`;
                        }
                        
                        if (product.discountPrices && product.discountPrices[priceIndex] && discountPriceDisplay) {
                            discountPriceDisplay.textContent = `Rp ${product.discountPrices[priceIndex]}`;
                        }
                        
                        // Enable order button
                        orderButton.style.pointerEvents = 'auto';
                        orderButton.style.opacity = '1';
                        orderButton.href = `configure?product_id=${product.id_produk}&paket=${encodeURIComponent(selectedPaket)}&price_index=${priceIndex}`;
                    });
                });
                
                // Add zoom functionality
                modalImage.addEventListener('click', () => {
                    // Close current modal and open full image modal
                    Swal.close();
                    
                    Swal.fire({
                        imageUrl: images[currentImageIndex],
                        imageAlt: product.name,
                        width: '90%',
                        showCloseButton: true,
                        showConfirmButton: false,
                        background: 'rgba(13, 18, 26, 0.95)',
                        didClose: () => {
                            // Reopen original product detail modal when closed
                            createProductDetailModal();
                        }
                    });
                });

                if (!showNavigation) return;

                const prevButton = modalSlider.querySelector('.slider-prev');
                const nextButton = modalSlider.querySelector('.slider-next');

                prevButton.addEventListener('click', () => {
                    currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
                    modalImage.src = images[currentImageIndex];
                });

                nextButton.addEventListener('click', () => {
                    currentImageIndex = (currentImageIndex + 1) % images.length;
                    modalImage.src = images[currentImageIndex];
                });
            }
        });
    }

    // Initial call to create the modal
    createProductDetailModal();
}

// Category Filter Functionality
function initCategoryFilters() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    const productItems = document.querySelectorAll('.product-item');

    categoryButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            const category = button.getAttribute('data-category');

            productItems.forEach(item => {
                const itemCategory = item.getAttribute('data-category');
                
                if (category === 'all' || itemCategory === category) {
                    item.classList.remove('hide');
                } else {
                    item.classList.add('hide');
                }
            });
        });
    });
}

// Initialize functions when page loads
document.addEventListener('DOMContentLoaded', () => {
    initProductSlideshows();
    initCategoryFilters();
});