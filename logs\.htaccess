# Deny all access to logs folder
Order deny,allow
Deny from all

# Block access to all file types
<FilesMatch ".*">
    Order deny,allow
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Robots-Tag "noindex, nofollow"
</IfModule>

# Additional protection layers
<Files "*">
    Order deny,allow
    Deny from all
</Files>

# Prevent execution of any scripts
<IfModule mod_php5.c>
    php_flag engine off
</IfModule>
<IfModule mod_php7.c>
    php_flag engine off
</IfModule>

# Remove server signature
ServerSignature Off

# Prevent access via different methods
<Limit GET POST PUT DELETE HEAD OPTIONS TRACE CONNECT>
    Require all denied
</Limit> 