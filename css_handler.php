<?php
/**
 * CSS Handler - Ensure correct MIME type for CSS files
 * Usage: css_handler.php?file=css/style.css
 */

// Get the requested CSS file
$file = $_GET['file'] ?? '';

// Security: Only allow CSS files and prevent directory traversal
if (!$file || !preg_match('/^[a-zA-Z0-9\/_.-]+\.css$/', $file)) {
    http_response_code(400);
    exit('Invalid CSS file request');
}

// Remove any leading slashes and build the full path
$css_file = ltrim($file, '/');
$full_path = __DIR__ . '/assets/' . $css_file;

// Check if file exists
if (!file_exists($full_path) || !is_file($full_path)) {
    http_response_code(404);
    exit('CSS file not found: ' . $css_file);
}

// Set correct headers for CSS with enhanced CORS support
header('Content-Type: text/css; charset=utf-8');
header('Cache-Control: public, max-age=3600');

// Enhanced CORS headers for better compatibility
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
$host = $_SERVER['HTTP_HOST'] ?? '';

// Allow CORS for development servers
if (strpos($host, 'localhost') !== false || 
    strpos($host, '127.0.0.1') !== false ||
    strpos($host, 'institute-seeker.gl.at.ply.gg') !== false ||
    strpos($host, 'playit.plus') !== false ||
    strpos($host, 'playit.gg') !== false) {
    
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Accept, Origin, X-Requested-With');
    header('Access-Control-Max-Age: 3600');
}

// Handle OPTIONS request for CORS preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Read and process CSS content
$css_content = file_get_contents($full_path);

// Fix font paths for bootstrap-icons
if (strpos($css_file, 'bootstrap-icons') !== false) {
    // Replace relative font paths with absolute paths
    $css_content = str_replace(
        'url("./fonts/',
        'url("/assets/vendor/bootstrap-icons/fonts/',
        $css_content
    );
    // Also handle URLs without quotes
    $css_content = str_replace(
        'url(./fonts/',
        'url(/assets/vendor/bootstrap-icons/fonts/',
        $css_content
    );
}

// Fix font paths for boxicons
if (strpos($css_file, 'boxicons') !== false) {
    // Replace relative font paths with absolute paths
    $css_content = str_replace(
        'url("../fonts/',
        'url("/assets/vendor/boxicons/fonts/',
        $css_content
    );
    // Also handle URLs without quotes
    $css_content = str_replace(
        'url(../fonts/',
        'url(/assets/vendor/boxicons/fonts/',
        $css_content
    );
}

// Output the processed CSS content
echo $css_content;
?> 