<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

include '../../database/database.php';

$email = $_SESSION['email'];
$idchat = isset($_GET['idchat']) ? intval($_GET['idchat']) : 0;
$lastCheck = isset($_GET['last_check']) ? $_GET['last_check'] : '1970-01-01 00:00:00';

if (!$idchat) {
    echo json_encode(['success' => false, 'error' => 'No chat ID']);
    exit;
}

try {
    // Check for new messages since last check
    $stmt = $conn->prepare("SELECT COUNT(*) as new_count FROM chats WHERE idchat = ? AND created_at > ?");
    $stmt->bind_param("is", $idchat, $lastCheck);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    $hasNewMessages = $row['new_count'] > 0;
    
    echo json_encode([
        'success' => true,
        'has_new_messages' => $hasNewMessages,
        'new_count' => $row['new_count'],
        'current_time' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

$conn->close();
?> 