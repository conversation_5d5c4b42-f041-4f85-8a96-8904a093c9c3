<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

try {
    // Fetch all products
    $query = "SELECT * FROM produk ORDER BY id_produk ASC";
    $result = $conn->query($query);
    
    if (!$result) {
        throw new Exception("Error fetching products: " . $conn->error);
    }
    
} catch (Exception $e) {
    error_log("Products error: " . $e->getMessage());
    $_SESSION['error'] = "Ter<PERSON><PERSON> kesalahan saat memuat data produk. Silakan coba lagi nanti.";
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Products - Dopminer</title>
    <meta content="Dopminer Products" name="description">
    <meta content="hosting, server, VPS, game hosting" name="keywords">

    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    <link href="https://dopminer.com/Gambar/ezgif-6f3505eaac0f6a.png" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Products Specific CSS -->
    <link href="../assets/css/products.css" rel="stylesheet">
    
    <!-- Sweetalert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Remove inline styles -->
    <style>
        /* Minimal styles kept for any dynamic styling needs */
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>

    <section id="products">
        <div class="container" data-aos="fade-up">
            <div class="section-title">
                <h2>Our Products</h2>
                <div class="category-filters animate__animated animate__fadeIn" style="margin-bottom: 23px; margin-top: 45px;">
                    <button class="category-btn active" data-category="all">All Products</button>
                    <?php
                        // Get unique product types
                        $types_query = "SELECT DISTINCT type FROM produk ORDER BY type ASC";
                        $types_result = $conn->query($types_query);
                        
                        if ($types_result && $types_result->num_rows > 0) {
                            while($type_row = $types_result->fetch_assoc()) {
                                echo '<button class="category-btn" data-category="' . htmlspecialchars($type_row['type']) . '">' 
                                    . htmlspecialchars($type_row['type']) . '</button>';
                            }
                        }
                    ?>
                </div>
            </div>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger">
                    <?php 
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                    ?>
                </div>
            <?php endif; ?>

            <div class="row product-container">
                <?php if (isset($result) && $result->num_rows > 0): ?>
                    <?php while($row = $result->fetch_assoc()): ?>
                        <div class="col-lg-4 col-md-6 product-item" data-category="<?php echo htmlspecialchars($row['type']); ?>" data-aos="fade-up" data-aos-delay="100">
                            <div class="product-card">
                                <?php if ($row['image_links']): ?>
                                    <div class="product-image-slideshow">
                                        <?php
                                            $images = explode('|', $row['image_links']);
                                            foreach($images as $index => $image) {
                                                if (trim($image) !== '') {
                                                    echo '<img src="' . htmlspecialchars(trim($image)) . '" 
                                                         alt="' . htmlspecialchars($row['nama']) . '"
                                                         class="product-slide ' . ($index === 0 ? 'active' : '') . '">';
                                                }
                                            }
                                        ?>
                                    </div>
                                <?php else: ?>
                                    <div class="product-image-container">
                                        <img src="<?php 
                                            $gambar_links = $row['gambar'];
                                            $gambar_array = explode('|', $gambar_links);
                                            echo htmlspecialchars(trim($gambar_array[0])); 
                                        ?>" 
                                             alt="<?php echo htmlspecialchars($row['nama']); ?>"
                                             class="product-image">
                                    </div>
                                <?php endif; ?>
                                
                                <div class="product-title-container" style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                                    <h3 class="product-title" style="margin: 0; flex: 1;">
                                        <?php echo htmlspecialchars($row['nama']); ?>
                                    </h3>
                                    <span class="product-type-badge" style="background: linear-gradient(45deg, #47b2e4, #74b9ff); color: white; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; white-space: nowrap;">
                                        <?php echo htmlspecialchars($row['type']); ?>
                                    </span>
                                </div>
                                
                                <div class="product-description">
                                    <?php
                                        $specs = explode("\n", $row['spesifikasi']);
                                        foreach ($specs as $spec) {
                                            $spec = trim($spec);
                                            if (empty($spec)) continue;
                                            
                                            if (strpos($spec, 'CPU:') !== false) {
                                                echo '<div class="spec-item"><i class="fas fa-microchip"></i> ' . htmlspecialchars($spec) . '</div>';
                                            } elseif (strpos($spec, 'RAM:') !== false) {
                                                echo '<div class="spec-item"><i class="fas fa-memory"></i> ' . htmlspecialchars($spec) . '</div>';
                                            } elseif (strpos($spec, 'Bandwith:') !== false || strpos($spec, 'Bandwidth:') !== false) {
                                                echo '<div class="spec-item"><i class="fas fa-network-wired"></i> ' . htmlspecialchars($spec) . '</div>';
                                            } elseif (strpos($spec, 'DDOS') !== false) {
                                                echo '<div class="spec-item"><i class="fas fa-shield-alt"></i> ' . htmlspecialchars($spec) . '</div>';
                                            } elseif (strpos($spec, 'Speed') !== false) {
                                                echo '<div class="spec-item"><i class="fas fa-tachometer-alt"></i> ' . htmlspecialchars($spec) . '</div>';
                                            } else {
                                                echo '<div class="spec-item"><i class="fas fa-check"></i> ' . htmlspecialchars($spec) . '</div>';
                                            }
                                        }
                                    ?>
                                </div>
                                
                                <div class="price-container">
                                <?php 
                                    // Parse prices as arrays to get the lowest price
                                    $normalPrices = explode(',', $row['harga_normal']);
                                    $discountPrices = $row['harga_diskon'] ? explode(',', $row['harga_diskon']) : null;
                                    
                                    // Get the first (lowest) price
                                    $lowestNormalPrice = trim($normalPrices[0]);
                                    $lowestDiscountPrice = $discountPrices ? trim($discountPrices[0]) : null;
                                    
                                    // Parse paket to get the smallest paket
                                    $pakets = explode(',', $row['paket']);
                                    $smallestPaket = trim($pakets[0]);
                                ?>
                                <?php if ($lowestDiscountPrice): ?>
                                        <div class="price-discount">
                                            Rp <?php echo number_format($lowestDiscountPrice, 0, ',', '.'); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="price-normal">
                                        Rp <?php echo number_format($lowestNormalPrice, 0, ',', '.'); ?>
                                    </div>
                                    <div class="paket-info" style="font-size: 12px; color: #888; margin-top: 5px;">
                                        Mulai dari <?php echo htmlspecialchars($smallestPaket); ?>
                                    </div>
                                </div>

                                <button type="button" 
                                        class="product-details-btn"
                                        onclick='showProductDetail(<?php 
                                            // Parse prices as arrays
                                            $normalPrices = explode(',', $row['harga_normal']);
                                            $discountPrices = $row['harga_diskon'] ? explode(',', $row['harga_diskon']) : null;
                                            
                                            // Format prices
                                            $formattedNormalPrices = array_map(function($price) {
                                                return number_format(trim($price), 0, ',', '.');
                                            }, $normalPrices);
                                            
                                            $formattedDiscountPrices = null;
                                            if ($discountPrices) {
                                                $formattedDiscountPrices = array_map(function($price) {
                                                    return number_format(trim($price), 0, ',', '.');
                                                }, $discountPrices);
                                            }
                                            
                                            // Parse paket as array
                                            $pakets = explode(',', $row['paket']);
                                            $trimmedPakets = array_map('trim', $pakets);
                                            
                                            // Parse stock as array
                                            $stock = explode(',', $row['stock']);
                                            $trimmedStock = array_map('trim', $stock);
                                            
                                            echo htmlspecialchars(json_encode([
                                            "id" => $row['id'],
                                            "id_produk" => $row['id_produk'],
                                            "image" => $row['gambar'],
                                            "image_links" => $row['image_links'] ? $row['image_links'] : $row['gambar'],
                                            "name" => $row['nama'],
                                            "type" => $row['type'],
                                            "description" => $row['deskripsi'],
                                            "spesifikasi" => $row['spesifikasi'],
                                            "normalPrice" => $formattedNormalPrices[0], // For backward compatibility
                                            "discountPrice" => $formattedDiscountPrices ? $formattedDiscountPrices[0] : null, // For backward compatibility
                                            "normalPrices" => $formattedNormalPrices,
                                            "discountPrices" => $formattedDiscountPrices,
                                            "pakets" => $trimmedPakets,
                                            "stock" => $trimmedStock
                                        ]), ENT_QUOTES, 'UTF-8');
                                        ?>)'>
                                    View Details
                                </button>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p style="color: #fff;">No products found.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>

    <!-- Sweetalert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>

    <!-- Products Specific JS -->
    <script src="../assets/js/products.js"></script>

    <!-- Remove inline script -->
    <script>
        // Minimal script if needed
    </script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

    <!-- Category Filter Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.category-btn');
            const productItems = document.querySelectorAll('.product-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    button.classList.add('active');

                    const category = button.getAttribute('data-category');

                    productItems.forEach(item => {
                        const itemCategory = item.getAttribute('data-category');
                        
                        if (category === 'all' || category === itemCategory) {
                            item.classList.remove('hide');
                            // Reset the animation
                            item.style.animation = 'none';
                            item.offsetHeight; // Trigger reflow
                            item.style.animation = null;
                            
                            // Add fade-in animation
                            item.style.opacity = '0';
                            setTimeout(() => {
                                item.style.opacity = '1';
                                item.style.transition = 'opacity 0.4s ease-in-out';
                            }, 50);
                        } else {
                            item.classList.add('hide');
                        }
                    });
                });
            });
        });
    </script>
</body>

</html>