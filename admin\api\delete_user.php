<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized']);
    exit;
}

include __DIR__ . '/../../database/database.php';

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['status' => 'error', 'message' => 'Access denied']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'User email is required']);
    exit;
}

$userEmail = $input['email'];

// Prevent deleting current admin
if ($userEmail === $email) {
    echo json_encode(['status' => 'error', 'message' => 'You cannot delete your own account']);
    exit;
}

try {
    // Check if user is admin before deleting (extra protection)
    $checkStmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
    $checkStmt->bind_param("s", $userEmail);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    $targetUser = $checkResult->fetch_assoc();
    
    if ($targetUser && $targetUser['group'] === 'ADMIN') {
        echo json_encode(['status' => 'error', 'message' => 'Cannot delete admin users']);
        exit;
    }
    
    // Delete user
    $stmt = $conn->prepare("DELETE FROM users WHERE email = ? AND `group` != 'ADMIN'");
    $stmt->bind_param("s", $userEmail);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'User not found or cannot be deleted']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete user']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>