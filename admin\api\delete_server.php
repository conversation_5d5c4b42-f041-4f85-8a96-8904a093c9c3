<?php
// Start session if not already started
session_start();
require_once '../../database/database.php';

header('Content-Type: application/json');

// Cek apakah user adalah admin
if (!isset($_SESSION['loggedin']) || $_SESSION['group'] !== 'ADMIN') {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Ambil dan validasi input
$input = json_decode(file_get_contents('php://input'), true);
$serverId = isset($input['server_id']) ? intval($input['server_id']) : null;

if (!$serverId) {
    echo json_encode([
        'success' => false,
        'message' => 'ID Server tidak valid'
    ]);
    exit;
}

try {
    // Cek apakah server exists
    $checkQuery = "SELECT id FROM data_client WHERE id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param('i', $serverId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Server tidak ditemukan'
        ]);
        exit;
    }

    // Hapus server
    $deleteQuery = "DELETE FROM data_client WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param('i', $serverId);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Server berhasil dihapus'
        ]);
    } else {
        throw new Exception("Gagal menghapus server");
    }

} catch (Exception $e) {
    error_log("Error deleting server: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat menghapus server'
    ]);
}

$conn->close();
?> 