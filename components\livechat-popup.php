<?php
// Don't start session if already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$is_logged_in = isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true;
$is_admin = false;
$email = '';
$idchat = '';

if ($is_logged_in) {
    // Use correct path for database include
    $database_path = __DIR__ . '/../database/database.php';
    if (file_exists($database_path)) {
        include $database_path;
    } else {
        // Fallback path
        include '../database/database.php';
    }

    $email = $_SESSION['email'];

    // Check if database connection exists
    if (isset($conn) && $conn) {
        // Check if user is admin
        try {
            $stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
            if ($stmt) {
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $result = $stmt->get_result();
                $user = $result->fetch_assoc();
                $is_admin = ($user && $user['group'] === 'ADMIN');
            }
        } catch (Exception $e) {
            $is_admin = false; // Default to non-admin if error
        }

        // Get or create idchat
        try {
            if ($is_admin && isset($_GET['idchat']) && intval($_GET['idchat']) > 0) {
                $idchat = intval($_GET['idchat']);
            } else {
                $stmt = $conn->prepare("SELECT idchat FROM chats WHERE email = ? LIMIT 1");
                if ($stmt) {
                    $stmt->bind_param("s", $email);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $chat = $result->fetch_assoc();
                    
                    if ($chat) {
                        $idchat = $chat['idchat'];
                    } else {
                        $idchat = rand(100000, 999999);
                    }
                } else {
                    $idchat = rand(100000, 999999);
                }
            }
        } catch (Exception $e) {
            $idchat = rand(100000, 999999); // Fallback idchat
        }
    }
}
?>

<!-- Livechat Toggle Button -->
<div id="livechat-toggle" class="livechat-toggle">
    <i class="bi bi-chat-dots"></i>
    <?php if ($is_logged_in): ?>
        <span id="toggle-unread-badge" class="notification-badge" style="display: none;">0</span>
    <?php endif; ?>
</div>

<!-- Livechat Popup -->
<div id="livechat-popup" class="livechat-popup">
    <div class="livechat-header">
        <div class="header-info">
            <div class="header-title">
                <?php if ($is_logged_in): ?>
                    <?php echo $is_admin ? 'Admin Inbox' : 'Live Chat'; ?>
                <?php else: ?>
                    Live Chat
                <?php endif; ?>
            </div>
            <div class="header-subtitle">
                <?php if ($is_logged_in): ?>
                    <?php echo $is_admin ? 'Kelola Chat Users' : 'Chat dengan Admin'; ?>
                <?php else: ?>
                    Bantuan & Dukungan
                <?php endif; ?>
            </div>
        </div>
        <div class="header-actions">
            <?php if ($is_logged_in && $is_admin): ?>
                <button id="back-to-inbox" class="header-btn" style="display: none;" title="Kembali ke Inbox">
                    <i class="bi bi-arrow-left"></i>
                </button>
            <?php endif; ?>
            <button id="minimize-chat" class="header-btn">
                <i class="bi bi-dash"></i>
            </button>
            <button id="close-chat" class="header-btn">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <?php if ($is_logged_in): ?>
            <span id="unread-badge" class="notification-badge" style="display: none;">0</span>
        <?php endif; ?>
    </div>
    
    <?php if (!$is_logged_in): ?>
    <!-- Login Required Panel -->
    <div id="login-required" class="login-required-panel">
        <div class="login-message">
            <div class="login-icon">
                <i class="bi bi-lock"></i>
            </div>
            <h4>Akses Chat Membutuhkan Login</h4>
            <p>Untuk menggunakan fitur live chat dan mendapatkan bantuan dari tim support kami, silakan login terlebih dahulu.</p>
            <div class="login-benefits">
                <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Chat langsung dengan admin</span>
                </div>
                <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Respon cepat 24/7</span>
                </div>
                <!-- <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Riwayat percakapan tersimpan</span>
                </div> -->
            </div>
                         <div class="login-actions">
                 <a href="<?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) ? '../auth/login' : 'auth/login'; ?>" class="btn-login">
                     <i class="bi bi-box-arrow-in-right"></i>
                     Login Sekarang
                 </a>
                 <a href="<?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) ? '../auth/register' : 'auth/register'; ?>" class="btn-register">
                     <i class="bi bi-person-plus"></i>
                     Daftar Gratis
                 </a><br>
             </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Logged In Chat Interface -->
    <?php if ($is_admin): ?>
    <!-- Admin Inbox Panel -->
    <div id="admin-inbox" class="admin-inbox">
        <div class="inbox-header">
            <h4><i class="bi bi-inbox"></i> Chat Inbox</h4>
            <button id="refresh-inbox" class="refresh-btn" title="Refresh Inbox">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
        <div id="chat-users-list" class="chat-users-list">
            <!-- Chat users will be loaded here -->
        </div>
    </div>
    <?php endif; ?>
    
    <div id="chat-messages" class="chat-messages" <?php echo $is_admin ? 'style="display: none;"' : ''; ?>>
        <!-- Messages will be loaded here -->
    </div>
    
    <div class="chat-input" <?php echo $is_admin ? 'style="display: none;"' : ''; ?>>
        <input type="text" id="message-input" placeholder="💬 Ketik pesan Anda di sini..." maxlength="500" autocomplete="off">
        <button id="send-btn" class="send-btn" title="Kirim pesan">
            <i class="bi bi-send"></i>
        </button>
    </div>
    
    <!-- Auto Scroll Toggle Button -->
    <div id="auto-scroll-toggle" class="auto-scroll-toggle" title="Toggle Auto Scroll">
        <i class="bi bi-arrow-down-circle-fill"></i>
    </div>
    <?php endif; ?>
</div>

<style>
/* Force hide popup initially - but allow animation */
#livechat-popup:not(.show) {
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

.livechat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(30, 60, 114, 0.3);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    font-size: 24px;
}

.livechat-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.4);
}

.livechat-toggle.shake {
    animation: shake 0.6s ease-in-out;
}

.livechat-toggle.bounce {
    animation: bounce-click 0.4s ease-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes bounce-click {
    0% { transform: translateY(-2px) scale(1); }
    30% { transform: translateY(-6px) scale(1.05); }
    60% { transform: translateY(-2px) scale(0.98); }
    100% { transform: translateY(-2px) scale(1); }
}

/* Livechat popup main styles - loaded after initial hide */
.livechat-popup {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 350px !important;
    height: 500px !important;
    background: white !important;
    border-radius: 15px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1) !important;
    z-index: 1001 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    border: 1px solid #e9ecef !important;
    overscroll-behavior: contain !important;
    touch-action: pan-y !important;
    
    /* Initial hidden state */
    transform: translateY(100%) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    
    /* Animation properties */
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.4s ease !important;
}

.livechat-popup.show {
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Keyframe animations for better control */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
        visibility: hidden;
    }
    to {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    to {
        transform: translateY(100%);
        opacity: 0;
        visibility: hidden;
    }
}

.livechat-popup.animate-in {
    animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.livechat-popup.animate-out {
    animation: slideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}



.livechat-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: white !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: relative !important;
    flex-shrink: 0 !important;
}

.header-info {
    flex: 1;
}

.header-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.header-subtitle {
    font-size: 12px;
    opacity: 0.9;
    margin: 2px 0 0 0;
}

.header-actions {
    display: flex;
    gap: 5px;
}

.header-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: inline-block;
    line-height: 14px;
    text-align: center;
    font-weight: 700;
    border: 2px solid white;
    box-shadow: 0 1px 4px rgba(255, 71, 87, 0.5);
    z-index: 1002;
    animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
    0% {
        transform: scale(1);
        box-shadow: 0 1px 4px rgba(255, 71, 87, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 1px 4px rgba(255, 71, 87, 0.5);
    }
}

/* Login Required Panel Styles */
.login-required-panel {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    text-align: center !important;
    min-height: 0 !important;
}

.login-message {
    max-width: 280px;
}

.login-icon {
    font-size: 48px;
    color: #1e3c72;
    margin-bottom: 15px;
}

.login-message h4 {
    color: #1e3c72;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.login-message p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.login-benefits {
    margin-bottom: 25px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #495057;
}

.benefit-item i {
    color: #28a745;
    font-size: 14px;
}

.login-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-login, .btn-register {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-login {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
}

.btn-login:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
    color: white;
    text-decoration: none;
}

.btn-register {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #e9ecef;
}

.btn-register:hover {
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .livechat-popup {
        width: 320px;
        height: 450px;
        bottom: 15px;
        right: 15px;
    }
    
    .livechat-toggle {
        bottom: 15px;
        right: 15px;
        width: 55px;
        height: 55px;
        font-size: 22px;
    }
    
    .login-message {
        max-width: 250px;
    }
    
    .login-message h4 {
        font-size: 16px;
    }
    
    .login-message p {
        font-size: 13px;
    }
}

/* Auto Scroll Toggle Button */
.auto-scroll-toggle {
    position: absolute;
    bottom: 70px;
    right: 15px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(30, 60, 114, 0.3);
    z-index: 1003;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    font-size: 16px;
    opacity: 0.8;
}

.auto-scroll-toggle:hover {
    opacity: 1;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.4);
}

.auto-scroll-toggle.disabled {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    opacity: 0.6;
}

.auto-scroll-toggle.disabled:hover {
    opacity: 0.8;
}

.auto-scroll-toggle.pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 2px 10px rgba(30, 60, 114, 0.3);
    }
    50% {
        box-shadow: 0 2px 20px rgba(30, 60, 114, 0.6);
    }
    100% {
        box-shadow: 0 2px 10px rgba(30, 60, 114, 0.3);
    }
}

/* Admin Inbox Styles */
.admin-inbox {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    background: #f8f9fa !important;
    min-height: 0 !important;
    transition: all 0.3s ease-in-out !important;
}

.inbox-header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
}

.inbox-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1e3c72;
}

.refresh-btn {
    background: none;
    border: none;
    color: #1e3c72;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s;
}

.refresh-btn:hover {
    background: rgba(30, 60, 114, 0.1);
}

.chat-users-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    overscroll-behavior: contain;
}

.chat-user-item {
    background: white;
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e9ecef;
    position: relative;
    animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.chat-user-item:hover {
    background: #f0f7ff;
    border-color: #1e3c72;
    transform: translateY(-1px);
}

.chat-user-item:hover .user-name {
    color: #1e3c72;
}

.chat-user-item:hover .message-time {
    color: #2a5298;
}

.chat-user-item.active {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border-color: #1e3c72;
}

.chat-user-item.active .user-name,
.chat-user-item.active .user-email,
.chat-user-item.active .last-message,
.chat-user-item.active .message-time {
    color: white;
    opacity: 1;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 5px;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
}

.user-email {
    font-size: 11px;
    opacity: 0.7;
    color: #666;
}

.last-message {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: #555;
}

.message-time {
    font-size: 10px;
    opacity: 0.6;
    color: #888;
}

.unread-count {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    position: absolute;
    top: 8px;
    right: 8px;
}

.chat-user-item.active .unread-count {
    background: rgba(255, 255, 255, 0.3);
}

.no-chats {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-chats h5 {
    color: #495057;
    margin-bottom: 10px;
}

.no-chats p {
    color: #6c757d;
}

.no-chats i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
    color: #6c757d;
}

.chat-messages {
    flex: 1 !important;
    padding: 15px !important;
    overflow-y: auto !important;
    background: #f8f9fa !important;
    min-height: 0 !important;
    overscroll-behavior: contain !important;
    display: block !important;
    transition: all 0.3s ease-in-out !important;
    scroll-behavior: smooth !important;
    /* Prevent content jumping during updates */
    contain: layout style !important;
}

.message {
    margin-bottom: 12px;
    max-width: 80%;
    word-wrap: break-word;
    animation: fadeInUp 0.3s ease-out;
    /* Prevent layout shifts */
    will-change: transform;
    transform: translateZ(0);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(5px) translateZ(0);
    }
    to {
        opacity: 1;
        transform: translateY(0) translateZ(0);
    }
}

/* Prevent flash of content during updates */
.chat-messages.updating {
    pointer-events: none;
}

.chat-messages.updating .message {
    animation: none;
}

/* Smooth transitions for message container */
.chat-messages-container {
    transition: opacity 0.2s ease-in-out;
}

.chat-messages-container.loading {
    opacity: 0.7;
}

.message.sent {
    margin-left: auto;
    text-align: right;
}

.message.sent > div:first-child {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 18px 18px 5px 18px;
    display: inline-block;
}

.message.received > div:first-child {
    background: white;
    color: #333;
    padding: 10px 15px;
    border-radius: 18px 18px 18px 5px;
    display: inline-block;
    border: 1px solid #e9ecef;
}

.message.admin > div:first-child {
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 18px 18px 18px 5px;
    display: inline-block;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
}

.chat-input {
    padding: 15px !important;
    background: white !important;
    border-top: 1px solid #e9ecef !important;
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
    min-height: 75px !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
    position: relative !important;
    transition: all 0.3s ease-in-out !important;
}

.chat-input input {
    flex: 1;
    border: 2px solid #ddd;
    border-radius: 25px;
    padding: 12px 18px;
    outline: none;
    font-size: 14px;
    background: #f8f9fa;
    color: #333;
    height: 45px;
    box-sizing: border-box;
    font-family: inherit;
    transition: all 0.2s ease;
}

.chat-input input::placeholder {
    color: #6c757d;
    opacity: 1;
}

.chat-input input:focus {
    border-color: #2a5298;
    background: white;
    box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
}

.send-btn {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.send-btn:hover {
    transform: scale(1.05);
}

.auto-scroll-toggle {
    position: absolute;
    bottom: 90px;
    right: 15px;
    background: rgba(30, 60, 114, 0.8);
    color: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 18px;
    opacity: 0.7;
}

.auto-scroll-toggle:hover {
    opacity: 1;
    transform: scale(1.1);
}

.auto-scroll-toggle.disabled {
    background: rgba(108, 117, 125, 0.8);
}

/* Force Flexbox Layout - Override any external CSS */
#livechat-popup {
    display: flex !important;
    flex-direction: column !important;
}

#livechat-popup .livechat-header {
    flex-shrink: 0 !important;
}

#livechat-popup .chat-messages {
    flex: 1 !important;
    min-height: 0 !important;
}

#livechat-popup .chat-input {
    flex-shrink: 0 !important;
}

#livechat-popup .admin-inbox {
    flex: 1 !important;
    min-height: 0 !important;
}

#livechat-popup .login-required-panel {
    flex: 1 !important;
    min-height: 0 !important;
}

/* Admin View States - Class-based approach for better control */
.livechat-popup .admin-inbox {
    display: flex !important;
    flex: 1 !important;
    min-height: 0 !important;
    flex-direction: column !important;
}

.livechat-popup .admin-inbox.hidden {
    display: none !important;
}

.livechat-popup .admin-inbox.visible {
    display: flex !important;
}

.livechat-popup .chat-messages {
    flex: 1 !important;
    min-height: 0 !important;
}

.livechat-popup .chat-messages.hidden {
    display: none !important;
}

.livechat-popup .chat-messages.visible {
    display: block !important;
}

.livechat-popup .chat-input {
    flex-shrink: 0 !important;
}

.livechat-popup .chat-input.hidden {
    display: none !important;
}

.livechat-popup .chat-input.visible {
    display: flex !important;
}

/* Only override when JavaScript explicitly sets display styles */
.livechat-popup[id="livechat-popup"] .admin-inbox[style*="display: none"] {
    display: none !important;
}

.livechat-popup[id="livechat-popup"] .chat-messages[style*="display: none"] {
    display: none !important;
}

.livechat-popup[id="livechat-popup"] .chat-input[style*="display: none"] {
    display: none !important;
}

.livechat-popup[id="livechat-popup"] .admin-inbox[style*="display: flex"] {
    display: flex !important;
    flex-direction: column !important;
}

.livechat-popup[id="livechat-popup"] .chat-messages[style*="display: block"] {
    display: block !important;
}

.livechat-popup[id="livechat-popup"] .chat-input[style*="display: flex"] {
    display: flex !important;
}



/* Mobile Responsive Updates */
@media (max-width: 576px) {
    #livechat-popup {
        width: calc(100vw - 30px) !important;
        height: calc(100vh - 100px) !important;
        bottom: 15px !important;
        right: 15px !important;
        left: 15px !important;
        max-width: none !important;
    }
    
    .livechat-toggle {
        bottom: 15px !important;
        right: 15px !important;
    }
    
    .login-actions {
        flex-direction: column !important;
        gap: 8px !important;
    }
    
    .btn-login, .btn-register {
        padding: 10px 16px !important;
        font-size: 13px !important;
    }
}
</style>

<script>
// Immediately hide popup before DOM is ready (less aggressive)
(function() {
    const style = document.createElement('style');
    style.textContent = `
        #livechat-popup:not(.show) {
            display: none !important;
            visibility: hidden !important;
        }
    `;
    document.head.appendChild(style);
})();

document.addEventListener('DOMContentLoaded', function() {
    // Ensure popup is initially hidden (only if not showing)
    const popup = document.getElementById('livechat-popup');
    if (popup && !popup.classList.contains('show')) {
        popup.style.display = 'none';
        popup.style.visibility = 'hidden';
        popup.style.opacity = '0';
    }

    // Add small delay to ensure all elements are rendered
    setTimeout(function() {
        // Check if elements exist before proceeding
        const popup = document.getElementById('livechat-popup');
        const toggle = document.getElementById('livechat-toggle');
        const minimizeBtn = document.getElementById('minimize-chat');
        const closeBtn = document.getElementById('close-chat');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        const chatMessages = document.getElementById('chat-messages');
        const unreadBadge = document.getElementById('unread-badge');
        const toggleUnreadBadge = document.getElementById('toggle-unread-badge');
        
        // If essential elements don't exist, exit  
        if (!popup || !toggle) {
            console.error('Essential livechat elements not found:', {
                popup: !!popup,
                toggle: !!toggle
            });
            console.error('Available elements:', {
                allElements: document.querySelectorAll('*').length,
                divs: document.querySelectorAll('div').length,
                livechatElements: document.querySelectorAll('[id*="livechat"]').length
            });
            return;
        }
        
                        // console.log('Essential livechat elements found successfully');
        
        // Check if user is logged in - define at global scope
        window.isLoggedIn = <?php echo $is_logged_in ? 'true' : 'false'; ?>;
        
        // For non-logged in users, we only need basic functionality
        if (!window.isLoggedIn) {
            // Only check for basic elements needed for login screen
            if (!popup || !toggle) {
                return;
            }
        } else {
            // For logged in users, check for chat elements
            if (!messageInput || !sendBtn || !chatMessages) {
                console.error('Chat elements not found for logged in user:', {
                    messageInput: !!messageInput,
                    sendBtn: !!sendBtn,
                    chatMessages: !!chatMessages
                });
                return;
            }
        }
        
        initializeLivechat();
        
    }, 100);
    
    function initializeLivechat() {
        const popup = document.getElementById('livechat-popup');
        const toggle = document.getElementById('livechat-toggle');
        const minimizeBtn = document.getElementById('minimize-chat');
        const closeBtn = document.getElementById('close-chat');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        const chatMessages = document.getElementById('chat-messages');
        const unreadBadge = document.getElementById('unread-badge');
        const toggleUnreadBadge = document.getElementById('toggle-unread-badge');
        
        // Detect if we're in admin directory and set correct API path
        const isAdminPath = window.location.pathname.includes('/admin/');
        const currentPath = window.location.pathname;
        
        let apiPath;
        if (isAdminPath) {
            apiPath = '../api/';
        } else if (currentPath.includes('/pages/')) {
            apiPath = '../api/';
        } else {
            apiPath = 'api/';
        }
        
        const pagesPath = isAdminPath ? '../pages/' : 'pages/';
        
        let isPopupOpen = false;
        let idchat = <?php echo $idchat ? $idchat : 0; ?>;
        let currentEmail = '<?php echo $email; ?>';
        let isAdmin = <?php echo $is_admin ? 'true' : 'false'; ?>;
        
        // For non-admin users, use PHP idchat if available, otherwise generate new one
        if (!isAdmin) {
            if (idchat && idchat > 0) {
                // Use PHP idchat (from existing database record)
                // console.log('Using PHP idchat from database:', idchat);
                sessionStorage.setItem('livechat_idchat', idchat);
            } else {
                // Check sessionStorage first
                const storedIdchat = parseInt(sessionStorage.getItem('livechat_idchat'));
                if (storedIdchat && storedIdchat > 0) {
                    idchat = storedIdchat;
                    // console.log('Using stored idchat:', idchat);
                } else {
                    // Generate new idchat only if no existing data
                    idchat = Math.floor(Math.random() * 900000) + 100000;
                    // console.log('Generated new idchat for user:', idchat);
                    sessionStorage.setItem('livechat_idchat', idchat);
                }
            }
        }
        
        // console.log('Final idchat for user:', idchat, 'isAdmin:', isAdmin);
        let currentChatUser = null;
        let inboxView = true; // For admin: true = inbox view, false = chat view
        let autoScrollEnabled = true; // Auto scroll state
        let lastUnreadCount = 0;
        let notificationInterval = null;
        let hasUnreadMessages = false;
        let lastAdminUnreadCount = 0; // Track admin unread count for notifications
        let isLoadingChatUsers = false; // Prevent multiple loading requests
        
        // Audio notification functions
        function playNotificationSound() {
            try {
                // Detect correct path based on current location
                let audioPath = 'assets/notifpesan.mp3';
                if (window.location.pathname.includes('/pages/') || window.location.pathname.includes('/admin/')) {
                    audioPath = '../assets/notifpesan.mp3';
                }
                
                var audio = new Audio(audioPath);
                audio.volume = 0.5;
                var playPromise = audio.play();
                if (playPromise !== undefined) {
                    playPromise.catch(e => {
                        console.log('Audio play failed:', e);
                        // Try alternative path
                        try {
                            var altAudio = new Audio('/assets/notifpesan.mp3');
                            altAudio.volume = 0.5;
                            altAudio.play().catch(err => console.log('Alt audio also failed:', err));
                        } catch (altE) {
                            console.log('Alternative audio error:', altE);
                        }
                    });
                }
            } catch (e) {
                console.log('Audio error:', e);
            }
        }
        
        function startNotificationLoop() {
            if (notificationInterval) {
                clearInterval(notificationInterval);
            }
            
            hasUnreadMessages = true;
            
            // Play notification immediately
            playNotificationSound();
            
            // Then play every 20 seconds
            notificationInterval = setInterval(function() {
                if (hasUnreadMessages && !isPopupOpen) {
                    playNotificationSound();
                } else {
                    clearInterval(notificationInterval);
                    notificationInterval = null;
                }
            }, 20000); // 20 seconds
        }
        
        function stopNotificationLoop() {
            hasUnreadMessages = false;
            if (notificationInterval) {
                clearInterval(notificationInterval);
                notificationInterval = null;
            }
        }
        
        // Toggle popup
        if (toggle) {
            toggle.addEventListener('click', function() {
                if (isPopupOpen) {
                    closePopup();
                } else {
                    // Add bounce animation when clicking
                    toggle.classList.add('bounce');
                    setTimeout(() => {
                        toggle.classList.remove('bounce');
                    }, 400);
                    openPopup();
                }
            });
        }
        
        // Minimize button
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', function() {
                closePopup();
            });
        }
        
        // Close button
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                closePopup();
            });
        }
        
        // Auto scroll toggle button - with null check
        const autoScrollToggle = document.getElementById('auto-scroll-toggle');
        if (autoScrollToggle) {
            autoScrollToggle.addEventListener('click', function() {
                toggleAutoScroll();
            });
        } else {
            console.warn('Auto scroll toggle button not found, continuing without it');
        }
        
        // Admin inbox functionality with better error handling
        if (isAdmin) {
            // Wait a bit for DOM to be fully rendered
            setTimeout(() => {
                const refreshInboxBtn = document.getElementById('refresh-inbox');
                const backToInboxBtn = document.getElementById('back-to-inbox');
                
                if (refreshInboxBtn) {
                    refreshInboxBtn.addEventListener('click', function() {
                        // console.log('Refresh button clicked');
                        if (!isLoadingChatUsers && typeof loadChatUsers === 'function') {
                            loadChatUsers();
                        } else if (isLoadingChatUsers) {
                            console.log('Already loading, refresh ignored');
                        } else {
                            console.warn('loadChatUsers function not available');
                        }
                    });
                } else {
                    console.warn('Refresh inbox button not found');
                }
                
                if (backToInboxBtn) {
                    backToInboxBtn.addEventListener('click', function() {
                        // console.log('Back to inbox button clicked');
                        if (typeof showInboxView === 'function') {
                            showInboxView();
                            // Reload chat users when going back to inbox
                            if (!isLoadingChatUsers) {
                                setTimeout(() => {
                                    loadChatUsers();
                                }, 100);
                            }
                        } else {
                            console.warn('showInboxView function not available');
                        }
                    });
                } else {
                    console.warn('Back to inbox button not found');
                }
                
                // Load chat users immediately for admin after DOM is ready
                // console.log('Admin DOM ready, loading chat users...');
                loadChatUsers();
            }, 300);
        }
        
        // Add scroll event listener to detect manual scrolling
        if (chatMessages) {
            chatMessages.addEventListener('scroll', function(e) {
                e.stopPropagation(); // Prevent scroll event from bubbling
                checkIfUserScrolledUp();
            });
            
            // Prevent wheel event from propagating to body
            chatMessages.addEventListener('wheel', function(e) {
                e.stopPropagation();
                
                // Check if we're at the top or bottom and prevent further scrolling
                const { scrollTop, scrollHeight, clientHeight } = chatMessages;
                
                if (e.deltaY < 0 && scrollTop === 0) {
                    // Scrolling up at the top
                    e.preventDefault();
                } else if (e.deltaY > 0 && scrollTop + clientHeight >= scrollHeight) {
                    // Scrolling down at the bottom
                    e.preventDefault();
                }
            }, { passive: false });
        }
        
        // Add scroll prevention for chat users list (admin inbox)
        const chatUsersList = document.getElementById('chat-users-list');
        if (chatUsersList) {
            chatUsersList.addEventListener('scroll', function(e) {
                e.stopPropagation();
            });
            
            chatUsersList.addEventListener('wheel', function(e) {
                e.stopPropagation();
                
                const { scrollTop, scrollHeight, clientHeight } = chatUsersList;
                
                if (e.deltaY < 0 && scrollTop === 0) {
                    e.preventDefault();
                } else if (e.deltaY > 0 && scrollTop + clientHeight >= scrollHeight) {
                    e.preventDefault();
                }
            }, { passive: false });
        }
        
        // Initialize auto scroll button
        setTimeout(() => {
            updateAutoScrollButton();
        }, 300);
        
        // Ultra-smooth requestAnimationFrame animation
        function animateSlide(element, fromY, toY, fromOpacity, toOpacity, duration, callback) {
            if (isAnimating) return;
            isAnimating = true;
            
            const startTime = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Enhanced easing function (ease-out cubic for ultra-smooth)
                const easeOut = 1 - Math.pow(1 - progress, 3);
                
                // Calculate current values with sub-pixel precision
                const currentY = fromY + (toY - fromY) * easeOut;
                const currentOpacity = fromOpacity + (toOpacity - fromOpacity) * easeOut;
                
                // Apply styles with important flag and sub-pixel precision
                element.style.setProperty('transform', `translateY(${currentY.toFixed(3)}%)`, 'important');
                element.style.setProperty('opacity', currentOpacity.toFixed(3), 'important');
                
                // Force display and visibility during animation
                if (progress === 0 || elapsed < 16) { // First frame
                    element.style.setProperty('display', 'flex', 'important');
                    element.style.setProperty('visibility', 'visible', 'important');
                    element.style.setProperty('pointer-events', 'auto', 'important');
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Animation complete - set final values
                    element.style.setProperty('transform', `translateY(${toY}%)`, 'important');
                    element.style.setProperty('opacity', toOpacity, 'important');
                    isAnimating = false;
                    if (callback) callback();
                }
            }
            
            requestAnimationFrame(animate);
        }

        let isAnimating = false;

        function openPopup() {
            if (!popup || isAnimating) return;
            
            isPopupOpen = true;
            
            // Stop notifications when popup is opened
            stopNotificationLoop();
            
            // Hide toggle button with smooth fade when popup is open
            const toggleButton = document.getElementById('livechat-toggle');
            if (toggleButton) {
                toggleButton.style.setProperty('opacity', '0', 'important');
                toggleButton.style.setProperty('transform', 'scale(0.8)', 'important');
                setTimeout(() => {
                    toggleButton.style.setProperty('display', 'none', 'important');
                }, 200);
            }
            
            // Prevent body scroll when popup is open
            document.body.style.overflow = 'hidden';
            
            // Remove any CSS classes that might hide popup
            popup.classList.remove('hide', 'hidden');
            
            // Set initial state with important flags
            popup.style.setProperty('display', 'flex', 'important');
            popup.style.setProperty('visibility', 'visible', 'important');
            popup.style.setProperty('pointer-events', 'auto', 'important');
            popup.style.setProperty('transform', 'translateY(100%)', 'important');
            popup.style.setProperty('opacity', '0', 'important');
            
            // Animate from bottom (100%) to top (0%) - Ultra smooth 500ms
            animateSlide(popup, 100, 0, 0, 1, 500, () => {
                // Animation completed
            });
            
            // Only handle chat functionality if user is logged in
            if (window.isLoggedIn) {
                // console.log('Opening popup for logged in user. isAdmin:', isAdmin, 'idchat:', idchat);
                if (isAdmin) {
                    if (inboxView) {
                        // console.log('Admin opening inbox view');
                        showInboxView();
                        // Force load chat users when opening popup
                        if (!isLoadingChatUsers) {
                            setTimeout(() => {
                                loadChatUsers();
                            }, 200);
                        }
                    } else {
                        // console.log('Admin opening chat view');
                        showChatView();
                        loadMessages();
                    }
                } else {
                    // console.log('User opening chat - marking as read and loading messages');
                    markMessagesAsRead();
                    loadMessages();
                }
                
                if (unreadBadge) {
                    unreadBadge.style.display = 'none';
                }
                if (toggleUnreadBadge) {
                    toggleUnreadBadge.style.display = 'none';
                }
            }
            // For non-logged-in users, just show the login required panel
        }
        
        function closePopup() {
            if (!popup || isAnimating) return;
            
            isPopupOpen = false;
            
            // Animate from top (0%) to bottom (100%) - Ultra smooth 500ms
            animateSlide(popup, 0, 100, 1, 0, 500, () => {
                popup.style.setProperty('display', 'none', 'important');
                popup.style.setProperty('visibility', 'hidden', 'important');
                popup.style.setProperty('pointer-events', 'none', 'important');
                
                // Show toggle button again with smooth fade after popup is closed
                const toggleButton = document.getElementById('livechat-toggle');
                if (toggleButton) {
                    toggleButton.style.setProperty('display', 'flex', 'important');
                    toggleButton.style.setProperty('opacity', '0', 'important');
                    toggleButton.style.setProperty('transform', 'scale(0.8)', 'important');
                    
                    // Animate toggle button back in
                    setTimeout(() => {
                        toggleButton.style.setProperty('opacity', '1', 'important');
                        toggleButton.style.setProperty('transform', 'scale(1)', 'important');
                    }, 50);
                }
            });
            
            // Restore body scroll when popup is closed
            document.body.style.overflow = '';
        }
        
        // Auto scroll functions
        function toggleAutoScroll() {
            autoScrollEnabled = !autoScrollEnabled;
            updateAutoScrollButton();
            
            if (autoScrollEnabled) {
                scrollToBottom();
            }
        }
        
        function updateAutoScrollButton() {
            const autoScrollToggle = document.getElementById('auto-scroll-toggle');
            if (!autoScrollToggle) return;
            
            const icon = autoScrollToggle.querySelector('i');
            if (autoScrollEnabled) {
                autoScrollToggle.classList.remove('disabled');
                autoScrollToggle.title = 'Auto Scroll: ON (Klik untuk OFF)';
                if (icon) {
                    icon.className = 'bi bi-arrow-down-circle-fill';
                }
            } else {
                autoScrollToggle.classList.add('disabled');
                autoScrollToggle.title = 'Auto Scroll: OFF (Klik untuk ON)';
                if (icon) {
                    icon.className = 'bi bi-arrow-down-circle';
                }
            }
        }
        
        function scrollToBottom() {
            if (!chatMessages || !autoScrollEnabled) return;
            
            setTimeout(() => {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 100);
        }
        
        function checkIfUserScrolledUp() {
            if (!chatMessages) return;
            
            const scrollTop = chatMessages.scrollTop;
            const scrollHeight = chatMessages.scrollHeight;
            const clientHeight = chatMessages.clientHeight;
            
            // If user scrolled up more than 50px from bottom, disable auto scroll
            if (scrollHeight - scrollTop - clientHeight > 50) {
                if (autoScrollEnabled) {
                    autoScrollEnabled = false;
                    updateAutoScrollButton();
                    
                    // Add pulse animation to indicate auto scroll is disabled
                    const autoScrollToggle = document.getElementById('auto-scroll-toggle');
                    if (autoScrollToggle) {
                        autoScrollToggle.classList.add('pulse');
                        setTimeout(() => {
                            autoScrollToggle.classList.remove('pulse');
                        }, 3000);
                    }
                }
            }
        }
        
        // Send message
        function sendMessage() {
            const message = messageInput.value.trim();
            if (message === '') return;
            
            // console.log('Sending message:', message, 'to idchat:', idchat);
            
            // Stop notifications when user sends a message
            stopNotificationLoop();
            
            // Disable send button temporarily
            if (sendBtn) {
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<i class="bi bi-clock"></i>';
            }
            
            // Use JSON for API request
            const messageData = {
                message: message,
                idchat: idchat
            };
            
            fetch(apiPath + 'send_message.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            })
            .then(response => {
                // console.log('Send message response status:', response.status);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                // console.log('Send message response:', data);
                
                // Re-enable send button
                if (sendBtn) {
                    sendBtn.disabled = false;
                    sendBtn.innerHTML = '<i class="bi bi-send"></i>';
                }
                
                if (data.success) {
                    messageInput.value = '';
                    // Enable auto scroll when sending a message
                    autoScrollEnabled = true;
                    updateAutoScrollButton();
                    
                    // Load messages after a short delay to show the sent message
                    setTimeout(() => {
                        loadMessages();
                    }, 300);
                } else {
                    console.error('Send message error:', data.error);
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
                
                // Re-enable send button
                if (sendBtn) {
                    sendBtn.disabled = false;
                    sendBtn.innerHTML = '<i class="bi bi-send"></i>';
                }
                
                alert('Gagal mengirim pesan: ' + error.message);
            });
        }
        
        if (sendBtn) {
            sendBtn.addEventListener('click', sendMessage);
        }
        if (messageInput) {
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // Stop notifications when user focuses on input
            messageInput.addEventListener('focus', function() {
                stopNotificationLoop();
            });
        }
        
        // Load messages with debouncing to prevent rapid reloads
        let isLoadingMessages = false;
        
        function loadMessages() {
            if (!idchat || idchat === 0) {
                // console.log('No valid idchat, skipping loadMessages. Current idchat:', idchat);
                return;
            }
            
            // Prevent multiple simultaneous requests
            if (isLoadingMessages) {
                return;
            }
            
            isLoadingMessages = true;
            
            // console.log('Loading messages for idchat:', idchat, 'API path:', apiPath, 'currentEmail:', currentEmail);
            
            // Store current scroll position to maintain it
            const scrollTop = chatMessages ? chatMessages.scrollTop : 0;
            const scrollHeight = chatMessages ? chatMessages.scrollHeight : 0;
            const wasAtBottom = chatMessages ? (scrollHeight - scrollTop - chatMessages.clientHeight < 50) : true;
            
            fetch(apiPath + 'get_messages.php?idchat=' + idchat + '&_=' + Date.now())
                .then(response => {
                    // console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.text().then(text => {
                        // console.log('Raw response:', text);
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('JSON parse error:', e);
                            console.error('Response text:', text);
                            throw new Error('Invalid JSON response');
                        }
                    });
                })
                .then(data => {
                    if (data.success) {
                        displayMessages(data.messages);
                        
                        // Restore scroll position or scroll to bottom if user was at bottom
                        if (chatMessages) {
                            if (wasAtBottom || autoScrollEnabled) {
                                setTimeout(() => {
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                }, 50);
                            }
                        }
                    } else {
                        console.error('API Error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                })
                .finally(() => {
                    isLoadingMessages = false;
                });
        }
        
        // Add debouncing to prevent rapid message reloads
        let messageLoadTimeout = null;
        
        function displayMessages(messages) {
            // console.log('displayMessages called with:', messages);
            // console.log('Messages count:', messages ? messages.length : 0);
            // console.log('Current idchat:', idchat, 'Current email:', currentEmail);
            
            if (!chatMessages) {
                console.error('chatMessages element not found');
                return;
            }
            
            // Add updating class to prevent animations during update
            chatMessages.classList.add('updating');
            
            // Store current scroll position
            const wasAtBottom = chatMessages.scrollHeight - chatMessages.scrollTop - chatMessages.clientHeight < 50;
            
            chatMessages.innerHTML = '';
            
            if (!messages || messages.length === 0) {
                // console.log('No messages to display - showing empty state');
                chatMessages.innerHTML = `
                    <div style="text-align: center; color: #999; padding: 40px 20px;">
                        <i class="bi bi-chat-dots" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                        <h4>Belum ada pesan</h4>
                        <p>Mulai percakapan dengan mengirim pesan!</p>
                    </div>
                `;
                return;
            }
            
            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + (message.is_admin ? 'admin' : (message.email === currentEmail ? 'sent' : 'received'));
                
                const time = new Date(message.created_at).toLocaleTimeString('id-ID', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const messageText = message.message || '[Pesan kosong]';
                
                messageDiv.innerHTML = `
                    <div>${messageText}</div>
                    <div class="message-time">${time}</div>
                `;
                
                chatMessages.appendChild(messageDiv);
            });
            
            // Remove updating class after a short delay
            setTimeout(() => {
                if (chatMessages) {
                    chatMessages.classList.remove('updating');
                }
            }, 100);
            
            // Auto scroll to bottom if enabled or user was at bottom
            if (autoScrollEnabled || wasAtBottom) {
                setTimeout(() => {
                    scrollToBottom();
                }, 150);
            }
        }
        
        // Check for new messages
        function checkNewMessages() {
            // console.log('Checking new messages...', 'API:', apiPath + 'cekpesan_livechat.php');
            fetch(apiPath + 'cekpesan_livechat.php?v=' + Date.now())
                .then(response => {
                    // console.log('checkNewMessages response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    // console.log('checkNewMessages data:', data);
                    const unreadCount = data.unread || 0;
                    
                    if (unreadCount > 0 && !isPopupOpen) {
                        if (unreadBadge) {
                            unreadBadge.textContent = unreadCount;
                            unreadBadge.style.display = 'inline-block';
                        }
                        if (toggleUnreadBadge) {
                            toggleUnreadBadge.textContent = unreadCount;
                            toggleUnreadBadge.style.display = 'inline-block';
                        }
                        
                        // If there are new unread messages (increased count), trigger notification
                        if (unreadCount > lastUnreadCount && !isPopupOpen) {
                            // Start notification loop
                            startNotificationLoop();
                            
                            // Add shake animation to toggle button
                            if (toggle) {
                                toggle.classList.add('shake');
                                setTimeout(() => {
                                    toggle.classList.remove('shake');
                                }, 600);
                            }
                        }
                        
                        // Stop notification if no unread messages or popup is open
                        if (unreadCount === 0 || isPopupOpen) {
                            stopNotificationLoop();
                        }
                    } else if (unreadCount === 0 || isPopupOpen) {
                        if (unreadBadge) {
                            unreadBadge.style.display = 'none';
                        }
                        if (toggleUnreadBadge) {
                            toggleUnreadBadge.style.display = 'none';
                        }
                    }
                    
                    lastUnreadCount = unreadCount;
                    
                    // Only load messages if popup is open AND there are actually new messages
                    // This prevents constant reloading that causes the jumping effect
                    if (isPopupOpen && unreadCount > lastUnreadCount) {
                        // Use debouncing to prevent rapid successive calls
                        if (messageLoadTimeout) {
                            clearTimeout(messageLoadTimeout);
                        }
                        messageLoadTimeout = setTimeout(() => {
                            if (!isLoadingMessages) {
                                loadMessages();
                            }
                        }, 500); // Increased delay to reduce jumping
                    }
                })
                .catch(error => {
                    console.error('Error checking messages:', error);
                    // Don't show alert for background checks, just log
                });
        }
        
        function markMessagesAsRead() {
            fetch(apiPath + 'mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    idchat: idchat
                })
            });
        }
        
        // Click outside to close popup
        document.addEventListener('click', function(event) {
            if (isPopupOpen && popup && toggle && !popup.contains(event.target) && !toggle.contains(event.target)) {
                closePopup();
            }
        });
        
        // Prevent popup from closing when clicking inside it
        if (popup) {
            popup.addEventListener('click', function(event) {
                event.stopPropagation();
            });
            
            // Prevent scroll events from propagating to body
            popup.addEventListener('wheel', function(e) {
                // Only prevent if the scroll is not within scrollable areas
                const target = e.target;
                const isScrollableArea = target.closest('.chat-messages') || target.closest('.chat-users-list');
                
                if (!isScrollableArea) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }, { passive: false });
        }
        
        // Admin inbox functions
        function loadChatUsers() {
            if (!isAdmin) {
                // console.log('Not admin, skipping loadChatUsers');
                return;
            }
            
            // Prevent multiple simultaneous requests
            if (isLoadingChatUsers) {
                // console.log('Already loading chat users, skipping...');
                return;
            }
            
            isLoadingChatUsers = true;
            // console.log('Loading chat users...', 'API Path:', apiPath + 'get_chat_users.php');
            
            // Show loading state
            const chatUsersList = document.getElementById('chat-users-list');
            if (chatUsersList) {
                chatUsersList.innerHTML = `
                    <div class="no-chats">
                        <i class="bi bi-clock"></i>
                        <h5>Loading...</h5>
                        <p>Memuat daftar chat...</p>
                    </div>
                `;
            }
            
            fetch(apiPath + 'get_chat_users.php')
                .then(response => {
                    // console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.text().then(text => {
                        // console.log('Raw response:', text);
                        try {
                            const parsed = JSON.parse(text);
                            // console.log('Parsed response:', parsed);
                            return parsed;
                        } catch (e) {
                            console.error('JSON parse error:', e);
                            console.error('Response text:', text);
                            throw new Error('Invalid JSON response');
                        }
                    });
                })
                .then(data => {
                    // console.log('Chat users data received:', data);
                    if (data.success) {
                        displayChatUsers(data.chat_users);
                        // console.log('Chat users displayed successfully');
                    } else {
                        console.error('Error loading chat users:', data.error);
                        // Show error in UI
                        const chatUsersList = document.getElementById('chat-users-list');
                        if (chatUsersList) {
                            chatUsersList.innerHTML = `
                                <div class="no-chats">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    <h5>Error Loading Chats</h5>
                                    <p>${data.error}</p>
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading chat users:', error);
                    // Show error in UI
                    const chatUsersList = document.getElementById('chat-users-list');
                    if (chatUsersList) {
                        chatUsersList.innerHTML = `
                            <div class="no-chats">
                                <i class="bi bi-exclamation-triangle"></i>
                                <h5>Connection Error</h5>
                                <p>Failed to load chat users: ${error.message}</p>
                            </div>
                        `;
                    }
                })
                .finally(() => {
                    // Always reset loading flag
                    isLoadingChatUsers = false;
                });
        }
        
        function displayChatUsers(chatUsers) {
            // console.log('displayChatUsers called with:', chatUsers);
            const chatUsersList = document.getElementById('chat-users-list');
            if (!chatUsersList) {
                console.error('chat-users-list element not found');
                return;
            }
            
            if (!chatUsers || chatUsers.length === 0) {
                // console.log('No chat users to display');
                chatUsersList.innerHTML = `
                    <div class="no-chats">
                        <i class="bi bi-chat-dots"></i>
                        <h5>Belum Ada Chat</h5>
                        <p>Belum ada user yang mengirim pesan</p>
                    </div>
                `;
                
                // Hide badges if no chats
                updateAdminBadges(0);
                return;
            }
            
            // console.log('Displaying', chatUsers.length, 'chat users');
            
            chatUsersList.innerHTML = '';
            let totalUnreadCount = 0;
            
            chatUsers.forEach(user => {
                const userDiv = document.createElement('div');
                userDiv.className = 'chat-user-item';
                userDiv.dataset.idchat = user.idchat;
                userDiv.dataset.email = user.email;
                
                const timeAgo = getTimeAgo(user.last_message_time);
                
                // Count total unread messages
                if (user.unread_count > 0) {
                    totalUnreadCount += parseInt(user.unread_count);
                }
                
                userDiv.innerHTML = `
                    <div class="user-info">
                        <div>
                            <div class="user-name">${user.username}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                        <div class="message-time">${timeAgo}</div>
                    </div>
                    <div class="last-message">${user.last_message}</div>
                    ${user.unread_count > 0 ? `<div class="unread-count">${user.unread_count}</div>` : ''}
                `;
                
                userDiv.addEventListener('click', function() {
                    selectChatUser(user);
                });
                
                chatUsersList.appendChild(userDiv);
            });
            
            // Update badges and notifications for admin
            updateAdminBadges(totalUnreadCount);
            
            // Play notification sound if there are new messages and popup is closed
            if (totalUnreadCount > lastAdminUnreadCount && !isPopupOpen) {
                // console.log('New messages detected for admin:', totalUnreadCount, 'vs', lastAdminUnreadCount);
                startNotificationLoop();
                
                // Shake toggle button
                if (toggle) {
                    toggle.classList.add('shake');
                    setTimeout(() => {
                        toggle.classList.remove('shake');
                    }, 600);
                }
            }
            
            lastAdminUnreadCount = totalUnreadCount;
        }
        
        function updateAdminBadges(unreadCount) {
            // console.log('Updating admin badges with count:', unreadCount);
            
            if (unreadCount > 0) {
                // Show badge on toggle button
                if (toggleUnreadBadge) {
                    toggleUnreadBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                    toggleUnreadBadge.style.display = 'inline-block';
                }
                
                // Show badge on popup header
                if (unreadBadge) {
                    unreadBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                    unreadBadge.style.display = 'inline-block';
                }
            } else {
                // Hide badges
                if (toggleUnreadBadge) {
                    toggleUnreadBadge.style.display = 'none';
                }
                if (unreadBadge) {
                    unreadBadge.style.display = 'none';
                }
                
                // Stop notifications
                stopNotificationLoop();
            }
        }
        
        function selectChatUser(user) {
            currentChatUser = user;
            idchat = user.idchat;
            inboxView = false;
            
            // Update header
            const headerTitle = document.querySelector('.header-title');
            const headerSubtitle = document.querySelector('.header-subtitle');
            if (headerTitle) headerTitle.textContent = user.username;
            if (headerSubtitle) headerSubtitle.textContent = user.email;
            
            showChatView();
            loadMessages();
            markMessagesAsRead();
        }
        
        function showInboxView() {
            const adminInbox = document.getElementById('admin-inbox');
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.querySelector('.chat-input');
            const backBtn = document.getElementById('back-to-inbox');
            const headerTitle = document.querySelector('.header-title');
            const headerSubtitle = document.querySelector('.header-subtitle');
            
            // Use classes instead of inline styles for better CSS control
            if (adminInbox) {
                adminInbox.classList.remove('hidden');
                adminInbox.classList.add('visible');
                adminInbox.style.display = 'flex';
            }
            if (chatMessages) {
                chatMessages.classList.add('hidden');
                chatMessages.classList.remove('visible');
                chatMessages.style.display = 'none';
            }
            if (chatInput) {
                chatInput.classList.add('hidden');
                chatInput.classList.remove('visible');
                chatInput.style.display = 'none';
            }
            if (backBtn) backBtn.style.display = 'none';
            
            if (headerTitle) headerTitle.textContent = 'Admin Inbox';
            if (headerSubtitle) headerSubtitle.textContent = 'Kelola Chat Users';
            
            inboxView = true;
            currentChatUser = null;
        }
        
        function showChatView() {
            const adminInbox = document.getElementById('admin-inbox');
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.querySelector('.chat-input');
            const backBtn = document.getElementById('back-to-inbox');
            
            // Use classes instead of inline styles for better CSS control
            if (adminInbox) {
                adminInbox.classList.add('hidden');
                adminInbox.classList.remove('visible');
                adminInbox.style.display = 'none';
            }
            if (chatMessages) {
                chatMessages.classList.remove('hidden');
                chatMessages.classList.add('visible');
                chatMessages.style.display = 'block';
            }
            if (chatInput) {
                chatInput.classList.remove('hidden');
                chatInput.classList.add('visible');
                chatInput.style.display = 'flex';
            }
            if (backBtn) backBtn.style.display = 'flex';
            
            inboxView = false;
        }
        
        function getTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) return 'Baru saja';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' menit lalu';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' jam lalu';
            if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' hari lalu';
            
            return date.toLocaleDateString('id-ID');
        }
        
        // Start checking for new messages only if user is logged in
        if (window.isLoggedIn) {
            if (isAdmin) {
                // Load chat users immediately for admin
                loadChatUsers();
                
                // Single interval for admin - handles both inbox and chat views
                setInterval(() => {
                    if (inboxView && !isLoadingChatUsers) {
                        loadChatUsers();
                    } else if (!inboxView && idchat) {
                        // Only check for new messages in chat view, don't auto-reload
                        checkNewMessages();
                    }
                }, 8000); // Further reduced frequency to prevent jumping
            } else {
                // Single interval for regular users
                setInterval(() => {
                    if (!isPopupOpen) {
                        checkNewMessages(); // Only check when popup is closed
                    }
                }, 8000); // Further increased interval to reduce jumping
                
                // Initial check
                checkNewMessages();
            }
        }
    } // End of initializeLivechat function
});
</script>