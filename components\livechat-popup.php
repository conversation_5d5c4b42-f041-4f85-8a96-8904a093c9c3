<?php
// Don't start session if already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$is_logged_in = isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true;
$is_admin = false;
$email = '';
$idchat = '';

if ($is_logged_in) {
    // Use correct path for database include
    $database_path = __DIR__ . '/../database/database.php';
    if (file_exists($database_path)) {
        include $database_path;
    } else {
        // Fallback path
        include '../database/database.php';
    }

    $email = $_SESSION['email'];

    // Check if database connection exists
    if (isset($conn) && $conn) {
        // Check if user is admin
        try {
            $stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
            if ($stmt) {
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $result = $stmt->get_result();
                $user = $result->fetch_assoc();
                $is_admin = ($user && $user['group'] === 'ADMIN');
            }
        } catch (Exception $e) {
            $is_admin = false; // Default to non-admin if error
        }

        // Get or create idchat
        try {
            if ($is_admin && isset($_GET['idchat']) && intval($_GET['idchat']) > 0) {
                $idchat = intval($_GET['idchat']);
            } else {
                $stmt = $conn->prepare("SELECT idchat FROM chats WHERE email = ? LIMIT 1");
                if ($stmt) {
                    $stmt->bind_param("s", $email);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $chat = $result->fetch_assoc();
                    
                    if ($chat) {
                        $idchat = $chat['idchat'];
                    } else {
                        $idchat = rand(100000, 999999);
                    }
                } else {
                    $idchat = rand(100000, 999999);
                }
            }
        } catch (Exception $e) {
            $idchat = rand(100000, 999999); // Fallback idchat
        }
    }
}
?>

<!-- Livechat Toggle Button -->
<div id="livechat-toggle" class="livechat-toggle">
    <i class="bi bi-chat-dots"></i>
    <?php if ($is_logged_in): ?>
        <span id="toggle-unread-badge" class="notification-badge" style="display: none;">0</span>
    <?php endif; ?>
</div>

<!-- Livechat Popup -->
<div id="livechat-popup" class="livechat-popup">
    <div class="livechat-header">
        <div class="header-info">
            <div class="header-title">
                <?php if ($is_logged_in): ?>
                    <?php echo $is_admin ? 'Admin Inbox' : 'Live Chat'; ?>
                <?php else: ?>
                    Live Chat
                <?php endif; ?>
            </div>
            <div class="header-subtitle">
                <?php if ($is_logged_in): ?>
                    <?php echo $is_admin ? 'Kelola Chat Users' : 'Chat dengan Admin'; ?>
                <?php else: ?>
                    Bantuan & Dukungan
                <?php endif; ?>
            </div>
        </div>
        <div class="header-actions">
            <?php if ($is_logged_in && $is_admin): ?>
                <button id="back-to-inbox" class="header-btn" style="display: none;" title="Kembali ke Inbox">
                    <i class="bi bi-arrow-left"></i>
                </button>
            <?php endif; ?>
            <button id="minimize-chat" class="header-btn">
                <i class="bi bi-dash"></i>
            </button>
            <button id="close-chat" class="header-btn">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <?php if ($is_logged_in): ?>
            <span id="unread-badge" class="notification-badge" style="display: none;">0</span>
        <?php endif; ?>
    </div>
    
    <?php if (!$is_logged_in): ?>
    <!-- Login Required Panel -->
    <div id="login-required" class="login-required-panel">
        <div class="login-message">
            <div class="login-icon">
                <i class="bi bi-lock"></i>
            </div>
            <h4>Akses Chat Membutuhkan Login</h4>
            <p>Untuk menggunakan fitur live chat dan mendapatkan bantuan dari tim support kami, silakan login terlebih dahulu.</p>
            <div class="login-benefits">
                <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Chat langsung dengan admin</span>
                </div>
                <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Respon cepat 24/7</span>
                </div>
                <!-- <div class="benefit-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Riwayat percakapan tersimpan</span>
                </div> -->
            </div>
                         <div class="login-actions">
                 <a href="<?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) ? '../auth/login' : 'auth/login'; ?>" class="btn-login">
                     <i class="bi bi-box-arrow-in-right"></i>
                     Login Sekarang
                 </a>
                 <a href="<?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) ? '../auth/register' : 'auth/register'; ?>" class="btn-register">
                     <i class="bi bi-person-plus"></i>
                     Daftar Gratis
                 </a><br>
             </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Logged In Chat Interface -->
    <?php if ($is_admin): ?>
    <!-- Admin Inbox Panel -->
    <div id="admin-inbox" class="admin-inbox">
        <div class="inbox-header">
            <h4><i class="bi bi-inbox"></i> Chat Inbox</h4>
            <button id="refresh-inbox" class="refresh-btn" title="Refresh Inbox">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
        <div id="chat-users-list" class="chat-users-list">
            <!-- Chat users will be loaded here -->
        </div>
    </div>
    <?php endif; ?>
    
    <div id="chat-messages" class="chat-messages" <?php echo $is_admin ? 'style="display: none;"' : ''; ?>>
        <!-- Messages will be loaded here -->
    </div>
    
    <div class="chat-input" <?php echo $is_admin ? 'style="display: none;"' : ''; ?>>
        <input type="text" id="message-input" placeholder="💬 Ketik pesan Anda di sini..." maxlength="500" autocomplete="off">
        <button id="send-btn" class="send-btn" title="Kirim pesan">
            <i class="bi bi-send"></i>
        </button>
    </div>
    
    <!-- Auto Scroll Toggle Button -->
    <div id="auto-scroll-toggle" class="auto-scroll-toggle" title="Toggle Auto Scroll">
        <i class="bi bi-arrow-down-circle-fill"></i>
    </div>
    <?php endif; ?>
</div>

<!-- Include CSS and JS files -->
<?php
// Detect current path to determine correct asset path
$current_path = $_SERVER['REQUEST_URI'];
$asset_path = '';

if (strpos($current_path, '/admin/') !== false) {
    $asset_path = '../assets/';
} else if (strpos($current_path, '/pages/') !== false) {
    $asset_path = '../assets/';
} else {
    $asset_path = 'assets/';
}
?>

<link rel="stylesheet" href="<?php echo $asset_path; ?>css/livechat.css">
<script>
// Pass PHP variables to JavaScript
window.livechatConfig = {
    isLoggedIn: <?php echo $is_logged_in ? 'true' : 'false'; ?>,
    idchat: <?php echo $idchat ? $idchat : 0; ?>,
    currentEmail: '<?php echo $email; ?>',
    isAdmin: <?php echo $is_admin ? 'true' : 'false'; ?>,
    assetPath: '<?php echo $asset_path; ?>'
};
</script>
<script src="<?php echo $asset_path; ?>js/livechat.js"></script>