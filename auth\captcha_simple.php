<?php
session_start();

// Generate a random arithmetic expression for CAPTCHA
$num1 = rand(1, 10);
$num2 = rand(1, 10);
$operators = ['+', '-'];
$operator = $operators[rand(0, 1)];

// Calculate the correct answer
switch ($operator) {
    case '+':
        $answer = $num1 + $num2;
        break;
    case '-':
        // Ensure positive result
        if ($num1 < $num2) {
            $temp = $num1;
            $num1 = $num2;
            $num2 = $temp;
        }
        $answer = $num1 - $num2;
        break;
}

// Store the CAPTCHA answer in session
$_SESSION['captcha'] = $answer;

// Generate SVG captcha
$svg_captcha = '
<svg width="120" height="40" xmlns="http://www.w3.org/2000/svg">
  <rect width="120" height="40" fill="#212529" stroke="#454545" stroke-width="2"/>
  <text x="60" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
        fill="#ffffff" text-anchor="middle" dominant-baseline="middle">
    ' . $num1 . ' ' . $operator . ' ' . $num2 . ' = ?
  </text>
  <!-- Add some noise lines for security -->
  <line x1="10" y1="5" x2="25" y2="15" stroke="#666" stroke-width="1"/>
  <line x1="95" y1="10" x2="110" y2="20" stroke="#666" stroke-width="1"/>
  <line x1="30" y1="35" x2="45" y2="25" stroke="#666" stroke-width="1"/>
  <line x1="75" y1="30" x2="90" y2="35" stroke="#666" stroke-width="1"/>
</svg>';

// Set content type to SVG
header('Content-Type: image/svg+xml');
echo $svg_captcha;
?> 