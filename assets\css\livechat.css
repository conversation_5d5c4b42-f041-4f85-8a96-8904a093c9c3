/* Livechat Popup Styles */

/* Toggle Button */
.livechat-toggle {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    z-index: 9998;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    color: white;
    font-size: 24px;
}

.livechat-toggle:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.livechat-toggle:active {
    transform: translateY(-1px) scale(1.02);
}

/* Toggle Button Animations */
.livechat-toggle.bounce {
    animation: bounceToggle 0.4s ease-out;
}

.livechat-toggle.shake {
    animation: shakeToggle 0.6s ease-in-out;
}

@keyframes bounceToggle {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes shakeToggle {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* Unread Badge */
.unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Unread Badge on Toggle */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

/* Popup Container */
.livechat-popup {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    display: none;
    flex-direction: column;
    overflow: hidden;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#livechat-popup.show {
    display: flex;
    transform: translateY(0);
    opacity: 1;
}

/* Header */
.livechat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
    position: relative;
}

.header-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.header-subtitle {
    font-size: 13px;
    opacity: 0.9;
    margin: 2px 0 0 0;
    line-height: 1.2;
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Header notification badge */
.livechat-header .notification-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

.header-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.header-btn:active {
    transform: scale(0.95);
}

/* Auto Scroll Toggle */
.auto-scroll-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.auto-scroll-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
}

.auto-scroll-toggle.disabled {
    opacity: 0.5;
    background: rgba(255, 255, 255, 0.1);
}

.auto-scroll-toggle.pulse {
    animation: pulseButton 3s ease-in-out;
}

@keyframes pulseButton {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Login Required Panel */
.login-required-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 30px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.login-message {
    width: 100%;
    max-width: 320px;
}

.login-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    color: white;
    font-size: 36px;
}

.login-required-panel h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 22px;
    font-weight: 600;
}

.login-required-panel p {
    color: #7f8c8d;
    margin-bottom: 20px;
    line-height: 1.6;
    font-size: 15px;
}

.login-benefits {
    margin: 20px 0;
    text-align: left;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 14px;
}

.benefit-item i {
    color: #27ae60;
    font-size: 16px;
}

.login-actions {
    display: flex;
    gap: 15px;
    width: 100%;
    max-width: 280px;
    margin-top: 10px;
}

.btn-login, .btn-register {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-register {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-register:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

/* Admin Inbox */
.admin-inbox {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.inbox-header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.inbox-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.refresh-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.chat-users-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.chat-user-item {
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    position: relative;
}

.chat-user-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.user-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.user-email {
    color: #7f8c8d;
    font-size: 12px;
    margin-top: 2px;
}

.message-time {
    color: #95a5a6;
    font-size: 11px;
    white-space: nowrap;
}

.last-message {
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.unread-count {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

.no-chats {
    text-align: center;
    padding: 60px 20px;
    color: #95a5a6;
}

.no-chats i {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.no-chats h5 {
    margin-bottom: 10px;
    color: #7f8c8d;
}

.no-chats p {
    font-size: 14px;
    line-height: 1.5;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 15px;
    scroll-behavior: smooth;
}

.chat-messages.updating {
    pointer-events: none;
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.sent {
    align-self: flex-end;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.message.received {
    align-self: flex-start;
    background: white;
    color: #2c3e50;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
}

.message.admin {
    align-self: flex-start;
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    border-bottom-left-radius: 6px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 5px;
    text-align: right;
}

.message.received .message-time,
.message.admin .message-time {
    text-align: left;
}

/* Chat Input */
.chat-input {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    align-items: flex-end;
    border-radius: 0 0 20px 20px;
}

.message-input {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 12px 18px;
    font-size: 14px;
    resize: none;
    outline: none;
    transition: all 0.3s ease;
    font-family: inherit;
    line-height: 1.4;
    max-height: 100px;
    min-height: 44px;
}

.message-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input::placeholder {
    color: #95a5a6;
}

.send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.send-btn:active:not(:disabled) {
    transform: scale(0.95);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Back Button */
.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 480px) {
    #livechat-popup {
        width: calc(100vw - 20px);
        height: calc(100vh - 40px);
        bottom: 10px;
        right: 10px;
        border-radius: 15px;
    }

    .livechat-toggle {
        bottom: 20px;
        right: 20px;
        width: 55px;
        height: 55px;
        font-size: 22px;
    }

    .livechat-header {
        padding: 15px;
        border-radius: 15px 15px 0 0;
    }

    .header-title {
        font-size: 16px;
    }

    .header-subtitle {
        font-size: 12px;
    }

    .chat-messages {
        padding: 15px;
    }

    .chat-input {
        padding: 15px;
        border-radius: 0 0 15px 15px;
    }

    .message {
        max-width: 85%;
        padding: 10px 14px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar,
.chat-users-list::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.chat-users-list::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb,
.chat-users-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.chat-users-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}
