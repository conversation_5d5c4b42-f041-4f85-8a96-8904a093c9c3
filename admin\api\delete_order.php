<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing order_id parameter']);
    exit;
}

$order_id = intval($input['order_id']);

if ($order_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid order_id']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();
    
    // Check if order exists and get order details
    $check_stmt = $conn->prepare("SELECT id, email, nama_server, bukti_pembayaran FROM `order` WHERE id = ?");
    if (!$check_stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $check_stmt->bind_param("i", $order_id);
    $check_stmt->execute();
    $order_result = $check_stmt->get_result();
    $order = $order_result->fetch_assoc();
    
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    // Delete payment proof file if exists
    if ($order['bukti_pembayaran']) {
        $file_path = __DIR__ . '/../../uploads/payment_proofs/' . basename($order['bukti_pembayaran']);
        if (file_exists($file_path)) {
            if (!unlink($file_path)) {
                error_log("Warning: Failed to delete payment proof file: {$file_path}");
                // Don't throw exception, continue with database deletion
            }
        }
    }
    
    // Delete related QRIS log entries if any
    $delete_qris_stmt = $conn->prepare("DELETE FROM log_qris WHERE order_id = ?");
    if ($delete_qris_stmt) {
        $delete_qris_stmt->bind_param("i", $order_id);
        $delete_qris_stmt->execute();
    }
    
    // Delete the order
    $delete_stmt = $conn->prepare("DELETE FROM `order` WHERE id = ?");
    if (!$delete_stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $delete_stmt->bind_param("i", $order_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Failed to delete order: ' . $delete_stmt->error);
    }
    
    if ($delete_stmt->affected_rows === 0) {
        throw new Exception('Order not found or already deleted');
    }
    
    // Log the deletion action
    error_log("Order #{$order_id} deleted by admin {$email} (customer: {$order['email']}, server: {$order['nama_server']})");
    
    // Commit transaction
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Order deleted successfully',
        'order_id' => $order_id
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
} catch (Exception $e) {
    error_log('delete_order.php error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// Clean output buffer
ob_end_flush();
?>