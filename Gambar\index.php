<?php
// Mengatur error reporting untuk debugging jika diperlukan
error_reporting(E_ALL);
ini_set('display_errors', 1);

// <PERSON><PERSON><PERSON>an pesan bahwa akses ke folder ini tidak diperbolehkan
header("HTTP/1.0 403 Forbidden");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A<PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        .container {
            display: inline-block;
            padding: 20px;
            border: 1px solid #ddd;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }
        h1 {
            color: #d9534f;
        }
        p {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><PERSON><PERSON><PERSON></h1>
        <p>Anda tidak memiliki izin untuk mengakses folder ini secara langsung.</p>
        <p><PERSON><PERSON>an kembali ke <a href="/">halaman utama</a>.</p>
    </div>
</body>
</html>
