<?php
// Include config for dynamic paths
require_once __DIR__ . '/../includes/config.php';

// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Buat token CSRF
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Check if user is already logged in
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true) {
            header('Location: ../pages/dashboard');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">

  <title>Dopminer.com - Register</title>
  <meta content="" name="description">
      <meta content="" name="keywords">

    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <link href="../assets/img/Dopminer.png" rel="apple-touch-icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
  <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
  <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Template Main CSS File -->
  <link href="../assets/css/style.css" rel="stylesheet">

  <!-- Dopminer Custom Styles -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: "Open Sans", sans-serif;
      background: #0a0e27;
      color: #ffffff;
      min-height: 100vh;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: "Jost", sans-serif;
    }
    
    .register-section {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 120px 20px 20px 20px;
      background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
    }
    
    .register-container {
      width: 100%;
      max-width: 450px;
    }
    
    .register-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      padding: 2.5rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }
    
    .register-card:hover {
      border-color: #4fc3f7;
      box-shadow: 0 25px 50px rgba(79, 195, 247, 0.1);
    }
    
    .auth-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .auth-title {
      color: #4fc3f7;
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
      color: #b0b0b0;
      font-size: 1rem;
    }
    
    .auth-subtitle a {
      color: #4fc3f7;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }
    
    .auth-subtitle a:hover {
      color: #29b6f6;
    }
    
    .form-group {
      margin-bottom: 1.5rem;
    }
    
    .form-label {
      color: #b0b0b0;
      font-weight: 500;
      margin-bottom: 0.5rem;
      display: block;
    }
    
    .form-control {
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 12px;
      padding: 0.875rem 1rem;
      color: #ffffff;
      font-size: 0.95rem;
      transition: all 0.3s ease;
    }
    
    .form-control:focus {
      background: rgba(255, 255, 255, 0.12);
      border-color: #4fc3f7;
      box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.15);
      color: #ffffff;
    }
    
    .form-control::placeholder {
      color: #666;
    }
    
    .input-group-text {
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-left: none;
      border-radius: 0 12px 12px 0;
      color: #b0b0b0;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .input-group-text:hover {
      background: rgba(255, 255, 255, 0.12);
      color: #4fc3f7;
    }
    
    .input-group .form-control {
      border-radius: 12px 0 0 12px;
    }
    
    .captcha-info {
      color: #888;
      font-size: 0.8rem;
      margin-top: 0.5rem;
    }
    
    .btn-auth {
      background: linear-gradient(135deg, #4fc3f7, #29b6f6);
      border: none;
      border-radius: 12px;
      padding: 0.875rem 2rem;
      color: #ffffff;
      font-weight: 600;
      font-size: 1rem;
      width: 100%;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }
    
    .btn-auth:hover {
      background: linear-gradient(135deg, #29b6f6, #0288d1);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(79, 195, 247, 0.3);
    }
    
    .alert {
      border: none;
      border-radius: 12px;
      padding: 1rem;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
    
    .alert-danger {
      background: rgba(244, 67, 54, 0.1);
      color: #f44336;
      border: 1px solid rgba(244, 67, 54, 0.2);
    }
    
    .alert-success {
      background: rgba(76, 175, 80, 0.1);
      color: #4caf50;
      border: 1px solid rgba(76, 175, 80, 0.2);
    }
    
    #captchaImage {
      border-radius: 8px;
      cursor: pointer;
      transition: opacity 0.3s ease;
    }
    
    #captchaImage:hover {
      opacity: 0.8;
    }
    
    @media (max-width: 768px) {
      .register-section {
        padding: 100px 15px 20px 15px;
      }
      
      .register-card {
        padding: 2rem;
      }
      
      .auth-title {
        font-size: 1.75rem;
      }
    }
  </style>
</head>

<body>

  <?php include '../components/navbar.php'; ?>

  <!-- ======= Register Section ======= -->
  <section class="register-section">
    <div class="register-container" data-aos="fade-up">
      <div class="register-card">
        
        <!-- Header -->
        <div class="auth-header">
          <h1 class="auth-title">✨ Daftar Akun</h1>
          <p class="auth-subtitle">Sudah punya akun? <a href="login">Masuk sekarang</a></p>
        </div>

        <!-- Alerts -->
        <div id="error-message" class="alert alert-danger d-none"></div>
        <div id="success-message" class="alert alert-success d-none"></div>

        <!-- Form -->
        <form id="registerForm" method="post" action="register" onsubmit="return validateForm()">
          <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
          
          <div class="form-group">
            <label class="form-label" for="email">📧 Alamat Email</label>
            <input type="email" name="email" class="form-control" id="email" placeholder="Masukkan alamat email Anda" required>
          </div>

          <div class="form-group">
            <label class="form-label" for="password">🔑 Kata Sandi</label>
            <div class="input-group">
              <input type="password" name="password" class="form-control" id="password" placeholder="Buat kata sandi yang kuat" required>
              <span class="input-group-text" onclick="togglePassword('password', 'togglePasswordIcon')">
                <i class="bi bi-eye" id="togglePasswordIcon"></i>
              </span>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="confirm_password">🔐 Konfirmasi Kata Sandi</label>
            <div class="input-group">
              <input type="password" name="confirm_password" class="form-control" id="confirm_password" placeholder="Ulangi kata sandi Anda" required>
              <span class="input-group-text" onclick="togglePassword('confirm_password', 'toggleConfirmPasswordIcon')">
                <i class="bi bi-eye" id="toggleConfirmPasswordIcon"></i>
              </span>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="captcha">🔢 CAPTCHA</label>
            <div class="input-group">
              <input type="number" name="captcha" class="form-control" id="captcha" placeholder="Masukkan jawaban" required>
              <span class="input-group-text">
                <img src="captcha.php?<?php echo time(); ?>" alt="CAPTCHA" id="captchaImage">
              </span>
            </div>
            <small class="captcha-info">Klik gambar untuk refresh CAPTCHA</small>
          </div>

          <button type="submit" class="btn-auth">Buat Akun Sekarang</button>
        </form>
        
      </div>
    </div>
  </section>
                <script>
                  function togglePassword(fieldId, iconId) {
                    var passwordField = document.getElementById(fieldId);
                    var toggleIcon = document.getElementById(iconId);
                    if (passwordField.type === "password") {
                      passwordField.type = "text";
                      toggleIcon.classList.remove("bi-eye");
                      toggleIcon.classList.add("bi-eye-slash");
                    } else {
                      passwordField.type = "password";
                      toggleIcon.classList.remove("bi-eye-slash");
                      toggleIcon.classList.add("bi-eye");
                    }
                  }

                  // Function to refresh captcha
                  function refreshCaptcha() {
                    document.getElementById('captchaImage').src = 'captcha.php?' + new Date().getTime();
                  }

                  // Add click event to captcha image
                  document.addEventListener('DOMContentLoaded', function() {
                    document.getElementById('captchaImage').addEventListener('click', refreshCaptcha);
                  });

                  function validateForm() {
                    var form = document.getElementById('registerForm');
                    var formData = new FormData(form);
                    var password = document.getElementById('password').value;
                    var confirmPassword = document.getElementById('confirm_password').value;

                    if (password !== confirmPassword) {
                      document.getElementById('error-message').classList.remove('d-none');
                      document.getElementById('error-message').innerText = 'Kata sandi tidak cocok!';
                      return false;
                    }

                    if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@#!$]{6,}$/.test(password)) {
                      document.getElementById('error-message').classList.remove('d-none');
                      document.getElementById('error-message').innerText = 'Kata sandi harus minimal 6 karakter dan kombinasi huruf dan nomor! Contoh: password1 atau kata2sandi';
                      return false;
                    }

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', 'register_handler.php', true);
                    xhr.onload = function () {
                      if (xhr.status === 200) {
                        var response = JSON.parse(xhr.responseText);
                        if (response.error) {
                          document.getElementById('success-message').classList.add('d-none');
                          document.getElementById('error-message').classList.remove('d-none');
                          document.getElementById('error-message').innerText = response.error;
                          if (response.new_captcha) {
                            document.getElementById('captchaImage').src = response.new_captcha;
                          } else {
                            refreshCaptcha(); // Refresh captcha on error
                          }
                        } else if (response.success) {
                          document.getElementById('error-message').classList.add('d-none');
                          document.getElementById('success-message').classList.remove('d-none');
                          document.getElementById('success-message').innerHTML = response.message + '<br>Dialihkan Dalam waktu <span id="countdown">10</span> detik...';
                          document.getElementById('registerForm').reset();
                          var countdownElement = document.getElementById('countdown');
                          var countdown = 10;
                          var countdownInterval = setInterval(function() {
                            countdown--;
                            countdownElement.innerText = countdown;
                            if (countdown <= 0) {
                              clearInterval(countdownInterval);
                              window.location.href = 'login';
                            }
                          }, 1000);
                        }
                      }
                    };
                    xhr.send(formData);
                    return false; // Prevent form from submitting the default way
                  }
                </script>
        
      </div>
    </div>
  </section>


  <div style="position: relative; bottom: 0; width: 100%;">
    <?php include '../components/footer.php'; ?>
  </div>

  

  <!-- Vendor JS Files -->
  <script src="../assets/vendor/aos/aos.js"></script>
  <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
  <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="../assets/vendor/php-email-form/validate.js"></script>

  <!-- Template Main JS File -->
  <script src="../assets/js/main.js"></script>

</body>

</html>