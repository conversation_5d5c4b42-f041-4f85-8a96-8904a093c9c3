-- Create order table
CREATE TABLE IF NOT EXISTS `order` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `id_order` varchar(50) NOT NULL,
    `id_produk` int(11) NOT NULL,
    `email` varchar(255) NOT NULL,
    `nama_server` varchar(255) NOT NULL,
    `slot` varchar(50) DEFAULT NULL,
    `status_bayar` enum('Menunggu Pembayaran','Menunggu Verifikasi','Sudah Dibayar') NOT NULL DEFAULT 'Menunggu Pembayaran',
    `pembayaran` enum('QRIS','MANUAL PAYMENT') NOT NULL,
    `bukti_pembayaran` varchar(500) DEFAULT NULL,
    `jumlah_bayar` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `id_order` (`id_order`),
    UNIQUE KEY `nama_server` (`nama_server`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status_bayar`),
    KEY `idx_produk` (`id_produk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add id_order column to existing table if it doesn't exist
ALTER TABLE `order` 
ADD COLUMN IF NOT EXISTS `id_order` varchar(50) NOT NULL AFTER `id`,
ADD UNIQUE KEY IF NOT EXISTS `idx_id_order` (`id_order`);

-- Create uploads directory structure (this is just documentation)
-- You need to create these directories manually:
-- uploads/
-- uploads/payment_proofs/

-- Update log_qris table to include order_id if not exists
ALTER TABLE `log_qris` 
ADD COLUMN IF NOT EXISTS `order_id` int(11) DEFAULT NULL,
ADD KEY IF NOT EXISTS `idx_order_id` (`order_id`);