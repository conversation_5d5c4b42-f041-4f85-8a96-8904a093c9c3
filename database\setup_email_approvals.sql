-- Create email_change_requests table
CREATE TABLE IF NOT EXISTS `email_change_requests` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_email` VARCHAR(255) NOT NULL,
    `old_email` VARCHAR(255) NOT NULL,
    `new_email` VARCHAR(255) NOT NULL,
    `reason` TEXT,
    `request_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `status` ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    `admin_email` VARCHAR(255),
    `admin_action_date` TIMESTAMP NULL,
    `admin_notes` TEXT,
    INDEX `idx_user_email` (`user_email`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create profile_history table
CREATE TABLE IF NOT EXISTS `profile_history` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_email` VARCHAR(255) NOT NULL,
    `change_type` ENUM('email_change', 'password_change') NOT NULL,
    `old_value` VARCHAR(255) NOT NULL,
    `new_value` VARCHAR(255) NOT NULL,
    `change_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `status` ENUM('pending', 'approved', 'rejected', 'success') DEFAULT 'pending',
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `admin_approved_by` VARCHAR(255),
    `admin_ip` VARCHAR(45),
    `admin_user_agent` TEXT,
    INDEX `idx_user_email` (`user_email`),
    INDEX `idx_change_type` (`change_type`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 