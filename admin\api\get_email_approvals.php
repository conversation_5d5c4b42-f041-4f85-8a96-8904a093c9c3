<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['group']) || $_SESSION['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Set headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

// Include database connection
require_once '../../database/database.php';

try {
    // Check database connection
    if (!isset($conn) || !$conn->ping()) {
        throw new Exception("Database connection failed");
    }

    // Handle POST requests for actions (approve/reject/delete)
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['action'], $data['request_id'])) {
            throw new Exception("Missing required parameters");
        }
        
        $action = $data['action'];
        $request_id = (int)$data['request_id'];
        $admin_email = $_SESSION['email'];
        $admin_notes = $data['notes'] ?? '';
        
        if ($action === 'approve') {
            // Get request details
            $get_request = $conn->prepare("SELECT * FROM email_change_requests WHERE id = ? AND status = 'pending'");
            $get_request->bind_param("i", $request_id);
            $get_request->execute();
            $request_result = $get_request->get_result();
            
            if ($request_result->num_rows === 0) {
                throw new Exception("Request not found or already processed");
            }
            
            $request_data = $request_result->fetch_assoc();
            
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Update user's email
                $update_user = $conn->prepare("UPDATE users SET email = ? WHERE email = ?");
                $update_user->bind_param("ss", $request_data['new_email'], $request_data['old_email']);
                $update_user->execute();
                
                // Update request status
                $update_request = $conn->prepare("UPDATE email_change_requests SET status = 'approved', admin_email = ?, admin_action_date = NOW(), admin_notes = ? WHERE id = ?");
                $update_request->bind_param("ssi", $admin_email, $admin_notes, $request_id);
                $update_request->execute();
                
                // Update history
                $admin_ip = $_SERVER['REMOTE_ADDR'];
                $admin_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
                $update_history = $conn->prepare("UPDATE profile_history SET status = 'approved', admin_approved_by = ?, admin_ip = ?, admin_user_agent = ? WHERE user_email = ? AND change_type = 'email_change' AND new_value = ? AND status = 'pending'");
                $update_history->bind_param("sssss", $admin_email, $admin_ip, $admin_user_agent, $request_data['old_email'], $request_data['new_email']);
                $update_history->execute();
                
                $conn->commit();
                echo json_encode(['success' => true, 'message' => 'Email change request approved successfully']);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw new Exception("Failed to approve request: " . $e->getMessage());
            }
            
        } elseif ($action === 'reject') {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Update request status
                $update_request = $conn->prepare("UPDATE email_change_requests SET status = 'rejected', admin_email = ?, admin_action_date = NOW(), admin_notes = ? WHERE id = ? AND status = 'pending'");
                $update_request->bind_param("ssi", $admin_email, $admin_notes, $request_id);
                $update_request->execute();
                
                if ($update_request->affected_rows === 0) {
                    throw new Exception("Request not found or already processed");
                }
                
                // Get request details for history update
                $get_request = $conn->prepare("SELECT user_email, new_email FROM email_change_requests WHERE id = ?");
                $get_request->bind_param("i", $request_id);
                $get_request->execute();
                $request_data = $get_request->get_result()->fetch_assoc();
                
                // Update history
                $admin_ip = $_SERVER['REMOTE_ADDR'];
                $admin_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
                $update_history = $conn->prepare("UPDATE profile_history SET status = 'rejected', admin_approved_by = ?, admin_ip = ?, admin_user_agent = ? WHERE user_email = ? AND change_type = 'email_change' AND new_value = ? AND status = 'pending'");
                $update_history->bind_param("sssss", $admin_email, $admin_ip, $admin_user_agent, $request_data['user_email'], $request_data['new_email']);
                $update_history->execute();
                
                $conn->commit();
                echo json_encode(['success' => true, 'message' => 'Email change request rejected successfully']);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw new Exception("Failed to reject request: " . $e->getMessage());
            }
            
        } elseif ($action === 'delete') {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Get request details before deleting
                $get_request = $conn->prepare("SELECT user_email, new_email FROM email_change_requests WHERE id = ?");
                $get_request->bind_param("i", $request_id);
                $get_request->execute();
                $request_result = $get_request->get_result();
                
                if ($request_result->num_rows === 0) {
                    throw new Exception("Request not found");
                }
                
                $request_data = $request_result->fetch_assoc();
                
                // Delete request
                $delete_request = $conn->prepare("DELETE FROM email_change_requests WHERE id = ?");
                $delete_request->bind_param("i", $request_id);
                $delete_request->execute();
                
                // Delete corresponding history entry
                $delete_history = $conn->prepare("DELETE FROM profile_history WHERE user_email = ? AND change_type = 'email_change' AND new_value = ? AND status = 'pending'");
                $delete_history->bind_param("ss", $request_data['user_email'], $request_data['new_email']);
                $delete_history->execute();
                
                $conn->commit();
                echo json_encode(['success' => true, 'message' => 'Email change request deleted successfully']);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw new Exception("Failed to delete request: " . $e->getMessage());
            }
        } else {
            throw new Exception("Invalid action");
        }
        
        exit;
    }

    // GET request - fetch all email change requests with user details
    $requests_query = "
        SELECT ecr.*, 
               u.username,
               COALESCE(ph.ip_address, 'Unknown') as request_ip,
               COALESCE(ph.user_agent, 'Unknown') as request_user_agent
        FROM email_change_requests ecr
        LEFT JOIN users u ON ecr.user_email = u.email
        LEFT JOIN profile_history ph ON ecr.user_email = ph.user_email 
            AND ecr.new_email = ph.new_value 
            AND ph.change_type = 'email_change'
        ORDER BY ecr.request_date DESC";
    
    $requests_result = $conn->query($requests_query);
    
    if ($requests_result === false) {
        throw new Exception("Failed to fetch email approvals");
    }
    
    $requests = [];
    while ($row = $requests_result->fetch_assoc()) {
        // Format dates
        $row['request_date'] = date('Y-m-d H:i:s', strtotime($row['request_date']));
        if ($row['admin_action_date']) {
            $row['admin_action_date'] = date('Y-m-d H:i:s', strtotime($row['admin_action_date']));
        }
        $requests[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $requests
    ]);

} catch (Exception $e) {
    error_log("Email approvals error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Error loading email approvals. Please try again.',
        'debug_message' => $e->getMessage()
    ]);
} 