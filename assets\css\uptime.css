* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Open Sans", sans-serif;
    background: #0a0e27;
    color: #ffffff;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Jost", sans-serif;
}

.uptime-section {
    padding: 120px 0 2rem 0;
    min-height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.server-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    border-radius: 20px;
    margin-bottom: 25px;
    padding: 25px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: white;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.server-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 30%, 
        rgba(79, 195, 247, 0.1) 50%, 
        transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.server-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(79, 195, 247, 0.5);
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(79, 195, 247, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.server-card:hover::before {
    opacity: 1;
}

.server-type {
    display: inline-block;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
    padding: 6px 16px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.server-card h3 {
    color: #ffffff;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 18px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
}

.server-info {
    margin-bottom: 20px;
    text-align: left;
}

.server-info p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 10px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.server-info p:hover {
    color: #4fc3f7;
    background: rgba(79, 195, 247, 0.1);
    padding-left: 10px;
}

.server-info i {
    color: #4fc3f7;
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 0.9rem;
}

.status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 24px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.8rem;
    margin: 15px 0;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    position: relative;
    overflow: hidden;
    min-width: 100px;
}

.status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.status:hover::before {
    left: 100%;
}

.status-online {
    background: linear-gradient(135deg, #00e676, #4caf50);
    color: white;
    box-shadow: 
        0 6px 20px rgba(0, 230, 118, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.status-offline {
    background: linear-gradient(135deg, #ff5252, #f44336);
    color: white;
    box-shadow: 
        0 6px 20px rgba(255, 82, 82, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.status-warning {
    background: linear-gradient(135deg, #ffab00, #ff9800);
    color: white;
    box-shadow: 
        0 6px 20px rgba(255, 171, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.server-stats {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Keep server card stats as grid */
.server-card .stat-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4fc3f7, #29b6f6);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    background: rgba(79, 195, 247, 0.1);
    transform: translateY(-2px);
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 600;
}

.last-check {
    text-align: center;
    margin-top: 20px;
    padding: 40px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.last-check small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.server-name {
    color: #4fc3f7;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title h2 {
    color: #4fc3f7;
    text-align: center;
    margin-bottom: 50px;
    font-weight: 600;
}

.filter-buttons {
    text-align: center;
    margin-bottom: 30px;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 8px 20px;
    margin: 0 5px;
    border-radius: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(79, 195, 247, 0.2);
    border-color: #4fc3f7;
    transform: translateY(-2px);
}

.overall-stats {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 40px;
    text-align: center;
}

.overall-stats h3 {
    color: #4fc3f7;
    margin-bottom: 20px;
    font-weight: 600;
}

/* Overall stats horizontal layout */
.overall-stats .server-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.overall-stats .stat-item {
    flex: 1;
    min-width: 120px;
    padding: 20px 15px;
    margin: 0;
}

/* Server Grid Layout */
.server-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.server-item {
    width: 100%;
}

/* Horizontal Server Card */
.server-card-horizontal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: white;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.server-card-horizontal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 30%, 
        rgba(79, 195, 247, 0.1) 50%, 
        transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.server-card-horizontal:hover {
    transform: translateY(-5px) scale(1.02);
    border-color: rgba(79, 195, 247, 0.5);
    box-shadow: 
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(79, 195, 247, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.server-card-horizontal:hover::before {
    opacity: 1;
}

/* Compact server info for horizontal layout */
.server-card-horizontal .server-info {
    margin-bottom: 15px;
}

.server-card-horizontal .server-info p {
    margin-bottom: 6px;
    font-size: 0.8rem;
    padding: 3px 0;
}

.server-card-horizontal h3 {
    font-size: 1.1rem;
    margin-bottom: 12px;
}

.server-card-horizontal .server-stats {
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.server-card-horizontal .stat-row {
    gap: 15px;
    margin-bottom: 15px;
}

.server-card-horizontal .stat-item {
    padding: 12px;
}

.server-card-horizontal .last-check {
    margin-top: 15px;
    padding: 8px;
}

/* Responsive design for overall stats */
@media (max-width: 768px) {
    .overall-stats .server-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .overall-stats .stat-item {
        min-width: 100px;
        padding: 15px 10px;
    }
    
    .stat-value {
        font-size: 1.4rem;
    }
    
    .stat-label {
        font-size: 0.65rem;
    }
    
    /* Server grid responsive */
    .server-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
    }
    
    .server-card-horizontal {
        min-height: 260px;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .overall-stats .server-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .overall-stats .stat-item {
        padding: 12px 8px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
    
    /* Mobile server grid */
    .server-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .server-card-horizontal {
        min-height: 240px;
        padding: 15px;
    }
}

.refresh-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
}

.refresh-btn:hover {
    transform: scale(1.1) rotate(180deg);
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
}

/* Loading Indicator */
#loading-indicator {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    margin: 20px 0;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.text-primary {
    color: #4fc3f7 !important;
}

.server-content {
    display: none;
}

.server-content.loaded {
    display: block;
}

/* No hosting state styles */
.no-hosting {
    text-align: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    border: 2px dashed #4fc3f7;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    margin: 0 auto;
}

.no-hosting .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    border-radius: 50%;
    margin: 0 auto 25px auto;
    box-shadow: 0 10px 30px rgba(79, 195, 247, 0.4);
    position: relative;
    overflow: hidden;
}

.no-hosting .icon-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.no-hosting .icon-container i {
    font-size: 40px;
    color: white;
    z-index: 2;
    position: relative;
}

.no-hosting h3 {
    color: #4fc3f7;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.no-hosting p {
    color: #b0b0b0;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.no-hosting .btn {
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
}

.no-hosting .btn:hover {
    background: linear-gradient(45deg, #3a9bc1, #1976d2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
    color: white;
    text-decoration: none;
}