RewriteEngine On

# Set proper MIME types for CSS and other assets
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType font/ttf .ttf
    AddType font/otf .otf
    AddType font/eot .eot
</IfModule>

# Force correct MIME type for CSS files
<FilesMatch "\.(css)$">
    Header set Content-Type "text/css; charset=utf-8"
    Header set Cache-Control "public, max-age=3600"
</FilesMatch>

# Force correct MIME types for JavaScript files
<FilesMatch "\.(js)$">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set Cache-Control "public, max-age=3600"
</FilesMatch>

# Enable GZIP compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Allow direct access to static assets (CSS, JS, images)
RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|ico|svg)$ [NC]
RewriteRule ^ - [L]

# Allow direct access to font files (no proxy needed for local assets)
RewriteCond %{REQUEST_URI} \.(woff|woff2|ttf|otf|eot)$ [NC]
RewriteRule ^ - [L]

# Allow direct access to handler files and API endpoints
RewriteCond %{REQUEST_URI} _handler\.php$ [NC,OR]
RewriteCond %{REQUEST_URI} /auth/captcha\.php$ [NC,OR]
RewriteCond %{REQUEST_URI} /auth/.*\.php$ [NC,OR]
RewriteCond %{REQUEST_URI} /api/.*\.php$ [NC,OR]
RewriteCond %{REQUEST_URI} /admin/api/.*\.php$ [NC,OR]
RewriteCond %{REQUEST_URI} /test_.*\.php$ [NC]
RewriteRule ^ - [L]

# Handle pages folder specifically
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^pages/([^\.]+)/?$ pages/$1.php [NC,L]

# Handle auth folder specifically  
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^auth/([^\.]+)/?$ auth/$1.php [NC,L]

# Handle admin/api folder specifically - add .php extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^admin/api/([^\.]+)/?$ admin/api/$1.php [NC,L]

# Handle admin folder specifically (but exclude admin/api)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !/admin/api/ [NC]
RewriteRule ^admin/([^\.]+)/?$ admin/$1.php [NC,L]

# Handle API folder specifically - add .php extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^api/([^\.]+)/?$ api/$1.php [NC,L]

# Handle root level files (but not those handled by specific rules above)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !(auth|pages|admin|api)/ [NC]
RewriteRule ^([^/\.]+)/?$ $1.php [NC,L]

# Security Headers (only if mod_headers is available)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always unset X-Powered-By
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to database files
<Files "database.php">
    Order allow,deny
    Deny from all
</Files>

# Block access to config files
<FilesMatch "config\.(php|inc|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Block suspicious requests
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (union|select|insert|drop|delete|update|cast|create|char|convert|alter|declare|exec|script) [NC,OR]
    RewriteCond %{QUERY_STRING} (\.\./|\.\.\%2F) [NC,OR]
    RewriteCond %{QUERY_STRING} (localhost|loopback|127\.0\.0\.1) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*(\>|%3E) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Remove trailing slash
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.+)/$ /$1 [R=301,L]

# Clean URLs - Remove .php extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Allow access to assets directory
RewriteRule ^assets/(.*)$ assets/$1 [L]

# Allow access to API directories
RewriteRule ^api/(.*)$ api/$1 [L]
RewriteRule ^admin/api/(.*)$ admin/api/$1 [L]

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

