-- Create products table
CREATE TABLE IF NOT EXISTS `produk` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `type` VARCHAR(255) NOT NULL,
    `nama` VARCHAR(255) NOT NULL,
    `harga_normal` VARCHAR(255) NOT NULL,
    `harga_diskon` VARCHAR(255),
    `deskripsi` TEXT,
    `gambar` VARCHAR(255),
    `image_links` TEXT,
    `spesifikasi` TEXT,
    `slot` VARCHAR(255) DEFAULT '30 SLOT,60 SLOT,120 SLOT,150 SLOT'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial data
INSERT INTO `produk` (`type`, `nama`, `harga_normal`, `harga_diskon`, `deskripsi`, `gambar`) VALUES
('FIVEM', 'FiveM Gaming #1', '700000', '500000', 'Testing description for FiveM Gaming Server #1', 'https://dopminer.com/Gambar/image-2024-03-10-172946442.png');

-- Update existing records to include sample image links
UPDATE produk SET image_links = CONCAT(
    'https://dopminer.com/Gambar/CnP_09102024_222723.png|',
    'https://dopminer.com/Gambar/image-2024-03-10-172946442.png'
) WHERE id = 1;