<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Dopminer.com - App Hosting</title>
    <meta content="" name="description">
    <meta content="" name="keywords">
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <link href="../assets/img/Dopminer.png" rel="apple-touch-icon">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        #app .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 20px;
        }
        #app .content {
            width: 100%;
            max-width: 900px;
            margin-bottom: 20px;
        }
        #app .fitur {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 0;
            justify-content: center;
        }
        #app .fitur li {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        #app .serangan {
            padding: 10px 10px;
            background-color: #1c2030;
            margin-top: 9px;
            border-radius: 10px;
            border: 2px solid #77cdffa1;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: border-color 0.3s;
        }
        #app .serangan:hover {
            border-color: #77cdfff0;
        }
        #app .serangan.highlight {
            border-color: #ffffff;
        }
        #app .serangan .status-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        #app .serangan .status {
            padding: 2px;
            border-radius: 5px;
            color: white;
            width: 70px;
            text-align: center;
            box-sizing: border-box;
            /* border: 2px solid #4d4d4d; */
        }
        #app .berlangsung {
            background-color: transparent;
            border-color: #28a745;
        }
        #app .selesai {
            background-color: transparent;
            border-color: #28a745;
        }
        .logo-wrapper {
            width: 45px;
            height: 45px;
            border-radius: 15%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #4d4d4d;
        }
        .logo-wrapper.no-border {
            border: none;
        }
        .logo-img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
        .logo-bendera-img {
            width: 70%;
            height: auto;
            object-fit: cover;
        }
        .logo-container {
            display: flex;
            align-items: center;
        }
        .server-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 10px;
            color: #7d7d7d;
            font-size: 13px;
        }
        .game-info {
            font-size: 14px;
        }
        @media (max-width: 600px) {
            #app .container {
                padding: 0 10px;
            }
            #app .serangan {
                flex-direction: column;
                align-items: flex-start;
            }
            #app .serangan .status-container {
                margin-top: 10px;
            }
        }
        #searchInput::placeholder {
            color: #ffffff;
        }
        .input-group-text {
            background-color: #333333;
            border: 2px solid #555555;
            color: #ffffff;
        }
        .form-control, .form-select {
            background-color: #333333;
            border: 2px solid #555555;
            color: #ffffff;
        }
        .input-group, .form-control, .form-select {
            font-size: 12px;
        }
        /*#serverList {*/
        /*    background-image: url('https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fget.wallhere.com%2Fphoto%2Fblack-abstract-1333583.jpg&f=1&nofb=1&ipt=86b912b8c1d0e558a40352841cee13c5b6891ba174db729b67259ce7547ebd24&ipo=images');*/
        /*    background-size: cover;*/
        /*    background-position: center;*/
        /*}*/
        /*#serverList .serangan {*/
            background: rgba(43 43 43 / 59%); /* Optional: Adds a semi-transparent background to each item */
        /*}*/
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>
    <!-- ======= Hero Section ======= -->
    <section id="app" class="pricing" style="padding: 50px 0 120px 0">
        <div class="container">
            <div class="content">
                <div class="section-title">
                    <h2>SERVERLIST</h2>
                    <p>Serverlist ini hanya yang Berlangganan di Dopminer.</p>
                </div>
                <div class="d-flex justify-content-between mb-3 align-items-center">
                    <!--<p class="me-3" style="color: #ffffff; font-size: 16px; margin-top: 10px; margin-left: 25px;"><b>Serverlist ini hanya yang Berlangganan di Dopminer.</b></p>-->
                    <div class="d-flex">
                        <input id="searchInput" type="text" class="form-control me-2" placeholder="Cari server..." style="height: 45px; width: 450px; background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                        <div class="input-group me-2" style="width: 120px; height: 40px;">
                            <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                                <i class="bi bi-globe"></i>
                            </span>
                            <select id="filterInput" class="form-select" style="padding: 12px 2px 13px 5px; background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                                <option value="all">Indonesia</option>
                            </select>
                        </div>
                        <div class="input-group me-2" style="width: 120px; height: 40px;">
                            <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                                <i class="bi bi-filter"></i>
                            </span>
                            <select id="filterCategory" class="form-select" style="padding: 12px 2px 13px 5px; background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                                <option value="all">Filter</option>
                                <option value="FIVEM">FiveM</option>
                                <option value="SAMP">SA:MP</option>
                                <!-- Tambahkan opsi lainnya sesuai kebutuhan -->
                            </select>
                        </div>
                        <button id="prevPage" class="btn btn-primary me-2" style="font-size: 15px; background-color: #333333; border: 2px solid #555555; color: #fff; padding: 2px 8px;" disabled><i class="bi bi-caret-left"></i> Previous</button>
                        <button id="nextPage" class="btn btn-primary" style="font-size: 15px; background-color: #333333; border: 2px solid #555555; color: #fff; padding: 2px 16px;">Next <i class="bi bi-caret-right"></i></button>
                    </div>
                </div>
                <?php include '../database/database.php'; ?>
                <div id="serverList">
                    <?php
                    $sql = "SELECT nama_server, players, max_players, type_game FROM serverlist WHERE type_game IN ('FIVEM', 'SAMP') ORDER BY players DESC";
                    $result = $conn->query($sql);

                    if ($result->num_rows > 0) {
                        $servers = [];
                        while($row = $result->fetch_assoc()) {
                            $servers[] = $row;
                        }
                        $totalPages = ceil(count($servers) / 10);
                    } else {
                        ?>
                        <p>No servers found.</p>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </div>
        <script>
            var currentPage = 1;
            var totalPages = <?php echo $totalPages; ?>;
            var allServers = <?php echo json_encode($servers); ?>;
            var servers = allServers;

            function renderServers() {
                var serverList = document.getElementById('serverList');
                serverList.innerHTML = '';
                var start = (currentPage - 1) * 10;
                var end = Math.min(start + 10, servers.length);

                for (var i = start; i < end; i++) {
                    var row = servers[i];
                    var serverDiv = document.createElement('div');
                    serverDiv.className = 'serangan d-flex align-items-center justify-content-between';
                    serverDiv.setAttribute('data-category', row.type_game);
                    serverDiv.style.cursor = 'pointer';
                    serverDiv.style.backgroundColor = '#5389a924';
                    serverDiv.innerHTML = `
                        <div class="logo-container d-flex align-items-center">
                            <div class="logo-wrapper">
                                <a href="link_to_page.html">
                                    ${row.type_game === 'FIVEM' ? '<img src="https://dopminer.com/Gambar/ezgif-2-b5fb708071.png" alt="FiveM Logo" class="logo-img">' : '<img src="https://dopminer.com/Gambar/sampdnew.png" alt="SA:MP Logo" class="logo-img">'}
                                </a>
                            </div>
                            <div class="server-info">
                                <div class="game-info"><b>${row.nama_server}</b></div>
                                <a>${row.nama_server} Adalah server Roleplay dari Indonesia</a>
                            </div>
                        </div>
                        <!-- <div>${row.type_game}</div> -->
                        <div class="status-container">
                            <div class="logo-wrapper no-border">
                                <a href="link_to_page.html">
                                    <img src="../assets/img/bendera.png" alt="Logo" class="logo-bendera-img">
                                </a>
                            </div>
                            <div class="status berlangsung">${row.players}/${row.max_players}</div>
                        </div>
                    `;
                    serverList.appendChild(serverDiv);
                }

                document.getElementById('prevPage').disabled = currentPage === 1;
                document.getElementById('nextPage').disabled = currentPage === totalPages;
            }

            document.getElementById('prevPage').addEventListener('click', function () {
                if (currentPage > 1) {
                    currentPage--;
                    renderServers();
                }
            });

            document.getElementById('nextPage').addEventListener('click', function () {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderServers();
                }
            });

            document.getElementById('searchInput').addEventListener('input', function () {
                var searchTerm = this.value.toLowerCase();
                if (searchTerm === '') {
                    servers = allServers;
                } else {
                    servers = allServers.filter(function (server) {
                        return server.nama_server.toLowerCase().includes(searchTerm);
                    });
                }
                currentPage = 1;
                renderServers();
            });

            document.getElementById('filterCategory').addEventListener('change', function () {
                var filterValue = this.value;
                if (filterValue === 'all') {
                    servers = allServers;
                } else {
                    servers = allServers.filter(function (server) {
                        return server.type_game === filterValue;
                    });
                }
                currentPage = 1;
                renderServers();
            });

            document.addEventListener('DOMContentLoaded', function () {
                renderServers();
            });
        </script>
    </section>
    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>
    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
