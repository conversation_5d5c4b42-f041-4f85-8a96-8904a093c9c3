<?php
/**
 * Main Configuration File for DopMiner Website
 * Security and Environment Settings
 */

// Security configuration file for DopMiner
// This file can be included safely in any part of the application

// Define application constants
define('DOPMINER_VERSION', '2.0.0');
define('DOPMINER_ENV', $_ENV['ENVIRONMENT'] ?? 'production');

// Security settings will be set when session starts

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Production security settings
if (DOPMINER_ENV === 'production' || !isLocalhost()) {
    // Disable error display for production
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    
    // Enable error logging
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/php_errors.log');
    
    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
    
} else {
    // Development settings
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
}

// Database configuration
$db_config = [
    'host' => $_ENV['DB_HOST'] ?? 'db.dopminer.cloud',
    'username' => $_ENV['DB_USER'] ?? 'dopminer',
    'password' => $_ENV['DB_PASS'] ?? 'Dopminer!@007',
    'database' => $_ENV['DB_NAME'] ?? 'dopminer',
    'charset' => 'utf8mb4'
];

// Application settings
$app_config = [
    'site_name' => 'DopMiner Hosting',
    'site_url' => $_ENV['SITE_URL'] ?? 'https://dopminer.com',
    'admin_email' => $_ENV['ADMIN_EMAIL'] ?? '<EMAIL>',
    'max_login_attempts' => 5,
    'session_timeout' => 3600, // 1 hour
    'file_upload_max_size' => 5 * 1024 * 1024, // 5MB
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']
];

// Security functions
function secure_session_start() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
        
        // Regenerate session ID periodically
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
}

// Input validation function
function validate_input($data, $type = 'string') {
    $data = trim($data);
    $data = stripslashes($data);
    
    switch ($type) {
        case 'email':
            return filter_var($data, FILTER_VALIDATE_EMAIL);
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT);
        case 'url':
            return filter_var($data, FILTER_VALIDATE_URL);
        case 'string':
        default:
            return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

// Check if user is authenticated
function is_authenticated() {
    return isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true;
}

// Check if user is admin
function is_admin() {
    return is_authenticated() && isset($_SESSION['group']) && $_SESSION['group'] === 'ADMIN';
}

// Log security events
function log_security_event($event, $details = []) {
    $log_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'event' => $event,
        'details' => $details
    ];
    
    $log_file = __DIR__ . '/../logs/security.log';
    file_put_contents($log_file, json_encode($log_data) . "\n", FILE_APPEND | LOCK_EX);
}

// Secure redirect function
function secure_redirect($url) {
    // Validate URL to prevent open redirect
    $parsed = parse_url($url);
    if (isset($parsed['host']) && $parsed['host'] !== $_SERVER['HTTP_HOST']) {
        $url = '/'; // Redirect to home if external URL
    }
    
    header('Location: ' . $url);
    exit;
}

// Set session parameters and start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    // Use @ to suppress warnings in case settings are already configured
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @ini_set('session.cookie_samesite', 'Strict');
    
    @session_start();
}

// Detect if we're on localhost or online
function isLocalhost() {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    return (strpos($host, 'localhost') !== false || 
            strpos($host, '127.0.0.1') !== false ||
            strpos($host, '::1') !== false);
}

// Check if we're on a development/testing server
function isDevelopmentServer() {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    return (isLocalhost() || 
            strpos($host, 'institute-seeker.gl.at.ply.gg') !== false ||
            strpos($host, 'playit.plus') !== false ||
            strpos($host, 'playit.gg') !== false);
}

// Updated asset function - no more /dopminer/ prefix
function asset($path) {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
    
    // For localhost, use relative paths from root
    if (isLocalhost()) {
        return '/assets/' . ltrim($path, '/');
    }
    
    // For development servers (institute, playit, etc.)
    if (isDevelopmentServer()) {
        // For CSS files, use CSS handler to ensure correct MIME type and fix font paths
        if ($extension === 'css') {
            return '/css_handler.php?file=' . ltrim($path, '/');
        }
        
        // For images only, redirect to dopminer.com to save bandwidth (optional)
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'ico'])) {
            // Use local files for development to avoid CORS issues
            return '/assets/' . ltrim($path, '/');
        }
        
        // For all other assets (JS, fonts, etc), use local files directly
        return '/assets/' . ltrim($path, '/');
    }
    
    // For online (dopminer.com), use absolute paths
    return 'https://dopminer.com/assets/' . ltrim($path, '/');
}

// Updated URL function - no more /dopminer/ prefix
function url($path = '') {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    
    // For localhost
    if (isLocalhost()) {
        return 'http://localhost/' . ltrim($path, '/');
    }
    
    // For development servers
    if (isDevelopmentServer()) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $port = $_SERVER['SERVER_PORT'] ?? '80';
        $port_part = ($port != '80' && $port != '443') ? ':' . $port : '';
        return $protocol . '://' . $host . $port_part . '/' . ltrim($path, '/');
    }
    
    // For online production
    return 'https://dopminer.com/' . ltrim($path, '/');
}

// Updated root URL function - no more /dopminer/ prefix
function root_url($path = '') {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    
    // For all environments, use root path
    return '/' . ltrim($path, '/');
}

?> 