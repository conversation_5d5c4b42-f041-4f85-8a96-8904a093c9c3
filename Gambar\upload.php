<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$targetDir = __DIR__ . "/"; // Path absolut ke direktori saat ini
$uploadDir = ""; // Direktori untuk upload, kosong karena berada di direktori yang sama
$targetFile = $targetDir . basename($_FILES["gambar"]["name"]);
$uploadOk = 1;
$imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));

// Cek apakah direktori tujuan ada
if (!is_dir($targetDir)) {
    echo "Direktori tujuan tidak ditemukan.";
    $uploadOk = 0;
}

// Cek apakah file gambar adalah gambar sebenarnya
if (isset($_POST["submit"])) {
    $check = getimagesize($_FILES["gambar"]["tmp_name"]);
    if ($check !== false) {
        $uploadOk = 1;
    } else {
        echo "File bukan gambar.";
        $uploadOk = 0;
    }
}

// Cek apakah file sudah ada
if (file_exists($targetFile)) {
    echo "Maaf, file sudah ada.";
    $uploadOk = 0;
}

// Batasi format file
if ($imageFileType != "jpg" && $imageFileType != "jpeg" && $imageFileType != "png" && $imageFileType != "gif") {
    echo "Maaf, hanya file JPG, JPEG, PNG & GIF yang diperbolehkan.";
    $uploadOk = 0;
}

// Cek apakah $uploadOk bernilai 0 karena kesalahan
if ($uploadOk == 0) {
    echo "Maaf, file tidak diunggah.";
} else {
    if (move_uploaded_file($_FILES["gambar"]["tmp_name"], $targetFile)) {
        echo "File " . htmlspecialchars(basename($_FILES["gambar"]["name"])) . " telah diunggah.";
        header("Location: get.php"); // Redirect kembali ke halaman galeri setelah upload
    } else {
        // Menulis detail kesalahan ke file log
        $error = isset($_FILES["gambar"]["error"]) ? $_FILES["gambar"]["error"] : 'Tidak ada informasi kesalahan.';
        file_put_contents('upload_error_log.txt', date('Y-m-d H:i:s') . " - Kesalahan Upload: " . $error . PHP_EOL, FILE_APPEND);
        
        echo "Maaf, terjadi kesalahan saat mengunggah file.";
        echo "<br>Detail Kesalahan: " . htmlspecialchars($error);
    }
}
?>
