<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Dopminer.com - App Hosting</title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: Arsha
  * Updated: Jan 29 2024 with Bootstrap v5.3.2
  * Template URL: https://bootstrapmade.com/arsha-free-bootstrap-html-template-corporate/
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body>

    <?php include '../components/navbar.php'; ?>

    <!-- ======= Hero Section ======= -->

    <section id="app" class="pricing" style="padding: 50px 0 120px 0">
        <div class="container">
            <div class="content">
                <div class="section-title">
                    <h2>SERVERLIST</h2>
                </div>
                <div class="d-flex justify-content-end mb-3 align-items-center">
                    <input id="searchInput" type="text" class="form-control me-2" placeholder="Cari server..." style="width: 200px; background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                    <div class="input-group me-2" style="width: 150px;">
                        <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <i class="bi bi-filter"></i>
                        </span>
                        <select id="filterInput" class="form-select" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <option value="all">All</option>
                            <option value="fivem">FIVEM</option>
                            <option value="sa:mp">SA:MP</option>
                        </select>
                    </div>
                    <div class="input-group" style="width: 150px;">
                        <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <i class="bi bi-tags"></i>
                        </span>
                        <select id="filterCategory" class="form-select" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <option value="all">All Categories</option>
                            <option value="category1">Category 1</option>
                            <option value="category2">Category 2</option>
                            <!-- Tambahkan opsi lainnya sesuai kebutuhan -->
                        </select>
                    </div>
                </div>
                <div id="serverList">
                    <div class="serangan d-flex align-items-center justify-content-between" data-category="category1">
                        <div class="logo-container d-flex align-items-center">
                            <div class="logo-wrapper">
                                <img src="../assets/img/pma3.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="server-info">
                                <div class="game-info"><b>Quantum Roleplay</b></div>
                                <a>serangan d-flex align-items-center justify-content-between</a>
                            </div>
                        </div>
                        <div>Fivem</div>
                        <div class="status-container">
                            <div class="logo-wrapper no-border">
                                <img src="../assets/img/bendera.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="status berlangsung">130/200</div>
                        </div>
                    </div>

                    <div class="serangan d-flex align-items-center justify-content-between" data-category="category2">
                        <div class="logo-container d-flex align-items-center">
                            <div class="logo-wrapper">
                                <img src="../assets/img/pma3.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="server-info">
                                <div class="game-info"><b>Sadewa Roleplay</b></div>
                                <a>serangan d-flex align-items-center justify-content-between</a>
                            </div>
                        </div>
                        <div>SA:MP</div>
                        <div class="status-container">
                            <div class="logo-wrapper no-border">
                                <img src="../assets/img/bendera.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="status selesai">12/30</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <style>
            #app .container {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 20px;
            }

            #app .content {
                width: 100%;
                max-width: 900px;
                margin-bottom: 20px;
            }

            #app .fitur {
                list-style: none;
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                padding: 0;
                justify-content: center;
            }

            #app .fitur li {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            #app .serangan {
                padding: 10px 10px;
                background-color: #1c2030;
                margin-top: 20px;
                border-radius: 10px;
                border: 2px solid #4d4d4d;
                width: 100%;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                transition: border-color 0.3s;
            }

            #app .serangan.highlight {
                border-color: #ffffff;
            }

            #app .serangan .status-container {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            #app .serangan .status {
                padding: 2px;
                border-radius: 5px;
                color: white;
                width: 70px;
                text-align: center;
                box-sizing: border-box;
                border: 2px solid #4d4d4d;
            }

            #app .berlangsung {
                background-color: transparent;
                border-color: #28a745;
            }

            #app .selesai {
                background-color: transparent;
                border-color: #28a745;
            }

            .logo-wrapper {
                width: 45px;
                height: 45px;
                border-radius: 15%;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #4d4d4d;
            }

            .logo-wrapper.no-border {
                border: none;
            }

            .logo-img {
                width: 70%;
                height: auto;
                object-fit: cover;
            }

            .logo-container {
                display: flex;
                align-items: center;
            }

            .server-info {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 10px;
                color: #7d7d7d;
                font-size: 13px;
            }

            .game-info {
                font-size: 14px;
            }

            @media (max-width: 600px) {
                #app .container {
                    padding: 0 10px;
                }

                #app .serangan {
                    flex-direction: column;
                    align-items: flex-start;
                }

                #app .serangan .status-container {
                    margin-top: 10px;
                }
            }

            #searchInput::placeholder {
                color: #ffffff;
            }

            .input-group-text {
                background-color: #333333;
                border: 2px solid #555555;
                color: #ffffff;
            }

            .form-control, .form-select {
                background-color: #333333;
                border: 2px solid #555555;
                color: #ffffff;
            }
        </style>

        <script>
            document.getElementById('searchInput').addEventListener('keyup', function () {
                filterServers();
            });

            document.getElementById('filterInput').addEventListener('change', function () {
                filterServers();
            });

            document.getElementById('filterCategory').addEventListener('change', function () {
                filterServers();
            });

            function filterServers() {
                var searchInput = document.getElementById('searchInput').value.toLowerCase();
                var filterInput = document.getElementById('filterInput').value.toLowerCase();
                var filterCategory = document.getElementById('filterCategory').value.toLowerCase();
                var servers = document.querySelectorAll('.serangan');
                var serverList = document.getElementById('serverList');
                var matchedServers = [];
                var unmatchedServers = [];

                servers.forEach(function (server) {
                    var serverName = server.querySelector('.game-info b').textContent.toLowerCase();
                    var serverType = server.querySelector('.game-info').nextElementSibling.textContent.toLowerCase();
                    var serverCategory = server.getAttribute('data-category').toLowerCase();

                    if ((serverName.includes(searchInput) || searchInput === '') && 
                        (filterInput === 'all' || serverType.includes(filterInput)) &&
                        (filterCategory === 'all' || serverCategory.includes(filterCategory))) {
                        matchedServers.push(server);
                    } else {
                        unmatchedServers.push(server);
                    }
                });

                // Clear the current server list
                serverList.innerHTML = '';

                // Append matched servers first and add highlight
                matchedServers.forEach(function (server) {
                    server.classList.add('highlight');
                    server.style.display = 'flex';
                    serverList.appendChild(server);
                });

                // Append unmatched servers after and remove highlight
                unmatchedServers.forEach(function (server) {
                    server.classList.remove('highlight');
                    server.style.display = 'flex';
                    serverList.appendChild(server);
                });

                // If search input is empty, remove highlights from all servers
                if (searchInput === '') {
                    servers.forEach(function (server) {
                        server.classList.remove('highlight');
                    });
                }
            }
        </script>
    </section>

    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

</body>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Dopminer.com - App Hosting</title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: Arsha
  * Updated: Jan 29 2024 with Bootstrap v5.3.2
  * Template URL: https://bootstrapmade.com/arsha-free-bootstrap-html-template-corporate/
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body>

    <?php include '../components/navbar.php'; ?>

    <!-- ======= Hero Section ======= -->

    <section id="app" class="pricing" style="padding: 50px 0 120px 0">
        <div class="container">
            <div class="content">
                <div class="section-title">
                    <h2>SERVERLIST</h2>
                </div>
                <div class="d-flex justify-content-end mb-3 align-items-center">
                    <input id="searchInput" type="text" class="form-control me-2" placeholder="Cari server..." style="width: 200px; background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                    <div class="input-group me-2" style="width: 150px;">
                        <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <i class="bi bi-filter"></i>
                        </span>
                        <select id="filterInput" class="form-select" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <option value="all">All</option>
                            <option value="fivem">FIVEM</option>
                            <option value="sa:mp">SA:MP</option>
                        </select>
                    </div>
                    <div class="input-group" style="width: 150px;">
                        <span class="input-group-text" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <i class="bi bi-tags"></i>
                        </span>
                        <select id="filterCategory" class="form-select" style="background-color: #333333; border: 2px solid #555555; color: #ffffff;">
                            <option value="all">All Categories</option>
                            <option value="category1">Category 1</option>
                            <option value="category2">Category 2</option>
                            <!-- Tambahkan opsi lainnya sesuai kebutuhan -->
                        </select>
                    </div>
                </div>
                <div id="serverList">
                    <div class="serangan d-flex align-items-center justify-content-between" data-category="category1">
                        <div class="logo-container d-flex align-items-center">
                            <div class="logo-wrapper">
                                <img src="../assets/img/pma3.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="server-info">
                                <div class="game-info"><b>Quantum Roleplay</b></div>
                                <a>serangan d-flex align-items-center justify-content-between</a>
                            </div>
                        </div>
                        <div>Fivem</div>
                        <div class="status-container">
                            <div class="logo-wrapper no-border">
                                <img src="../assets/img/bendera.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="status berlangsung">130/200</div>
                        </div>
                    </div>

                    <div class="serangan d-flex align-items-center justify-content-between" data-category="category2">
                        <div class="logo-container d-flex align-items-center">
                            <div class="logo-wrapper">
                                <img src="../assets/img/pma3.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="server-info">
                                <div class="game-info"><b>Sadewa Roleplay</b></div>
                                <a>serangan d-flex align-items-center justify-content-between</a>
                            </div>
                        </div>
                        <div>SA:MP</div>
                        <div class="status-container">
                            <div class="logo-wrapper no-border">
                                <img src="../assets/img/bendera.png" alt="Logo" class="logo-img">
                            </div>
                            <div class="status selesai">12/30</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <style>
            #app .container {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 20px;
            }

            #app .content {
                width: 100%;
                max-width: 900px;
                margin-bottom: 20px;
            }

            #app .fitur {
                list-style: none;
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                padding: 0;
                justify-content: center;
            }

            #app .fitur li {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            #app .serangan {
                padding: 10px 10px;
                background-color: #1c2030;
                margin-top: 20px;
                border-radius: 10px;
                border: 2px solid #4d4d4d;
                width: 100%;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                transition: border-color 0.3s;
            }

            #app .serangan.highlight {
                border-color: #ffffff;
            }

            #app .serangan .status-container {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            #app .serangan .status {
                padding: 2px;
                border-radius: 5px;
                color: white;
                width: 70px;
                text-align: center;
                box-sizing: border-box;
                border: 2px solid #4d4d4d;
            }

            #app .berlangsung {
                background-color: transparent;
                border-color: #28a745;
            }

            #app .selesai {
                background-color: transparent;
                border-color: #28a745;
            }

            .logo-wrapper {
                width: 45px;
                height: 45px;
                border-radius: 15%;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #4d4d4d;
            }

            .logo-wrapper.no-border {
                border: none;
            }

            .logo-img {
                width: 70%;
                height: auto;
                object-fit: cover;
            }

            .logo-container {
                display: flex;
                align-items: center;
            }

            .server-info {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 10px;
                color: #7d7d7d;
                font-size: 13px;
            }

            .game-info {
                font-size: 14px;
            }

            @media (max-width: 600px) {
                #app .container {
                    padding: 0 10px;
                }

                #app .serangan {
                    flex-direction: column;
                    align-items: flex-start;
                }

                #app .serangan .status-container {
                    margin-top: 10px;
                }
            }

            #searchInput::placeholder {
                color: #ffffff;
            }

            .input-group-text {
                background-color: #333333;
                border: 2px solid #555555;
                color: #ffffff;
            }

            .form-control, .form-select {
                background-color: #333333;
                border: 2px solid #555555;
                color: #ffffff;
            }
        </style>

        <script>
            document.getElementById('searchInput').addEventListener('keyup', function () {
                filterServers();
            });

            document.getElementById('filterInput').addEventListener('change', function () {
                filterServers();
            });

            document.getElementById('filterCategory').addEventListener('change', function () {
                filterServers();
            });

            function filterServers() {
                var searchInput = document.getElementById('searchInput').value.toLowerCase();
                var filterInput = document.getElementById('filterInput').value.toLowerCase();
                var filterCategory = document.getElementById('filterCategory').value.toLowerCase();
                var servers = document.querySelectorAll('.serangan');
                var serverList = document.getElementById('serverList');
                var matchedServers = [];
                var unmatchedServers = [];

                servers.forEach(function (server) {
                    var serverName = server.querySelector('.game-info b').textContent.toLowerCase();
                    var serverType = server.querySelector('.game-info').nextElementSibling.textContent.toLowerCase();
                    var serverCategory = server.getAttribute('data-category').toLowerCase();

                    if ((serverName.includes(searchInput) || searchInput === '') && 
                        (filterInput === 'all' || serverType.includes(filterInput)) &&
                        (filterCategory === 'all' || serverCategory.includes(filterCategory))) {
                        matchedServers.push(server);
                    } else {
                        unmatchedServers.push(server);
                    }
                });

                // Clear the current server list
                serverList.innerHTML = '';

                // Append matched servers first and add highlight
                matchedServers.forEach(function (server) {
                    server.classList.add('highlight');
                    server.style.display = 'flex';
                    serverList.appendChild(server);
                });

                // Append unmatched servers after and remove highlight
                unmatchedServers.forEach(function (server) {
                    server.classList.remove('highlight');
                    server.style.display = 'flex';
                    serverList.appendChild(server);
                });

                // If search input is empty, remove highlights from all servers
                if (searchInput === '') {
                    servers.forEach(function (server) {
                        server.classList.remove('highlight');
                    });
                }
            }
        </script>
    </section>

    <?php include '../components/footer.php'; ?>

    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

</body>

</html>
