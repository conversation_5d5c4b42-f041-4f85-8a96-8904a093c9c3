<!-- ======= Footer ======= -->
<footer id="footer">

  <div class="footer-top">
    <div class="container">
      <div class="row">

        <div class="col-lg-3 col-md-6 footer-contact">
          <h3>DOPMINER</h3>
          <p>
            Dopminer adalah per<PERSON> hosting yang menyediakan berbagai layanan hosting seperti Game hosting, Database Hosting, App hosting untuk memenuhi kebutuhan hosting Anda. <br>
          </p>
          <div class="company-info">
            <br><strong><p>PT. Dopminer Cyber Network</p></strong>
            <p>Hosting Provider Terpercaya Sejak 2019</p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 footer-links">
          <h4>Produk dan Lay<PERSON>n</h4>
          <ul>
            <li><i class="bx bx-chevron-right"></i> <a href="games">Game Hosting</a></li>
            <li><i class="bx bx-chevron-right"></i> <a href="app">App Hosting</a></li>
          </ul>
        </div>

        <div class="col-lg-3 col-md-6 footer-links">
          <h4>Legal</h4>
          <ul>
            <li><i class="bx bx-chevron-right"></i> <a href="terms">Terms</a></li>
            <li><i class="bx bx-chevron-right"></i> <a href="privacy">Privacy Policy</a></li>
          </ul>
        </div>

        <div class="col-lg-3 col-md-6 footer-links">
          <h4>Social Media</h4>
          <ul>
            <li><i class="bx bx-chevron-right"></i> <a href="https://discord.com/invite/dopminer">Discord</a></li>
            <li><i class="bx bx-chevron-right"></i> <a href="https://www.tiktok.com/@dopminer">Tiktok</a></li>
            <li><i class="bx bx-chevron-right"></i> <a href="https://www.instagram.com/@dopminer_official">Instagram</a></li>
          </ul>
          <div class="social-links mt-3">
            <a href="https://discord.com/invite/dopminer" class="google-plus"><i class="bx bxl-discord"></i></a>
            <a href="https://www.tiktok.com/@dopminer" class="linkedin"><i class="bx bxl-tiktok"></i></a>
            <a href="https://www.instagram.com/@dopminer_official" class="linkedin"><i class="bx bxl-instagram"></i></a>
          </div>
        </div>

      </div>
    </div>
  </div>

  <div class="container footer-bottom clearfix">
    <div class="bank-logos">
      <!-- Adding bank logos section with lazy loading -->
      <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-src="https://dopminer.com/Gambar/bca.png" alt="BCA" style="height: 22px; margin-right: 10px;" loading="lazy" class="lazy">
      <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-src="https://dopminer.com/Gambar/dana-logo-png-**********.png" alt="Permata Bank" style="height: 24px; margin-right: 10px;" loading="lazy" class="lazy">
      <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-src="https://dopminer.com/Gambar/logo-ovo-**********.png" alt="Shopee Pay" style="height: 33px; margin-right: 10px;" loading="lazy" class="lazy">
      <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-src="https://dopminer.com/Gambar/gopay (2) (1).png" alt="GoPay" style="height: 23px; margin-right: 10px;" loading="lazy" class="lazy">
      <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-src="https://dopminer.com/Gambar/qris_putih-**********.png" alt="Paypal" style="height: 20px; margin-right: 10px;" loading="lazy" class="lazy">
    </div>
    <div class="credits">
      Powered by <a href="https://dopminer.com/">Dopminer Technology</a>
    </div>
  </div>

</footer><!-- End Footer -->

<?php
// Always include livechat popup - it will handle login state internally
// Use __DIR__ to get correct path regardless of where footer.php is included from
include __DIR__ . '/livechat-popup.php';
?>

<div id="preloader"></div>

<!-- Lazy Load Script -->
<script>
document.addEventListener("DOMContentLoaded", function() {
    var lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));

    if ("IntersectionObserver" in window) {
        let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    let lazyImage = entry.target;
                    lazyImage.src = lazyImage.dataset.src;
                    lazyImage.classList.remove("lazy");
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });

        lazyImages.forEach(function(lazyImage) {
            lazyImageObserver.observe(lazyImage);
        });
    }
    
    // Remove preloader after page load
    var preloader = document.getElementById('preloader');
    if(preloader) {
        setTimeout(function() {
            preloader.style.display = 'none';
        }, 500);
    }
});
</script>
