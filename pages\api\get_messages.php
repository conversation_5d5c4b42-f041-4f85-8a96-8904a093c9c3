<?php
session_start();
header('Content-Type: application/json');

// Enable error reporting for debugging
// Error reporting disabled for production security
error_reporting(0);
ini_set('display_errors', 0);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

include '../../database/database.php';

// Get current user email
$email = $_SESSION['email'];

// Check if user is admin
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$is_admin = ($user['group'] === 'ADMIN');

// Get idchat from URL parameter or session
$idchat = null;
if (isset($_GET['idchat'])) {
    $idchat = intval($_GET['idchat']);
} elseif (isset($_SESSION['idchat'])) {
    $idchat = $_SESSION['idchat'];
} else {
    // Try to get idchat from user's existing chats
    $stmt = $conn->prepare("SELECT idchat FROM chats WHERE email = ? LIMIT 1");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $chat = $result->fetch_assoc();
    if ($chat) {
        $idchat = $chat['idchat'];
    }
}

if (!$idchat) {
    echo json_encode(['success' => false, 'error' => 'No chat ID found']);
    exit;
}

// Check if user has access to this chat (unless admin)
if (!$is_admin) {
    $stmt = $conn->prepare("SELECT email FROM chats WHERE idchat = ? LIMIT 1");
    $stmt->bind_param("i", $idchat);
    $stmt->execute();
    $result = $stmt->get_result();
    $chat = $result->fetch_assoc();
    if ($chat && $chat['email'] !== $email) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
}

try {
    // Get all messages for this chat
    $stmt = $conn->prepare("SELECT id, idchat, email, message, is_admin, created_at, dibaca FROM chats WHERE idchat = ? ORDER BY created_at ASC");
    $stmt->bind_param("i", $idchat);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'idchat' => $row['idchat'],
            'email' => htmlspecialchars($row['email']),
            'message' => htmlspecialchars($row['message']),
            'is_admin' => (bool)$row['is_admin'],
            'created_at' => $row['created_at'],
            'dibaca' => (bool)$row['dibaca']
        ];
    }
    
    // Mark messages as read for current user (messages from others)
    $stmt = $conn->prepare("UPDATE chats SET dibaca = 1 WHERE idchat = ? AND email != ?");
    $stmt->bind_param("is", $idchat, $email);
    $stmt->execute();
    
    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'idchat' => $idchat,
        'count' => count($messages)
    ]);
    
} catch (Exception $e) {
    error_log("Get messages error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?> 