<?php
/**
 * QRIS Web Monitor
 * Version khusus untuk dijalankan via browser dengan time limit yang aman
 */

// Set time limit untuk web (maksimal 5 menit)
set_time_limit(300); // 5 menit = 300 detik
ini_set('max_execution_time', 300);

// Output buffering untuk real-time display
if (ob_get_level()) ob_end_clean();
ob_implicit_flush(true);

require_once 'qris_monitor.php';

// Konfigurasi web monitoring berdasarkan parameter URL
// (duration_options sekarang didefinisikan di config_qris.php)

$selected_duration = isset($_GET['duration']) ? $_GET['duration'] : $default_web_duration;
if (!isset($duration_options[$selected_duration])) {
    $selected_duration = $default_web_duration;
}

$max_runtime = $duration_options[$selected_duration]['runtime'];
$duration_name = $duration_options[$selected_duration]['name'];

// Hitung max iterations berdasarkan runtime
if ($max_runtime > 0) {
    $max_iterations = ceil($max_runtime / $check_interval);
} else {
    $max_iterations = 0; // Unlimited
}

// Set PHP time limit sesuai durasi yang dipilih
if ($max_runtime > 0) {
    set_time_limit($max_runtime + 60); // Tambah 60 detik buffer
    ini_set('max_execution_time', $max_runtime + 60);
} else {
    set_time_limit(0); // Unlimited
    ini_set('max_execution_time', 0);
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QRIS Monitor - Live Monitoring</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1e1e1e;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 100%;
            background: #2a2a2a;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0,255,0,0.3);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #00ff00;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .log {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        .btn {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #00cc00;
        }
        .status {
            margin-bottom: 15px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            text-align: center;
        }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .warning { color: #ffff44; }
    </style>
    <script>
        function scrollToBottom() {
            var log = document.getElementById('log');
            log.scrollTop = log.scrollHeight;
        }
        
        // Auto scroll setiap detik
        setInterval(scrollToBottom, 1000);
        
        // Auto refresh halaman jika diperlukan (disabled untuk continuous monitoring)
        // setTimeout function removed untuk continuous monitoring tanpa interupsi
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔴 LIVE - QRIS Payment Monitor</h1>
            <p>Monitoring akan berjalan selama: <strong><?php echo $duration_name; ?></strong></p>
        </div>
        
        <div class="status">
            <span class="success">🟢 MONITORING AKTIF</span> | 
            Check Interval: <?php echo $check_interval; ?> detik | 
            Duration: <?php echo $duration_name; ?>
            <?php if ($max_runtime > 0): ?>
            | Max Runtime: <?php echo $max_runtime; ?> detik
            <?php endif; ?>
        </div>
        
        <div class="log" id="log"><?php
        
echo "[" . date('Y-m-d H:i:s') . "] 🚀 Starting QRIS Web Monitor...\n";
echo "[" . date('Y-m-d H:i:s') . "] ⚙️ Configuration loaded\n";
echo "[" . date('Y-m-d H:i:s') . "] 🔄 Check interval: {$check_interval} seconds\n";
echo "[" . date('Y-m-d H:i:s') . "] ⏱️ Duration: {$duration_name}\n";
if ($max_runtime > 0) {
    echo "[" . date('Y-m-d H:i:s') . "] ⏱️ Max runtime: {$max_runtime} seconds\n";
    echo "[" . date('Y-m-d H:i:s') . "] 🔢 Max iterations: {$max_iterations}\n";
} else {
    echo "[" . date('Y-m-d H:i:s') . "] ♾️ Running unlimited (use with caution!)\n";
}
echo str_repeat("=", 80) . "\n\n";

// Flush output
flush();

$iteration = 0;
$start_time = time();

while (true) {
    $iteration++;
    $current_time = time();
    $running_time = $current_time - $start_time;
    
    // Cek time limit (hanya jika tidak unlimited)
    if ($max_runtime > 0 && $running_time >= $max_runtime) {
        echo "[" . date('Y-m-d H:i:s') . "] ⏰ Maximum runtime reached ({$max_runtime}s), stopping...\n";
        break;
    }
    
    // Cek iteration limit (hanya jika tidak unlimited)
    if ($max_iterations > 0 && $iteration > $max_iterations) {
        echo "[" . date('Y-m-d H:i:s') . "] 🔢 Maximum iterations reached ({$max_iterations}), stopping...\n";
        break;
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] --- Iteration #{$iteration} (Running: " . gmdate("H:i:s", $running_time) . ") ---\n";
    
    try {
        $result = monitorQRIS($discord_webhook_url, $cache_file);
        
        if ($result) {
            echo "[" . date('Y-m-d H:i:s') . "] ✅ Monitoring completed successfully\n";
        } else {
            echo "[" . date('Y-m-d H:i:s') . "] ❌ Monitoring failed\n";
        }
        
        // Memory management
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
    } catch (Exception $e) {
        echo "[" . date('Y-m-d H:i:s') . "] 💥 ERROR: " . $e->getMessage() . "\n";
        
        // Jika error, tunggu lebih lama
        echo "[" . date('Y-m-d H:i:s') . "] ⏳ Waiting extra 10 seconds due to error...\n";
        flush();
        sleep(10);
    }
    
    // Memory info
    $memory_mb = round(memory_get_usage(true) / 1024 / 1024, 2);
    echo "[" . date('Y-m-d H:i:s') . "] 💾 Memory usage: {$memory_mb} MB\n";
    
    // Estimasi waktu tersisa (hanya jika tidak unlimited)
    if ($max_runtime > 0) {
        $remaining_time = $max_runtime - $running_time;
        $remaining_iterations = $max_iterations - $iteration;
        echo "[" . date('Y-m-d H:i:s') . "] ⏱️ Time remaining: " . gmdate("H:i:s", $remaining_time) . " | Iterations remaining: {$remaining_iterations}\n";
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] ♾️ Running unlimited mode | Iteration: {$iteration}\n";
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] ⏳ Waiting {$check_interval} seconds before next check...\n";
    echo str_repeat("-", 80) . "\n";
    
    // Flush output untuk real-time display
    flush();
    
    sleep($check_interval);
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "[" . date('Y-m-d H:i:s') . "] 🏁 QRIS Web Monitor stopped\n";
echo "[" . date('Y-m-d H:i:s') . "] 📊 Total iterations: {$iteration}\n";
echo "[" . date('Y-m-d H:i:s') . "] ⏱️ Total runtime: " . gmdate("H:i:s", time() - $start_time) . "\n";
echo "[" . date('Y-m-d H:i:s') . "] 💾 Final memory usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB\n";

?></div>
        
        <div class="controls">
            <a href="?" class="btn">🔄 Restart Monitor</a>
            <a href="index.php" class="btn">🏠 Dashboard</a>
            <a href="qris_monitor.php" class="btn">▶️ Single Run</a>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <h4 style="color: #dcddde; margin-bottom: 10px;">⏱️ Pilih Durasi Monitoring:</h4>
            <div style="display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;">
                <?php foreach ($duration_options as $key => $option): ?>
                    <a href="?duration=<?php echo $key; ?>" 
                       class="btn <?php echo $selected_duration === $key ? 'active' : ''; ?>"
                       style="<?php echo $selected_duration === $key ? 'background: #ff6b6b; color: white;' : ''; ?>
                              <?php echo $key === 'unlimited' ? 'background: #dc3545; color: white;' : ''; ?>">
                        <?php echo $option['name']; ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #888; font-size: 12px;">
            <p>💡 Tips: Gunakan Command Line untuk monitoring unlimited: <code>php qris_continuous.php</code></p>
            <p>🔄 Monitoring akan berjalan terus sesuai durasi yang dipilih tanpa interupsi</p>
            <p>⚠️ Untuk monitoring jangka panjang, disarankan menggunakan Command Line</p>
        </div>
    </div>
</body>
</html> 