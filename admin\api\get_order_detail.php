<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

// Get order ID from query parameter
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Order ID is required']);
    exit;
}

$orderId = $_GET['id'];

try {
    // Get order details with product information
    $query = "
        SELECT 
            o.id,
            o.id_produk,
            o.email,
            o.nama_server,
            o.paket,
            o.status_bayar,
            o.pembayaran,
            o.bukti_pembayaran,
            o.jumlah_bayar,
            o.created_at,
            o.updated_at,
            p.nama as nama_produk,
            p.harga_normal as harga_produk
        FROM `order` o
        LEFT JOIN produk p ON o.id_produk = p.id_produk
        WHERE o.id = ?
    ";
    
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception('Prepare statement error: ' . $conn->error);
    }
    
    $stmt->bind_param("i", $orderId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Order not found']);
        exit;
    }
    
    $order = $result->fetch_assoc();
    
    // Extract filename from bukti_pembayaran path if it exists
    if ($order['bukti_pembayaran']) {
        $order['bukti_pembayaran'] = basename($order['bukti_pembayaran']);
    }
    
    echo json_encode($order);
    
} catch (Exception $e) {
    error_log('get_order_detail.php error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

// Clean output buffer
ob_end_flush();
?>