<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['group'] !== 'ADMIN') {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

include __DIR__ . '/../../database/database.php';

// Function to determine status based on expired date
function getStatusFromExpired($expired) {
    $now = new DateTime();
    $expiry = new DateTime($expired);
    
    if ($expiry < $now) {
        return 'Expired';
    } elseif ($now->diff($expiry)->days <= 7) {
        return 'Expiring Soon';
    } else {
        return 'Active';
    }
}

try {
    // Query untuk mengambil data server, kecuali yang namakota mengandung "GET DATA"
    $query = "SELECT id, email, typehosting, harganormal, hargabulanan, namakota, expired, 
             pakethosting, iphosting, catatan, token 
             FROM data_client 
             WHERE namakota NOT LIKE '%GET DATA%'
             ORDER BY expired ASC";
             
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $servers = [];
    while ($row = $result->fetch_assoc()) {
        // Format tanggal expired
        $expired = new DateTime($row['expired']);
        $now = new DateTime();
        
        // Status berdasarkan tanggal expired
        if ($expired < $now) {
            $status = "Expired";
            $statusClass = "danger";
        } else {
            $diff = $now->diff($expired);
            if ($diff->days <= 7) {
                $status = "Warning";
                $statusClass = "warning";
            } else {
                $status = "Active";
                $statusClass = "success";
            }
        }

        // Format data sesuai dengan yang diharapkan frontend
        $servers[] = [
            'id' => $row['id'],
            'pemilik' => $row['email'],
            'nama_server' => $row['namakota'],
            'type_game' => $row['typehosting'],
            'ip_server' => $row['iphosting'],
            'status' => $status,
            'statusClass' => $statusClass,
            'paket' => $row['pakethosting'],
            'harga_normal' => number_format($row['harganormal'], 0, ',', '.'),
            'harga_bulanan' => number_format($row['hargabulanan'], 0, ',', '.'),
            'expired' => $row['expired'],
            'catatan' => $row['catatan'],
            'token' => $row['token']
        ];
    }
    
    // Langsung return array of servers tanpa wrapper
    echo json_encode($servers);

} catch (Exception $e) {
    error_log("Error fetching servers: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Terjadi kesalahan saat mengambil data server']);
}

$conn->close();
?> 