<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

include __DIR__ . '/../../database/database.php';

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Check if ID is provided
if (!isset($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Server ID is required']);
    exit;
}

$serverId = intval($_GET['id']);

try {
    // Get server details from data_client table
    $stmt = $conn->prepare("SELECT * FROM data_client WHERE id = ?");
    $stmt->bind_param("i", $serverId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Server not found']);
        exit;
    }
    
    $server = $result->fetch_assoc();
    
    // Map data_client fields to expected admin fields
    $serverData = [
        'id' => $server['id'],
        'pemilik' => $server['email'], // Owner email
        'nama_server' => $server['namakota'], // Server name
        'type_game' => $server['typehosting'], // Type
        'ip_server' => $server['iphosting'] ?? '', // IP address
        'status' => '', // Status will be calculated based on expired
        'paket' => $server['pakethosting'], // Package
        'harga_normal' => $server['harganormal'],
        'harga_bulanan' => $server['hargabulanan'],
        'expired' => $server['expired'],
        'catatan' => $server['catatan'] ?? '',
        'token' => $server['token'] ?? ''
    ];
    
    // Calculate status based on expired date
    $now = new DateTime();
    $expiry = new DateTime($server['expired']);
    
    if ($expiry < $now) {
        $serverData['status'] = 'Expired';
    } elseif ($now->diff($expiry)->days <= 7) {
        $serverData['status'] = 'Expiring Soon';
    } else {
        $serverData['status'] = 'Active';
    }
    
    echo json_encode($serverData);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?> 