<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include '../database/database.php';
include '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}

// Ambil data dari session
$product_id = $_SESSION['product_id'] ?? null;
$server_name = $_SESSION['server_name'] ?? '';
$amount = $_SESSION['unique_amount'] ?? null;
$email = $_SESSION['email'] ?? '';

if (!$product_id || $server_name === '' || !$amount) {
    header('Location: products');
    exit;
}

// Check if there's already a paid or pending verification order for this product and server
try {
    $check_order_query = "SELECT status_bayar FROM `order` WHERE email = ? AND id_produk = ? AND nama_server = ? AND (status_bayar = 'Sudah Dibayar' OR status_bayar = 'Menunggu Verifikasi') ORDER BY created_at DESC LIMIT 1";
    $check_stmt = $conn->prepare($check_order_query);
    $check_stmt->bind_param("sss", $email, $product_id, $server_name);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        $existing_order = $check_result->fetch_assoc();
        $_SESSION['error'] = "Anda sudah memiliki order dengan status '{$existing_order['status_bayar']}' untuk server ini. Silakan cek riwayat order Anda.";
        header('Location: order_history');
        exit;
    }
} catch (Exception $e) {
    error_log("Payment validation error: " . $e->getMessage());
}

// Fetch product details
try {
    $query = "SELECT * FROM produk WHERE id_produk = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $product_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Location: products');
        exit;
    }
    
    $product = $result->fetch_assoc();
    // $amount sudah diambil dari session (hasil konfigurasi + kode unik)
} catch (Exception $e) {
    error_log("Payment error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat memuat data produk.";
    header('Location: products');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Payment - Dopminer</title>
    
    <!-- Favicon -->
    <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    
    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- Payment Script - Load early to prevent ReferenceError -->
    <script>
        let selectedPayment = null;
        
        // Define function globally immediately to prevent ReferenceError
        function selectPayment(method) {
            // Remove active class from all methods
            document.querySelectorAll('.payment-method').forEach(el => el.classList.remove('active'));
            document.querySelectorAll('.payment-details').forEach(el => el.classList.remove('active'));
            
            // Add active class to selected method
            document.querySelector(`#${method}`).parentElement.classList.add('active');
            document.querySelector(`#${method}-details`).classList.add('active');
            document.querySelector(`#${method}`).checked = true;
            
            selectedPayment = method;
            
            // Enable pay button
            document.getElementById('payButton').disabled = false;
        }
        
        // Make function available globally immediately
        window.selectPayment = selectPayment;
    </script>
    
    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background: #0a0e27;
            color: #ffffff;
            min-height: 100vh;
        }
        
        .payment-container {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        
        .payment-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }
        
        .payment-card h2 {
            color: #4fc3f7;
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .order-summary {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .payment-method {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.1);
        }
        
        .payment-method.active {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.2);
        }
        
        .payment-method input[type="radio"] {
            display: none;
        }
        
        .payment-details {
            display: none;
            background: rgba(79, 195, 247, 0.1);
            border: 1px solid rgba(79, 195, 247, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .payment-details.active {
            display: block;
        }
        
        .btn-pay {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
            width: 100%;
        }
        
        .btn-pay:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
        }
        
        .bank-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.05);
        }
        
        .upload-area.dragover {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.1);
        }
        
        @media (max-width: 768px) {
            .payment-container {
                padding: 100px 0 1rem 0;
            }
            
            .payment-card {
                padding: 1.5rem;
                margin: 0 1rem;
            }
            
            .payment-card h2 {
                font-size: 1.5rem;
            }
        }
        
        #header {
            transition: background-color 0.3s ease;
        }
        
        #header.scrolled {
            background-color: #171717c9;
        }
    </style>
</head>

<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="payment-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="payment-card">
                        <h2 class="text-center text-white mb-4">
                            <i class="fas fa-credit-card"></i> Pembayaran
                        </h2>
                        
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <h5 class="text-white mb-3">
                                <i class="fas fa-clipboard-list"></i> Ringkasan Pesanan
                            </h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="text-white mb-1"><strong><?php echo htmlspecialchars($product['nama']); ?></strong></p>
                                    <p class="text-light mb-1">Server: <?php echo htmlspecialchars($server_name); ?></p>
                                    <p class="text-light mb-1">Type: <?php echo htmlspecialchars($product['type']); ?></p>
                                    <?php if (isset($_SESSION['selected_paket'])): ?>
                                        <p class="text-light mb-0">
                                            <i class="fas fa-users"></i> Paket: 
                                            <span class="badge" style="background: #4fc3f7; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px;">
                                                <?php echo htmlspecialchars($_SESSION['selected_paket']); ?>
                                            </span>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4 text-end">
                                    <h4 class="text-primary mb-0">Rp <?php echo number_format($amount, 0, ',', '.'); ?></h4>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Methods -->
                        <h5 class="text-white mb-3">
                            <i class="fas fa-wallet"></i> Pilih Metode Pembayaran
                        </h5>
                        
                        <form id="paymentForm" enctype="multipart/form-data">
                            <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">
                            <input type="hidden" name="server_name" value="<?php echo htmlspecialchars($server_name); ?>">
                            <input type="hidden" name="amount" value="<?php echo $amount; ?>">
                            <?php if (isset($_SESSION['selected_paket'])): ?>
                                <input type="hidden" name="paket" value="<?php echo htmlspecialchars($_SESSION['selected_paket']); ?>">
                            <?php endif; ?>
                            
                            <!-- QRIS Payment -->
                            <div class="payment-method" onclick="selectPayment('qris')">
                                <input type="radio" name="payment_method" value="QRIS" id="qris">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-qrcode fa-2x text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-white mb-1">QRIS (Otomatis)</h6>
                                        <small class="text-light">Pembayaran otomatis menggunakan QRIS</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="qris-details" class="payment-details">
                                <div class="text-center">
                                    <p class="text-white mb-3">Scan QR Code di bawah ini untuk melakukan pembayaran:</p>
                                    <div id="qris-code" class="mb-3">
                                        <img src="../assets/img/QRIS.png" alt="QRIS Code" style="max-width: 300px; height: auto; border-radius: 10px;">
                                    </div>
                                    <div class="alert alert-info mt-3">
                                        <p class="mb-2"><strong>Total Pembayaran: Rp <?php echo number_format($amount, 0, ',', '.'); ?></strong></p>
                                        <small>Pastikan nominal transfer sesuai dengan jumlah di atas (termasuk kode unik)</small>
                                    </div>
                                    <p class="text-warning">
                                        <i class="fas fa-clock"></i> Pembayaran akan diverifikasi otomatis
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Manual Payment -->
                            <div class="payment-method" onclick="selectPayment('manual')">
                                <input type="radio" name="payment_method" value="MANUAL PAYMENT" id="manual">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-university fa-2x text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-white mb-1">Manual Payment</h6>
                                        <small class="text-light">Transfer manual ke rekening kami</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="manual-details" class="payment-details">
                                <h6 class="text-white mb-3">Pilih Rekening Tujuan:</h6>
                                
                                <div class="bank-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-white">BCA</strong><br>
                                            <span class="text-light">**********</span><br>
                                            <small class="text-light">A/N Muhammad Alfian</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-light" onclick="copyToClipboard('**********')">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="bank-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-white">DANA</strong><br>
                                            <span class="text-light">************</span><br>
                                            <small class="text-light">A/N Muhammad Alfian</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-light" onclick="copyToClipboard('************')">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="bank-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-white">GOPAY</strong><br>
                                            <span class="text-light">************</span><br>
                                            <small class="text-light">A/N Muhammad Alfian</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-light" onclick="copyToClipboard('************')">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h6 class="text-white mb-3">Upload Bukti Pembayaran:</h6>
                                    <div class="upload-area" id="uploadArea">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                        <p class="text-white mb-2">Klik atau drag & drop file gambar</p>
                                        <small class="text-light">Format: JPG, PNG, PDF (Max: 5MB)</small>
                                        <input type="file" id="proofFile" name="proof_file" accept="image/*,.pdf" style="display: none;">
                                    </div>
                                    <div id="filePreview" class="mt-3"></div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-pay" id="payButton" disabled>
                                    <i class="fas fa-lock"></i> Proses Pembayaran
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Ensure DOM is ready before setting up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Set up event delegation as backup for payment method selection
            document.addEventListener('click', function(e) {
                if (e.target.closest('.payment-method')) {
                    const paymentMethod = e.target.closest('.payment-method');
                    const radioInput = paymentMethod.querySelector('input[type="radio"]');
                     if (radioInput) {
                         const method = radioInput.id; // Use the id directly (qris or manual)
                         if (method === 'qris' || method === 'manual') {
                             selectPayment(method);
                         }
                     }
                }
            });
        });
        
        // QR Code is now static image, no need to generate
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Nomor rekening telah disalin ke clipboard',
                    icon: 'success',
                    background: 'rgba(13, 18, 26, 0.95)',
                    color: '#fff',
                    timer: 2000,
                    showConfirmButton: false
                });
            });
        }
        
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('proofFile');
        const filePreview = document.getElementById('filePreview');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });
        
        function handleFile(file) {
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    title: 'Error!',
                    text: 'File terlalu besar. Maksimal 5MB.',
                    icon: 'error',
                    background: 'rgba(13, 18, 26, 0.95)',
                    color: '#fff'
                });
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                filePreview.innerHTML = `
                    <div class="text-center">
                        <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 10px;">
                        <p class="text-white mt-2">${file.name}</p>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
        
        // Form submission
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!selectedPayment) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Pilih metode pembayaran terlebih dahulu.',
                    icon: 'error',
                    background: 'rgba(13, 18, 26, 0.95)',
                    color: '#fff'
                });
                return;
            }
            
            if (selectedPayment === 'manual' && !fileInput.files[0]) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Upload bukti pembayaran terlebih dahulu.',
                    icon: 'error',
                    background: 'rgba(13, 18, 26, 0.95)',
                    color: '#fff'
                });
                return;
            }
            
            // Create form data
            const formData = new FormData(this);
            
            // Submit to API
            fetch('api/process_order.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (selectedPayment === 'qris') {
                        // Redirect to payment monitoring page
                        window.location.href = `payment_monitor?order_id=${data.order_id}`;
                    } else {
                        // Show success message for manual payment
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Pesanan Anda telah dibuat. Menunggu verifikasi pembayaran.',
                            icon: 'success',
                            background: 'rgba(13, 18, 26, 0.95)',
                            color: '#fff'
                        }).then(() => {
                            window.location.href = 'order_history';
                        });
                    }
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message || 'Terjadi kesalahan saat memproses pesanan.',
                        icon: 'error',
                        background: 'rgba(13, 18, 26, 0.95)',
                        color: '#fff'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memproses pesanan.',
                    icon: 'error',
                    background: 'rgba(13, 18, 26, 0.95)',
                    color: '#fff'
                });
            });
        });
    </script>
    
    <!-- Navbar Scroll Effect -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('header');
            
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });
    </script>
</body>
</html>