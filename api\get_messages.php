<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Turn off error reporting for clean JSON output
error_reporting(0);
ini_set('display_errors', 0);

// Clean any output that might have been sent
if (ob_get_level()) {
    ob_clean();
}

include '../database/database.php';

// Check database connection
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit;
}

if (!isset($_GET['idchat'])) {
    echo json_encode(['success' => false, 'error' => 'ID Chat not provided']);
    exit;
}

$idchat = intval($_GET['idchat']);
$email = $_SESSION['email'];

// Check if user is admin
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$is_admin = ($user['group'] === 'ADMIN');

// Check if user has access to this chat
if (!$is_admin) {
    $stmt = $conn->prepare("SELECT email FROM chats WHERE idchat = ? LIMIT 1");
    $stmt->bind_param("i", $idchat);
    $stmt->execute();
    $result = $stmt->get_result();
    $chat = $result->fetch_assoc();
    
    if ($chat && $chat['email'] !== $email) {
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
}

// Get messages
$stmt = $conn->prepare("SELECT * FROM chats WHERE idchat = ? ORDER BY created_at ASC");
if (!$stmt) {
    echo json_encode(['success' => false, 'error' => 'Prepare statement failed: ' . $conn->error]);
    exit;
}

$stmt->bind_param("i", $idchat);
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'error' => 'Execute failed: ' . $stmt->error]);
    exit;
}

$result = $stmt->get_result();
if (!$result) {
    echo json_encode(['success' => false, 'error' => 'Get result failed: ' . $stmt->error]);
    exit;
}

$messages = [];
while ($row = $result->fetch_assoc()) {
    $messages[] = [
        'id' => $row['id'] ?? '',
        'idchat' => $row['idchat'] ?? '',
        'email' => $row['email'] ?? '',
        'message' => $row['message'] ?? '', // Keep consistent field name
        'created_at' => $row['created_at'] ?? '', // Keep consistent field name
        'is_admin' => $row['is_admin'] ?? 0,
        'dibaca' => $row['dibaca'] ?? 0
    ];
}

echo json_encode(['success' => true, 'messages' => $messages]);
?> 