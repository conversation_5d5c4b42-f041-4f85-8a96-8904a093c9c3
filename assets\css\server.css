       /* Server Configuration */

       .per-month {

           font-size: 1.1rem;

           color: #4da6ff;

           margin-left: -4px;

       }



       .server-container {

           max-width: 1200px;

           margin: 0 auto;

           padding: 2rem;

           background-color: rgba(0, 0, 0, 0);

           border-radius: 10px;

       }



       .config-section {

           display: flex;

           flex-wrap: nowrap;

           gap: 2rem;

           margin-bottom: 3rem;

           align-items: flex-start;

       }



       .config-left {

           width: 60%;

           display: flex;

           flex-direction: column;

           gap: 1.5rem;

       }



       .config-right {

           width: 40%;

       }



       .config-subtitle {

           font-size: 22px;

           font-weight: 500;

           color: #0382bd;

       }



       .slider-container p {

           margin-bottom: 0.8rem;

       }



       .slider {

           width: 100%;

           margin-bottom: 0.5rem;

           height: 10px;

           border-radius: 5px;

           background: #2a3146;

           outline: none;

           -webkit-appearance: none;

           appearance: none;

       }



       .slider-labels {

           display: flex;

           justify-content: space-between;

           font-size: 0.9rem;

           color: #aaa;

           margin-top: 0.5rem;

       }



       .plan-description {

           background-color: #2c2c2cc2;

           padding: 1.5rem 1.1rem;

           border-radius: 8px;

           color: #ddd;

           font-size: 0.95rem;

           line-height: 1.5;

           /* margin-bottom: 20px; */

           /* border-top: 6px solid #006fa3; */

       }



       .features-table {

           background: linear-gradient(145deg, #1e1e1e, #2b2b2b);

           padding: 1.5rem;

           border-radius: 15px;

           color: #ddd;

           width: 100%;

           overflow-x: auto;

           box-shadow: 0 0 15px rgba(0, 0, 0, 0.4);

           border: 1px solid #3a3a3a;

       }



       .features-table .modern-table {

           width: 100%;

           border-collapse: collapse;

           font-family: 'Segoe UI', sans-serif;

       }



       .features-table .modern-table th {

           background-color: #006fa3;

           color: white;

           text-align: left;

           padding: 0.9rem;

           font-size: 1rem;

           /* border-radius: 8px  0 8px; */

           border-bottom: 2px solid #007bbf;

       }



       .features-table .modern-table td {

           background-color: rgba(255, 255, 255, 0.03);

           padding: 0.9rem;

           border-bottom: 1px solid #3a3a3a;

           font-size: 0.95rem;

       }



       .features-table .modern-table tbody tr:hover {

           background-color: rgba(77, 166, 255, 0.1);

           transition: 0.2s ease-in-out;

       }



       .features-table i {

           margin-right: 8px;

           color: #4da6ff;

       }





       .selected-plan {

           background-color: #2b2a2ab8;

           padding: 1.5rem;

           border-radius: 10px;

           border-top: 6px solid #006fa3;

           position: relative;

       }



       .plan-name {

           font-size: 1.5rem;

           font-weight: bold;

           color: #4da6ff;

       }



       .plan-original-price {

           text-decoration: line-through;

           color: #aaa;

           font-weight: bold;

       }



       .plan-price {

           color: #4da6ff;

           font-size: 2rem;

           margin-top: -11px;

           font-weight: bold;

       }



       .plan-discount {

           color: #ff4d4d;

           font-size: 12px;

           font-weight: bold;

           margin-bottom: 1500px;

       }



       .plan-available {

           color: #6eff8d;

           display: flex;

           align-items: center;

           margin: 10px 0;

       }



       .available-dot {

           width: 10px;

           height: 10px;

           background-color: #6eff8d;

           border-radius: 50%;

           margin-right: 6px;

       }



       .specs-list {

           margin: 1rem 0;

       }



       .spec-item {

           display: flex;

           align-items: center;

           margin-bottom: 0.6rem;

       }



       .spec-icon {

           margin-right: 0.5rem;

           color: #ccc;

       }



       .location img {

           margin-right: 0.5rem;

       }



       .select-plan-btn {

           display: block;

           text-align: center;

           background-color: #0649680a;

           color: white;

           padding: 0.8rem;

           border: 3px solid #006fa3;

           border-radius: 20px;

           font-size: 1.1rem;

           font-weight: 600;

           cursor: pointer;

           text-decoration: none;

           transition: background-color 0.3s;

       }



       .select-plan-btn:hover {

           background: #3d86cc;

           color: #fff;

       }

       .plan-decoration-right {

           position: absolute;

           right: 40px;

           top: 190px;

           transform: translateY(-50%);

           width: 130px;

           opacity: 0.95;

           filter: drop-shadow(0 0 10px rgba(0, 111, 163, 0.6));

           animation: float 3s ease-in-out infinite;

           pointer-events: none;

       }





       /* Animasi mengambang */

       @keyframes float {

           0% {
               transform: translateY(0px);
           }

           50% {
               transform: translateY(-12px);
           }

           100% {
               transform: translateY(0px);
           }

       }





       @media (max-width: 768px) {

           .config-section {

               flex-direction: column;

           }

           .config-left,

           .config-right {

               width: 100%;

           }

       }



       @media (max-width: 1024px) {

           .navbar {

               padding: 1rem 3%;

           }



           .game-header {

               padding: 2.5rem 0 1.5rem;

           }



           .game-title {

               font-size: 2.2rem;

           }



           .config-title {

               font-size: 1.8rem;

           }



           .plan-price {

               font-size: 2.2rem;

               font-weight: bold;

           }



       }



       @media (max-width: 768px) {





           .close-menu {

               display: block;
               /* Only display on mobile */

           }



           .game-header {

               padding: 2rem 0 1rem;

           }



           .game-logo {

               width: 70px;

               height: 70px;

           }



           .game-title {

               font-size: 1.8rem;

           }



           .game-subtitle {

               font-size: 1rem;

           }



           .server-container {

               padding: 1rem;

           }



           .config-title {

               font-size: 1.6rem;

           }



           .config-subtitle {

               font-size: 1rem;

           }



           .slider-labels {

               font-size: 0.8rem;

           }



           .plan-name {

               font-size: 1.8rem;

           }



           .plan-price {

               font-size: 2.2rem;

               font-weight: bold;

           }

       }



       @media (max-width: 480px) {

           .logo {

               font-size: 1.2rem;

           }



           .game-logo {

               width: 60px;

               height: 60px;

           }



           .game-title {

               font-size: 1.6rem;

           }



           .config-title {

               font-size: 1.4rem;

           }



           .config-right {

               padding: 1rem;

           }



           .slider-labels {

               font-size: 0.7rem;

           }



           .plan-name {

               font-size: 1.5rem;

           }



           .plan-price {

               font-size: 1.8rem;

               margin-bottom: 1rem;

               font-weight: bold;

           }



           .spec-item {

               font-size: 0.9rem;

           }



           .select-plan-btn {

               padding: 0.7rem;

               font-size: 1rem;

           }

       }



       .img-fluid {
           transition: opacity 0.5s ease-in-out;

       }







       .fade-image {



           opacity: 0;



       }



       .contain {



           margin-top: 120px;



       }







       h2 {



           color: #fff;



       }







       p {



           color: #d3d3d3;



           margin-bottom: 5px;



       }







       .feature-list {



           list-style: none;



           padding: 0;



       }







       .nav-item {



           font-size: 18px;



           margin: 10px 0;



           color: #fff;



           display: flex;



           align-items: center;



           background-color: #2b2d31;



           padding: 15px 20px;



           padding-left: 30px;



           border-radius: 10px;



           cursor: pointer;



           transition: all 0.3s ease;



           position: relative;



           margin-bottom: 15px;



           height: 70px;



           border-right: 5px solid transparent;



       }







       .nav-item .icon {



           margin-right: 15px;



           font-size: 24px;



       }







       .nav-item.active {



           border-right: 5px solid #007bff;



       }







       .nav-item:hover {



           background-color: #373a40;



       }







       .img-fluid {
           margin-top: 40px;
           margin-left: 30px;
           max-width: 100%;
           height: auto;
           object-fit: contain;
           border: 5px solid #373a40;
           border-radius: 20px;
       }

       .nav-item.active {



           background-color: #373a40;



       }



       .custom-section {



           color: white;



           padding: 3rem 0;



           position: relative;



           overflow: hidden;



           /* Menghindari scroll horizontal */



           background-color: transparent;



           /* Membuat latar belakang transparan */



       }







       .feature-container {



           display: flex;



           flex-wrap: wrap;



           justify-content: center;



           gap: 15px;



           /* Jarak antar elemen */



           margin-bottom: 20px;



           /* Jarak bawah kontainer */



       }







       .feature-pill {



           background-color: rgba(52, 73, 94, 0.9);



           /* Warna fitur tetap solid */



           border-radius: 20px;



           padding: 10px 20px;



           color: white;



           font-size: 16px;



           font-weight: bold;



           transition: background-color 0.3s ease;



       }







       .feature-pill:hover {



           background-color: rgba(52, 152, 219, 0.9);



           /* Warna saat di-hover */



       }







       .character {



           height: 350px;



           /* Ukuran gambar yang lebih besar */



           position: absolute;



           bottom: 0;



           /* Posisi vertikal di bagian bawah */



       }







       .left-character {



           left: 20px;



           /* Jarak dari kiri */



       }







       .right-character {



           right: 20px;



           /* Jarak dari kanan */



           transform: scaleX(-1);



           /* Membalikkan gambar */



       }