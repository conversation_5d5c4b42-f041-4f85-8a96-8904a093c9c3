/**

* Template Name: Arsha

* Updated: Jan 29 2024 with Bootstrap v5.3.2

* Template URL: https://bootstrapmade.com/arsha-free-bootstrap-html-template-corporate/

* Author: BootstrapMade.com

* License: https://bootstrapmade.com/license/

*/



/*--------------------------------------------------------------

# General

--------------------------------------------------------------*/

body {

  font-family: "Inter", "Open Sans", sans-serif;

  color: #ffffff;

}



a {

  color: #47b2e4;

  text-decoration: none;

}



a:hover {

  color: #73c5eb;

  text-decoration: none;

}



h1,

h2,

h3,

h4,

h5,

h6 {

  font-family: "Jost", sans-serif;

}



/*--------------------------------------------------------------

# Preloader

--------------------------------------------------------------*/

#preloader {

  position: fixed;

  top: 0;

  left: 0;

  right: 0;

  bottom: 0;

  z-index: 9999;

  overflow: hidden;

  background: #121212;

}



#preloader:before {

  content: "";

  position: fixed;

  top: calc(50% - 30px);

  left: calc(50% - 30px);

  border: 6px solid #121212;

  border-top-color: #fff;

  border-bottom-color: #fff;

  border-radius: 50%;

  width: 60px;

  height: 60px;

  animation: animate-preloader 1s linear infinite;

}



@keyframes animate-preloader {

  0% {

    transform: rotate(0deg);

  }



  100% {

    transform: rotate(360deg);

  }

}



/*--------------------------------------------------------------

# Back to top button

--------------------------------------------------------------*/

.back-to-top {

  position: fixed;

  visibility: hidden;

  opacity: 0;

  right: 15px;

  bottom: 15px;

  z-index: 996;

  background: #08a0ff13;

  width: 40px;

  height: 40px;

  border-radius: 50px;

  border: 1px solid rgba(0, 111, 163, 0.548);

  transition: all 0.4s;

}



.back-to-top i {

  font-size: 24px;

  color: #fff;

  line-height: 0;

}



.back-to-top:hover {

  background: #6bc1e9;

  color: #fff;

}



.back-to-top.active {

  visibility: visible;

  opacity: 1;

}



/*--------------------------------------------------------------

# Header

--------------------------------------------------------------*/

#header {

  transition: all 0.5s;

  z-index: 997;

  padding: 15px 0;

}



#header.header-scrolled,

#header.header-inner-pages {

  background: #121212e8;

}



#header .logo {

  font-size: 30px;

  margin: 0;

  padding: 0;

  line-height: 1;

  font-weight: 500;

  letter-spacing: 2px;

  text-transform: uppercase;

}



#header .logo a {

  color: #fff;

}



#header .logo img {

  max-height: 40px;

}



/*--------------------------------------------------------------

# Navigation Menu

--------------------------------------------------------------*/

/**

* Desktop Navigation 

*/

.navbar {

  padding: 0;

  font-family: "Inter", "Open Sans", sans-serif;

  transition: background-color 0.3s ease, backdrop-filter 0.3s ease;

  background-color: transparent;

  backdrop-filter: blur(0px);

}



.navbar ul {

  margin: 0;

  padding: 0;

  display: flex;

  list-style: none;

  align-items: center;

}



.navbar li {

  position: relative;

}



.navbar a,

.navbar a:focus {

  display: flex;

  align-items: center;

  justify-content: space-between;

  padding: 14px 0 10px 30px;

  font-size: 15px;

  font-weight: 500;

  color: #fff;

  white-space: nowrap;

  transition: 0.3s;

}



.navbar a i,

.navbar a:focus i {

  font-size: 12px;

  line-height: 0;

  margin-left: 5px;

}



.navbar a:hover,

.navbar .active,

.navbar .active:focus,

.navbar li:hover>a {

  color: #47b2e4;

}



.navbar .getstarted,

.navbar .getstarted:focus {

  padding: 8px 20px;

  margin-left: 30px;

  border-radius: 50px;

  color: #fff;

  font-size: 14px;

  border: 2px solid #47b2e4;

  font-weight: 600;

}



.navbar .getstarted:hover,

.navbar .getstarted:focus:hover {

  color: #fff;

  background: #31a9e1;

}



.navbar .dropdown ul {

  display: block;

  position: absolute;

  left: 14px;

  top: calc(100% + 30px);

  margin: 0;

  padding: 10px 0;

  z-index: 99;

  opacity: 0;

  visibility: hidden;

  background: #272626ba;

  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.25);

  transition: 0.3s;

  border-radius: 14px;

}



.navbar .dropdown ul li {

  min-width: 200px;

}



.navbar .dropdown ul a {

  padding: 3px 3px;

  font-size: 14px;

  text-transform: none;

  font-weight: 500;

  color: #fff;

}



.navbar .dropdown ul a i {

  font-size: 12px;

}

.navbar .dropdown li a i {

  font-size: 18px;

  display: inline-block;

  background: #47b2e4;

  color: #fff;

  line-height: 1;

  padding: 7px 0;

  margin-right: 4px;

  border-radius: 50%;

  text-align: center;

  width: 36px;

  height: 36px;

  transition: 0.3s;

  border-top-width: 1px;

}



.navbar .dropdown ul a:hover,

.navbar .dropdown ul .active:hover,

.navbar .dropdown ul li:hover>a {

  color: #47b2e4;

}



.navbar .dropdown:hover>ul {

  opacity: 1;

  top: 100%;

  visibility: visible;

}



.navbar .dropdown .dropdown ul {

  top: 0;

  left: calc(100% - 30px);

  visibility: hidden;

}



.navbar .dropdown .dropdown:hover>ul {

  opacity: 1;

  top: 0;

  left: 100%;

  visibility: visible;

}



@media (max-width: 1366px) {

  .navbar .dropdown .dropdown ul {

    left: -90%;

  }



  .navbar .dropdown .dropdown:hover>ul {

    left: -100%;

  }

}



/**

* Mobile Navigation 

*/

.mobile-nav-toggle {

  color: #fff;

  font-size: 28px;

  cursor: pointer;

  display: none;

  line-height: 0;

  transition: 0.5s;

}



.mobile-nav-toggle.bi-x {

  color: #fff;

}



@media (max-width: 991px) {

  .mobile-nav-toggle {

    display: block;

  }



  .navbar ul {

    display: none;

  }

}



.navbar-mobile {

  position: fixed;

  overflow: hidden;

  top: 0;

  right: 0;

  left: 0;

  bottom: 0;

  background: rgba(40, 58, 90, 0.9);

  transition: 0.3s;

  z-index: 999;

  font-family: "Inter", "Open Sans", sans-serif;

}





.navbar-mobile .mobile-nav-toggle {

  position: absolute;

  top: 15px;

  right: 15px;

}



.navbar-mobile ul {

  display: block;

  position: absolute;

  top: 55px;

  right: 15px;

  bottom: 15px;

  left: 15px;

  padding: 10px 0;

  border-radius: 10px;

  background-color: #272626;

  overflow-y: auto;

  transition: 0.3s;

}



.navbar-mobile a,

.navbar-mobile a:focus {

  padding: 10px 20px;

  font-size: 15px;

  color: #fff;

}



.navbar-mobile a:hover,

.navbar-mobile .active,

.navbar-mobile li:hover>a {

  color: #47b2e4;

}



.navbar-mobile .getstarted,

.navbar-mobile .getstarted:focus {

  margin: 15px;

  color: #37517e;

}



.navbar-mobile .dropdown ul {

  position: static;

  display: none;

  margin: 10px 20px;

  padding: 10px 0;

  z-index: 99;

  opacity: 1;

  visibility: visible;

  background: #272626;

  box-shadow: 0px 0px 30px rgba(76 76 76 / 25%);

}



.navbar-mobile .dropdown ul li {

  min-width: 200px;

}



.navbar-mobile .dropdown ul a {

  padding: 10px 20px;

}



.navbar-mobile .dropdown ul a i {

  font-size: 12px;

}



.navbar-mobile .dropdown ul a:hover,

.navbar-mobile .dropdown ul .active:hover,

.navbar-mobile .dropdown ul li:hover>a {

  color: #47b2e4;

}



.navbar-mobile .dropdown>.dropdown-active {

  display: block;

  visibility: visible !important;

}



/*--------------------------------------------------------------

# Hero Section

--------------------------------------------------------------*/

#hero {

  /*  width: 100%;*/

  /* height: 85vh; */

  /*background-color: #1a243c;*/

  /*  background: linear-gradient(to right, #000000bf 100%, transparent 0%), url("https://dopminer.com/Gambar/1410524073-gta-v-1968268344.jpg");*/

  background-size: cover;

}



#fivem {

  width: 100%;

  /* height: 190vh; */

  background: linear-gradient(to right, #121212cf 100%, transparent 0%), url(https://dopminer.com/Gambar/652707-2830837463.jpg) no-repeat center center;

  background-size: cover;

}

#vps {

  width: 100%;

  /* height: 190vh; */

  background: linear-gradient(to right, #000000c9 100%, transparent 0%), url("https://dopminer.com/Gambar/iu.png");

  background-size: cover;

}

#samp {

  width: 100%;

  /* height: 190vh; */

  background: linear-gradient(to right, #121212cf 100%, transparent 0%), url(https://dopminer.com/Gambar/Screenshot_16.png) no-repeat center center;

  background-size: cover;

}
#redm {

  width: 100%;

  /* height: 190vh; */

  background: linear-gradient(to right, #121212cf 100%, transparent 0%), url(https://dopminer.com/Gambar/01a018f69607acd220fc9bf35b646e2ec47a202f.jpg) no-repeat center center;

  background-size: cover;

}



#games {

  width: 100%;

  /* height: 100vh; */

  background: linear-gradient(to right, #000000d1 100%, transparent 0%), url("https://dopminer.com/Gambar/thumb-1920-597933-942613699.jpg");

  background-size: cover;

}



#app {

  width: 100%;

  /* height: 100vh; */

  background: linear-gradient(to right, #000000c9 100%, transparent 0%), url("https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg");

  background-size: cover;

}

#portfolio {

  width: 100%;

  /* height: 120vh; */

  background-color: #1a243c;

  background-size: cover;

}

#showcase {

  width: 100%;

  /* height: 195vh; */

  background: linear-gradient(to right, #000000a9 100%, transparent 0%), url("https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg");

  background-size: cover;

}

#showcase-details {

  width: 100%;

  /* height: 120vh; */

  background: linear-gradient(to right, #080808e3 100%, transparent 0%), url("https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg");

  background-size: cover;

}





#hero .container {

  padding-top: 72px;

}



#hero h1 {

  margin: 30px 0 10px 0;

  font-size: 38px;

  font-weight: 700;

  line-height: 56px;

  color: #fff;

}



#hero h2 {

  color: rgb(255, 255, 255);

  margin-bottom: 30px;

  font-size: 24px;

}



#hero .btn-get-started {

  font-family: "Poppins", sans-serif;

  font-weight: 500;

  font-size: 16px;

  letter-spacing: 1px;

  display: inline-block;

  padding: 10px 28px;

  border-radius: 50px;

  transition: 0.5s;

  margin-top: 5px;

  color: #fff;
  background: #08a0ff13;

  border: 2px solid rgba(0, 111, 163, 0.548);

}



#hero .btn-get-started:hover {

  background: #209ed859;

  border: 2px solid #25b1f1;
}



#hero .btn-watch-video {

  font-size: 16px;

  display: flex;

  align-items: center;

  transition: 0.5s;

  margin: 10px 0 0 25px;

  color: #fff;

  line-height: 1;

}



#hero .btn-watch-video i {

  line-height: 0;

  color: #fff;

  font-size: 32px;

  transition: 0.3s;

  margin-right: 8px;

}



#hero .btn-watch-video:hover i {

  color: #47b2e4;

}



#hero .animated {

  animation: up-down 2s ease-in-out infinite alternate-reverse both;

}





@media (max-width: 991px) {

  #hero {

    height: 100vh;

    text-align: center;

  }



  #hero .animated {

    animation: none;

  }



  #hero .hero-img {

    margin-left: auto;

    margin-right: auto;

  }

  #hero0 .hero-img {

    margin-left: auto;

    margin-right: auto;

  }



  #hero .hero-img img {

    width: 50%;

  }

}

@media (max-width: 768px) {

  #hero h1 {

    font-size: 28px;

    line-height: 36px;

  }



  #hero h2 {

    font-size: 18px;

    line-height: 24px;

    margin-bottom: 30px;

  }



  #hero .hero-img img {

    width: 70%;

  }

}



@media (max-width: 575px) {

  #hero .hero-img img {

    width: 80%;

  }



  #hero .btn-get-started {

    font-size: 16px;

    padding: 10px 24px 11px 24px;

  }

}



@keyframes up-down {

  0% {

    transform: translateY(10px);

  }



  100% {

    transform: translateY(-10px);

  }

}



/*--------------------------------------------------------------

# Sections General

--------------------------------------------------------------*/

section {

  padding: 60px 0;

  overflow: hidden;

  background-color: #121212;

}



.section {

  padding: 60px 0;

  overflow: hidden;

  background-color: #121212;

}



.section-bg {

  background-color: #121212;

}



.section-title {

  text-align: center;

  padding-bottom: 10px;

}

.section-titles {

  text-align: left;

  padding-bottom: 10px;

}



.section-title h2 {

  font-size: 30px;

  font-weight: bold;

  margin-bottom: 30px;

  margin-top: 120px;

  position: relative;

  color: #ffffff;

  font-family: "Jost", sans-serif;

}



.section-title h2::before {

  content: "";

  position: absolute;

  display: block;

  width: 120px;

  height: 1px;

  background: #ddd;

  bottom: -10px;

  left: calc(50% - 60px);

}



.section-title h2::after {

  content: "";

  position: absolute;

  display: block;

  width: 40px;

  height: 3px;

  background: #47b2e4;

  bottom: -9px;

  left: calc(50% - 20px);

}



.section-title p {

  margin-bottom: 20px;

  font-size: 20px;

  font-family: "Jost", sans-serif;

}

/*--------------------------------------------------------------

# Counts

--------------------------------------------------------------*/

.counts {

  background: linear-gradient(to right, #37517e34 100%, transparent 0%), url(https://dopminer.com/Gambar/csbg.png);

  padding: 80px 0 60px 0;

  background-size: cover;

}



.counts .counters span {

  font-size: 48px;

  display: block;

  color: #fff;

  font-weight: 600;

  font-family: "Poppins", sans-serif;

  margin-left: 50px;

}



.counts .counters .count-box {

  padding: 20px 0;

  width: 100%;

}



.counts .counters .count-box i {

  display: block;

  font-size: 39px;

  color: #3498db;

  float: left;

  line-height: 2;



}



.counts .counters p {

  padding: 10px;

  margin: 0 0 20px 0;

  font-size: 15px;

  color: rgba(255, 255, 255, 0.856);

}



/*--------------------------------------------------------------

# clientss

--------------------------------------------------------------*/

.clientss {

  padding: 12px 0;

  text-align: center;

}



.clientss img {

  max-width: 45%;

  transition: all 0.4s ease-in-out;

  display: inline-block;

  padding: 15px 0;

  filter: grayscale(100);

}



.clientss img:hover {

  filter: none;

  transform: scale(1.1);

}



@media (max-width: 768px) {

  .clientss img {

    max-width: 40%;



  }

}



/*--------------------------------------------------------------

# About Us

--------------------------------------------------------------*/

.about .content h3 {

  font-weight: 600;

  font-size: 26px;

}



.about .content ul {

  list-style: none;

  padding: 0;

}



.about .content ul li {

  padding-left: 28px;

  position: relative;

}



.about .content ul li+li {

  margin-top: 10px;

}



.about .content ul i {

  position: absolute;

  left: 0;

  top: 2px;

  font-size: 20px;

  color: #47b2e4;

  line-height: 1;

}



.about .content p:last-child {

  margin-bottom: 0;

}



.about .content .btn-learn-more {

  font-family: "Poppins", sans-serif;

  font-weight: 500;

  font-size: 14px;

  letter-spacing: 1px;

  display: inline-block;

  padding: 12px 32px;

  border-radius: 4px;

  transition: 0.3s;

  line-height: 1;

  color: #47b2e4;

  animation-delay: 0.8s;

  margin-top: 6px;

  border: 2px solid #47b2e4;

}



.about .content .btn-learn-more:hover {

  background: #47b2e4;

  color: #fff;

  text-decoration: none;

}



/*--------------------------------------------------------------

# Why Us

--------------------------------------------------------------*/

.why-us .content {

  padding: 10px 100px 0 100px;

}



.why-us .content h3 {

  font-weight: 400;

  font-size: 34px;

  color: #ffffff;

}



.why-us .content h4 {

  font-size: 20px;

  font-weight: 700;

  margin-top: 5px;

}



.why-us .content p {

  font-size: 15px;

  color: #ffffff99;

}

.why-us .content li {

  font-size: 15px;

  color: #ffffff99;

  margin-bottom: 20px;

}



.why-us .img {

  background-size: contain;

  background-repeat: no-repeat;

  background-position: center center;

}



.why-us .accordion-list {

  padding: 0 100px 60px 100px;

}



.why-us .accordion-list ul {

  padding: 0;

  list-style: none;

}



.why-us .accordion-list li+li {

  margin-top: 15px;

}



.why-us .accordion-list li {

  padding: 20px;

  background: #1d263660;

  border-radius: 12px;

}



.why-us .accordion-list a {

  display: block;

  position: relative;

  font-family: "Poppins", sans-serif;

  font-size: 16px;

  line-height: 24px;

  font-weight: 500;

  padding-right: 30px;

  outline: none;

  cursor: pointer;

}



.why-us .accordion-list span {

  color: #47b2e4;

  font-weight: 600;

  font-size: 18px;

  padding-right: 10px;

}



.why-us .accordion-list i {

  font-size: 24px;

  position: absolute;

  right: 0;

  top: 0;

}



.why-us .accordion-list p {

  margin-bottom: 0;

  padding: 10px 0 0 0;

  color: #fff;

}



.why-us .accordion-list .icon-show {

  display: none;

}



.why-us .accordion-list a.collapsed {

  color: #ffffff;

}



.why-us .accordion-list a.collapsed:hover {

  color: #47b2e4;

}



.why-us .accordion-list a.collapsed .icon-show {

  display: inline-block;

}



.why-us .accordion-list a.collapsed .icon-close {

  display: none;

}



@media (max-width: 1024px) {



  .why-us .content,

  .why-us .accordion-list {

    padding-left: 0;

    padding-right: 0;

  }

}



@media (max-width: 992px) {

  .why-us .img {

    min-height: 400px;

  }



  .why-us .content {

    padding-top: 30px;

  }



  .why-us .accordion-list {

    padding-bottom: 30px;

  }

}



@media (max-width: 575px) {

  .why-us .img {

    min-height: 200px;

  }

}



/*--------------------------------------------------------------

# Skills

--------------------------------------------------------------*/

.skills .content h3 {

  font-weight: 700;

  font-size: 32px;

  color: #37517e;

  font-family: "Poppins", sans-serif;

}



.skills .content ul {

  list-style: none;

  padding: 0;

}



.skills .content ul li {

  padding-bottom: 10px;

}



.skills .content ul i {

  font-size: 20px;

  padding-right: 4px;

  color: #47b2e4;

}



.skills .content p:last-child {

  margin-bottom: 0;

}



.skills .progress {

  height: 60px;

  display: block;

  background: none;

  border-radius: 0;

}



.skills .progress .skill {

  padding: 0;

  margin: 0 0 6px 0;

  text-transform: uppercase;

  display: block;

  font-weight: 600;

  font-family: "Poppins", sans-serif;

  color: #37517e;

}



.skills .progress .skill .val {

  float: right;

  font-style: normal;

}



.skills .progress-bar-wrap {

  background: #e8edf5;

  height: 10px;

}



.skills .progress-bar {

  width: 1px;

  height: 10px;

  transition: 0.9s;

  background-color: #4668a2;

}



/*--------------------------------------------------------------

# Services

--------------------------------------------------------------*/

.services .icon-box {

  box-shadow: 0px 0 25px 0 rgba(32, 32, 32, 0.349);

  padding: 50px 30px;

  transition: all ease-in-out 0.4s;

  background: #1d263660;

  border-radius: 30px;
  border: 3px solid #16191f;
  /* Added border radius */
}



.services .icon-box .icon {

  margin-bottom: 10px;

}



.services .icon-box .icon i {

  color: #47b2e4;

  font-size: 36px;

  transition: 0.3s;

}



.services .icon-box h4 {

  font-weight: 500;

  margin-top: 15px;

  margin-bottom: 15px;

  font-size: 24px;

  text-align: center;

}



.services .icon-box h4 a {

  color: #fefeff;

  transition: ease-in-out 0.3s;

  text-align: center;

}



.services .icon-box p {

  line-height: 24px;

  font-size: 14px;

  margin-bottom: 0;

}



.services .icon-box:hover {

  transform: translateY(-10px);
  border: 3px solid #006fa2;

}



.services .icon-box:hover h4 a {

  color: #47b2e4;

}



/*--------------------------------------------------------------

# Cta

--------------------------------------------------------------*/

.cta {

  background: linear-gradient(to right, #37517e4f 100%, transparent 0%), url(https://img.freepik.com/free-vector/3d-style-black-background-with-paper-layer_206725-669.jpg?w=826&t=st=1709038049~exp=1709038649~hmac=9a729ee4318357de39c895db6687e1da792420e6f9a5994f32fd85ed4ed4d48e);

  background-size: cover;

  padding: 120px 0;

}



.cta h3 {

  color: #fff;

  font-size: 28px;

  font-weight: 700;

}



.cta p {

  color: #fff;

}



.cta .cta-btn {

  font-family: "Jost", sans-serif;

  font-weight: 500;

  font-size: 16px;

  letter-spacing: 1px;

  display: inline-block;

  padding: 12px 40px;

  border-radius: 50px;

  transition: 0.5s;

  margin: 10px;

  border: 2px solid #fff;

  color: #fff;

}



.cta .cta-btn:hover {

  background: #47b2e4;

  border: 2px solid #47b2e4;

}



@media (max-width: 1024px) {

  .cta {

    background-attachment: scroll;

  }

}



@media (min-width: 769px) {

  .cta .cta-btn-container {

    display: flex;

    align-items: center;

    justify-content: flex-end;

  }

}



/*--------------------------------------------------------------

# clients

--------------------------------------------------------------*/



/*--------------------------------------------------------------

# Testimonials

--------------------------------------------------------------*/

.testimonials .testimonials-carousel,

.testimonials .testimonials-slider {

  overflow: hidden;

}



.testimonials .testimonial-item {

  box-sizing: content-box;

  min-height: 320px;

}



.testimonials .testimonial-item .testimonial-img {

  width: 90px;

  border-radius: 50%;

  margin: -40px 0 0 40px;

  position: relative;

  z-index: 2;

  border: 6px solid #fff;

  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);

}



.testimonials .testimonial-item h3 {

  font-size: 18px;

  font-weight: bold;

  margin: 10px 0 5px 45px;

  color: #f4f6f8;

}



.testimonials .testimonial-item h4 {

  font-size: 14px;

  color: #999;

  margin: 0 0 0 45px;

}



.testimonials .testimonial-item .quote-icon-left,

.testimonials .testimonial-item .quote-icon-right {

  color: #b1a9fc;

  font-size: 26px;

}



.testimonials .testimonial-item .quote-icon-left {

  display: inline-block;

  left: -5px;

  position: relative;

}



.testimonials .testimonial-item .quote-icon-right {

  display: inline-block;

  right: -5px;

  position: relative;

  top: 10px;

}



.testimonials .testimonial-item p {

  font-style: italic;

  margin: 0 15px 0 15px;

  padding: 20px 20px 60px 20px;

  background: #1d263660;

  position: relative;

  border-radius: 6px;

  font-size: 18px;

  position: relative;

  z-index: 1;

  box-shadow: 0 0px 20px 0 rgba(0, 0, 0, 0.1);

  text-align: center;

}



.testimonials .swiper-pagination {

  margin-top: 20px;

  position: relative;

}



.testimonials .swiper-pagination .swiper-pagination-bullet {

  width: 12px;

  height: 12px;

  background-color: #fff;

  opacity: 1;

  border: 1px solid #5846f9;

}



.testimonials .swiper-pagination .swiper-pagination-bullet-active {

  background-color: #5846f9;

}



@media (max-width: 767px) {

  .testimonials {

    margin: 30px 10px;

  }

}



/*--------------------------------------------------------------

# Portfolio

--------------------------------------------------------------*/

.portfolio #portfolio-flters {

  list-style: none;

  margin-bottom: 20px;

}



.portfolio #portfolio-flters li {

  cursor: pointer;

  display: inline-block;

  margin: 10px 5px;

  font-size: 15px;

  font-weight: 500;

  line-height: 1;

  color: #ffffff;

  transition: all 0.3s;

  padding: 8px 20px;

  border-radius: 50px;

  font-family: "Poppins", sans-serif;

}



.portfolio #portfolio-flters li:hover,

.portfolio #portfolio-flters li.filter-active {

  background: #47b2e4;

  color: #fff;

}



.portfolio .portfolio-item {

  margin-bottom: 30px;

}



.portfolio .portfolio-item::before {

  content: "";

  background: rgba(34, 34, 34, 0.6);

  position: absolute;

  left: 11px;

  right: 12px;

  top: 0;

  bottom: 0;

  transition: all ease-in-out 0.3s;

  z-index: 2;

  opacity: 5;

}



.portfolio .portfolio-item .portfolio-img {

  overflow: hidden;

}



.portfolio .portfolio-item .portfolio-img img {

  transition: all 0.6s;

}



.portfolio .portfolio-item .portfolio-info {

  /* opacity: 0; */

  position: absolute;

  left: 15px;

  bottom: 0;

  z-index: 3;

  right: 15px;

  transition: all 0.3s;

  /* background: rgba(55, 81, 126, 0.8); */

  padding: 10px 15px;

}



.portfolio .portfolio-item .portfolio-info h4 {

  font-size: 18px;

  color: #fff;

  font-weight: 600;

  color: #fff;

  margin-bottom: 0px;

}



.portfolio .portfolio-item .portfolio-info p {

  color: #f9fcfe;

  font-size: 14px;

  margin-bottom: 0;

}



.portfolio .portfolio-item .portfolio-info .preview-link,

.portfolio .portfolio-item .portfolio-info .details-link {

  position: absolute;

  right: 40px;

  font-size: 24px;

  top: calc(50% - 18px);

  color: #fff;

  transition: 0.3s;

}



.portfolio .portfolio-item .portfolio-info .preview-link:hover,

.portfolio .portfolio-item .portfolio-info .details-link:hover {

  color: #47b2e4;

}



.portfolio .portfolio-item .portfolio-info .details-link {

  right: 10px;

}



.portfolio .portfolio-item:hover .portfolio-img img {

  transform: scale(1.15);

}



.portfolio .portfolio-item:hover .portfolio-info {

  opacity: 1;

}







/*--------------------------------------------------------------

# Portfolio Details

--------------------------------------------------------------*/

.portfolio-details {

  padding-top: 100px;

}



.portfolio-details .portfolio-details-slider img {

  width: 100%;

}



.portfolio-details .portfolio-details-slider .swiper-pagination {

  margin-top: 20px;

  position: relative;

}



.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet {

  width: 12px;

  height: 12px;

  background-color: #fff;

  opacity: 1;

  border: 1px solid #47b2e4;

}



.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet-active {

  background-color: #47b2e4;

}



.portfolio-details .portfolio-info {

  padding: 30px;

  box-shadow: 0px 0 30px rgba(55, 81, 126, 0.08);

  margin-bottom: 10px;

}



.portfolio-details .portfolio-info h3 {

  font-size: 22px;

  font-weight: 700;

  margin-bottom: 20px;

  padding-bottom: 20px;

  border-bottom: 1px solid #eee;

}



.portfolio-details .portfolio-info ul {

  list-style: none;

  padding: 0;

  font-size: 15px;

}



.portfolio-details .portfolio-info ul li+li {

  margin-top: 10px;

}



.portfolio-details .portfolio-description {

  padding-top: 30px;

}



.portfolio-details .portfolio-description h2 {

  font-size: 26px;

  font-weight: 700;

  margin-bottom: 20px;

}



.portfolio-details .portfolio-description p {

  padding: 0;

}



.portfolio-details h3 {

  color: #fff;

  font-size: 28px;

  font-weight: 700;

}



.portfolio-details p {

  color: #fff;

}



.portfolio-details .details-btn {

  font-family: "Jost", sans-serif;

  font-weight: 500;

  font-size: 16px;

  letter-spacing: 1px;

  display: inline-block;

  padding: 12px 40px;

  border-radius: 50px;

  transition: 0.5s;

  margin: 10px;

  border: 2px solid #006fa3;

  color: #fff;

  background: #08a0ff13;

}

.portfolio-details .buy-btn {

  display: inline-block;

  padding: 12px 35px;

  border-radius: 50px;

  color: #47b2e4;

  transition: none;

  font-size: 18px;

  font-weight: 500;

  font-family: "Jost", sans-serif;

  transition: 0.3s;

  border: 1px solid #47b2e4;

}



.portfolio-details .buy-btn:hover {

  background: #193d4e;

  color: #fff;

}



.portfolio-details .featured {

  border-top-color: #47b2e4;

}



.portfolio-details .featured .buy-btn {

  background: #47b2e40a;

  color: #fff;

}



.portfolio-details .featured .buy-btn:hover {

  background: #23a3df;

}



.portfolio-details .details-btn:hover {

  background: #0eaffa41;

  border: 2px solid #4ac1f8;

}



@media (max-width: 1024px) {

  .portfolio-details {

    background-attachment: scroll;

  }

}



@media (min-width: 769px) {

  .portfolio-details .details-btn-container {

    display: flex;

    align-items: center;

    justify-content: flex-end;

  }

}



/*--------------------------------------------------------------

# Team

--------------------------------------------------------------*/

.team .member {

  position: relative;

  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);

  padding: 30px;

  border-radius: 44px;

  background: #fff;

  transition: 0.5s;

  height: 100%;

}



.team .member .pic {

  overflow: hidden;

  width: 180px;

  border-radius: 50%;

}



.team .member .pic img {

  transition: ease-in-out 0.3s;

}



.team .member:hover {

  transform: translateY(-10px);

}



.team .member .member-info {

  padding-left: 30px;

}



.team .member h4 {

  font-weight: 700;

  margin-bottom: 5px;

  font-size: 20px;

  color: #37517e;

}



.team .member span {

  display: block;

  font-size: 15px;

  padding-bottom: 10px;

  position: relative;

  font-weight: 500;

}



.team .member span::after {

  content: "";

  position: absolute;

  display: block;

  width: 50px;

  height: 1px;

  background: #cbd6e9;

  bottom: 0;

  left: 0;

}



.team .member p {

  margin: 10px 0 0 0;

  font-size: 14px;

}



.team .member .social {

  margin-top: 12px;

  display: flex;

  align-items: center;

  justify-content: flex-start;

}



.team .member .social a {

  transition: ease-in-out 0.3s;

  display: flex;

  align-items: center;

  justify-content: center;

  border-radius: 50px;

  width: 32px;

  height: 32px;

  background: #eff2f8;

}



.team .member .social a i {

  color: #37517e;

  font-size: 16px;

  margin: 0 2px;

}



.team .member .social a:hover {

  background: #47b2e4;

}



.team .member .social a:hover i {

  color: #fff;

}



.team .member .social a+a {

  margin-left: 8px;

}



/*--------------------------------------------------------------

# Pricing

--------------------------------------------------------------*/

.pricing .row {

  padding-top: 20px;
  padding-bottom: 40px;
  justify-content: center;

}



.pricing .box {

  padding: 55px 20px 35px 20px;

  box-shadow: 0 3px 20px -2px rgba(20, 45, 100, 0.1);

  background: linear-gradient(to right, #142542e5 100%, transparent 0%), url(https://e0.pxfuel.com/wallpapers/912/523/desktop-wallpaper-fivem-police-page-1-gta-5-police.jpg);

  background-size: cover;

  height: 100%;

  border-top: 5px solid #fff;

  border-radius: 44px;

  text-align: center;

}

.pricing .box2 {

  padding: 50px 20px;

  box-shadow: 0 3px 20px -2px rgba(20, 45, 100, 0.1);

  background: linear-gradient(to right, #142542e5 100%, transparent 0%), url(https://w0.peakpx.com/wallpaper/302/648/HD-wallpaper-gta-motorbike-bike-cool-entertainment-game-man-new-weapon.jpg);

  background-size: cover;

  height: 100%;

  border-top: 4px solid #fff;

  border-radius: 44px;

  text-align: center;

}



.pricing h3 {

  font-weight: 500;

  margin-bottom: 5px;

  font-size: 22px;

  color: #ffffff;

  text-align: center;

}



.pricing h4 {

  font-size: 16px;

  color: #797b7c;

  font-weight: 400;

  font-family: "Jost", sans-serif;

  margin-bottom: 30px;

  text-align: center;

}



.pricing h4 sup {

  font-size: 15px;

}



.pricing h4 span {

  color: #37517e;

  font-size: 16px;

  font-weight: 300;

}



.pricing .pricing-img {

  width: 50%;

  display: block;

  margin-left: auto;

  margin-right: auto;

}

.pricing-img .mx-auto {

  margin-left: auto;

  margin-right: auto;

}



.pricing ul {

  padding: 20px 0;

  list-style: none;

  color: #999;

  text-align: left;

  line-height: 20px;

}



.pricing ul li {

  padding: 3px 0 3px 3px;

  position: relative;

  text-align: center;

}



.pricing ul i {

  color: #28a745;

  font-size: 24px;

  position: absolute;

  left: 0;

  top: 6px;

}



.pricing ul .na {

  color: #ccc;

}



.pricing ul .na i {

  color: #ccc;

}



.pricing ul .na span {

  text-decoration: line-through;

}



.pricing .buy-btn {

  display: inline-block;

  padding: 12px 35px;

  border-radius: 50px;

  color: #fff;

  transition: none;

  font-size: 18px;

  font-weight: 500;

  font-family: "Jost", sans-serif;

  transition: 0.3s;

  border: 1px solid #47b2e4;

  margin-top: 25px;

}



.pricing .buy-btn:hover {

  background: #47b2e4;

  color: #fff;

}



.pricing .featured {

  border-top-color: #006fa3;

}



.pricing .featured .buy-btn {

  background: #47b2e40a;

  color: #fff;

}



.pricing .featured .buy-btn:hover {

  background: #23a3df;

}



@media (max-width: 992px) {

  .pricing .box {

    max-width: 60%;

    margin: 0 auto 30px auto;

  }

}



@media (max-width: 767px) {

  .pricing .box {

    max-width: 80%;

    margin: 0 auto 30px auto;

  }

}



@media (max-width: 420px) {

  .pricing .box {

    max-width: 100%;

    margin: 0 auto 30px auto;

  }

}



.pricing .details-btn {

  display: inline-block;

  padding: 12px 35px;

  border-radius: 50px;

  color: #47b2e4;

  transition: none;

  font-size: 16px;

  font-weight: 500;

  font-family: "Jost", sans-serif;

  transition: 0.3s;

  border: 3px solid #006fa3;

  margin-top: 40px;

}



.pricing .details-btn:hover {

  background: #47b2e4;

  color: #fff;

}







.pricing .featured .details-btn {

  background: #0649680a;

  color: #fff;

}



.pricing .featured .details-btn:hover {

  background: #209ed859;

  border: 3px solid #47b2e4;

}



@media (max-width: 992px) {

  .pricing .box {

    max-width: 60%;

    margin: 0 auto 30px auto;

  }

}



@media (max-width: 767px) {

  .pricing .box {

    max-width: 80%;

    margin: 0 auto 30px auto;

  }

}



@media (max-width: 420px) {

  .pricing .box {

    max-width: 100%;

    margin: 0 auto 30px auto;

  }

}







.pricing .pricing-detail-slider img {

  width: 100%;

}



.pricing .pricing-detail-slider .swiper-pagination {

  margin-top: 20px;

  position: relative;

}



.pricing .pricing-detail-slider .swiper-pagination .swiper-pagination-bullet {

  width: 12px;

  height: 12px;

  background-color: #fff;

  opacity: 1;

  border: 1px solid #47b2e4;

}



.pricing .pricing-detail-slider .swiper-pagination .swiper-pagination-bullet-active {

  background-color: #47b2e4;

}



.pricing .pricing-info {

  padding: 30px;

  box-shadow: 0px 0 30px rgba(55, 81, 126, 0.08);

  margin-bottom: 75px;

}



.pricing .pricing-info h3 {

  font-size: 22px;

  font-weight: 700;

  margin-bottom: 20px;

  padding-bottom: 20px;

  border-bottom: 1px solid #ffffff;

  text-align: left;

  color: #ffffff;

}



.pricing .pricing-info ul {

  list-style: none;

  padding: 0;

  font-size: 15px;

}



.pricing .pricing-info ul li {

  padding: 3px 0 3px 3px;

  position: relative;

  text-align: left;

  color: #ffffff;

}



.pricing .pricing-info ul li+li {

  margin-top: 10px;

}



.pricing .pricing-description {

  padding-top: 30px;

}



.pricing .pricing-description h2 {

  font-size: 26px;

  font-weight: 700;

  margin-bottom: 20px;

}



.pricing .pricing-description p {

  padding: 0;

}



.pricing-detail h3 {

  color: #fff;

  font-size: 28px;

  font-weight: 700;

  text-align: left;

}

.pricing b {

  color: #ffffff;

}



.pricing-details h3 {

  color: #fff;

  font-size: 28px;

  font-weight: 700;

  text-align: left;

}

.pricing-details p {

  color: #fcfcfc;

}



.pricing .detail-btn {

  font-family: "Jost", sans-serif;

  font-weight: 500;

  font-size: 16px;

  letter-spacing: 1px;

  display: inline-block;

  padding: 12px 40px;

  border-radius: 50px;

  transition: 0.5s;

  margin: 10px;

  border: 2px solid #fff;

  color: #fff;

}



.pricing .detail-btn:hover {

  background: #47b2e4;

  border: 2px solid #47b2e4;

}



@media (max-width: 1024px) {

  .pricing {

    background-attachment: scroll;

  }

}



@media (min-width: 769px) {

  .pricing .detail-btn-container {

    display: flex;

    align-items: center;

    justify-content: flex-end;

  }

}

body {

  background-color: #121212;

}

.random-image {

  position: relative;

  text-align: center;

  margin-bottom: 20px;

}

.random-image img {

  width: 100%;

  height: auto;

}

.overlay {

  content: '';

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 100%;

  background-color: rgba(0, 0, 0, 0.5);

  z-index: 1;

}

.carousel-caption {

  position: absolute;

  top: 50%;

  left: 50%;

  transform: translate(-50%, -50%);

  text-align: center;

  color: white;

  width: 100%;

}



.carousel-caption h1 {

  font-size: 2.5rem;

  margin-bottom: 5px;

}



.carousel-caption h2 {

  font-size: 1.5rem;

  margin-top: 2px;

}



@media (max-width: 768px) {

  .carousel-caption h2 {

    display: none;

  }

}

.carousel-caption .caption-text {

  font-size: 1.2rem;

  max-width: 700px;

  margin: 0 auto;

}



.caption-text {

  font-size: 1.2rem;

  max-width: 900px;

  margin: 0 auto;

}



@media (max-width: 768px) {

  .caption-text {

    display: none;

  }

}



.container {

  color: white;

  padding: 0 15px;

}

.vps-table {

  margin-top: 30px;

  color: white;

}

.order-btn {

  width: 100%;

}

.navbar {

  background-color: #121212 !important;

}

.section {

  background-color: #121212;

  color: #fff;

  padding: 50px 0;

}

.footer {

  background-color: #121212;

  color: #fff;

  padding: 10px 0;

}

.navbar-text .btn-primary {

  background-color: #007bff;

  color: #fff;

  border: 2px solid #007bff;

  border-radius: 12px;

  padding: 8px 10px;

  transition: all 0.3s ease;

}

.navbar-text .btn-primary:hover {

  background-color: #0056b3;

  border-color: #0056b3;

}



.vps-table thead th,

.vps-table tbody td {

  border-color: #131520;

}



.card-body {

  flex: 1 1 auto;

  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);

  color: var(--bs-card-color);

  box-shadow: 0 3px 20px -2px rgba(20, 45, 100, 0.1);

  border-top: 4px solid #fff;

  border-radius: 44px;

}

.card-body .featured {

  border-top-color: #47b2e4;

}



.card-body p {

  color: #c0bebe;

  font-size: 16px
}



.card-body h5 {

  color: #ffffffcb;

  font-size: 25px;

}



.card-body b {

  color: #c0bebe;

  font-size: 19px
}



.card-body .details-btn:hover {

  background: #47b2e4;

  color: #fff;

}



.card-body .featured {

  border-top-color: #47b2e4;

}



.card-body .featured .details-btn {

  background: #47b2e40a;

  color: #fff;

}



.card-body .featured .details-btn:hover {

  background: #23a3df;

}



@media (max-width: 991.98px) {

  .navbar-text {

    position: absolute;

    top: 50%;

    right: 15px;

    transform: translateY(-50%);

  }

}



/*--------------------------------------------------------------

# Frequently Asked Questions

--------------------------------------------------------------*/

.faq .faq-list {

  padding: 0 100px;

}



.faq .faq-list ul {

  padding: 0;

  list-style: none;

}



.faq .faq-list li+li {

  margin-top: 15px;

}



.faq .faq-list li {

  padding: 20px;

  background: #fff;

  border-radius: 4px;

  position: relative;

}



.faq .faq-list a {

  display: block;

  position: relative;

  font-family: "Poppins", sans-serif;

  font-size: 16px;

  line-height: 24px;

  font-weight: 500;

  padding: 0 30px;

  outline: none;

  cursor: pointer;

}



.faq .faq-list .icon-help {

  font-size: 24px;

  position: absolute;

  right: 0;

  left: 20px;

  color: #47b2e4;

}



.faq .faq-list .icon-show,

.faq .faq-list .icon-close {

  font-size: 24px;

  position: absolute;

  right: 0;

  top: 0;

}



.faq .faq-list p {

  margin-bottom: 0;

  padding: 10px 0 0 0;

}



.faq .faq-list .icon-show {

  display: none;

}



.faq .faq-list a.collapsed {

  color: #37517e;

  transition: 0.3s;

}



.faq .faq-list a.collapsed:hover {

  color: #47b2e4;

}



.faq .faq-list a.collapsed .icon-show {

  display: inline-block;

}



.faq .faq-list a.collapsed .icon-close {

  display: none;

}



@media (max-width: 1200px) {

  .faq .faq-list {

    padding: 0;

  }

}



/*--------------------------------------------------------------

# Contact

--------------------------------------------------------------*/

.contact .info {

  border-top: 3px solid #47b2e4;

  border-bottom: 3px solid #47b2e4;

  padding: 30px;

  background: #fff;

  width: 100%;

  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.1);

}



.contact .info i {

  font-size: 20px;

  color: #47b2e4;

  float: left;

  width: 44px;

  height: 44px;

  background: #e7f5fb;

  display: flex;

  justify-content: center;

  align-items: center;

  border-radius: 50px;

  transition: all 0.3s ease-in-out;

}



.contact .info h4 {

  padding: 0 0 0 60px;

  font-size: 22px;

  font-weight: 600;

  margin-bottom: 5px;

  color: #37517e;

}



.contact .info p {

  padding: 0 0 10px 60px;

  margin-bottom: 20px;

  font-size: 14px;

  color: #6182ba;

}



.contact .info .email p {

  padding-top: 5px;

}



.contact .info .social-links {

  padding-left: 60px;

}



.contact .info .social-links a {

  font-size: 18px;

  display: inline-block;

  background: #333;

  color: #fff;

  line-height: 1;

  padding: 8px 0;

  border-radius: 50%;

  text-align: center;

  width: 36px;

  height: 36px;

  transition: 0.3s;

  margin-right: 10px;

}



.contact .info .social-links a:hover {

  background: #47b2e4;

  color: #fff;

}



.contact .info .email:hover i,

.contact .info .address:hover i,

.contact .info .phone:hover i {

  background: #47b2e4;

  color: #fff;

}



.contact .php-email-form {

  width: 100%;

  border-top: 3px solid #47b2e4;

  border-bottom: 3px solid #47b2e4;

  padding: 30px;

  background: #fff;

  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.12);

}



.contact .php-email-form .form-group {

  padding-bottom: 8px;

}



.contact .php-email-form .validate {

  display: none;

  color: red;

  margin: 0 0 15px 0;

  font-weight: 400;

  font-size: 13px;

}



.contact .php-email-form .error-message {

  display: none;

  color: #fff;

  background: #ed3c0d;

  text-align: left;

  padding: 15px;

  font-weight: 600;

}



.contact .php-email-form .error-message br+br {

  margin-top: 25px;

}



.contact .php-email-form .sent-message {

  display: none;

  color: #fff;

  background: #18d26e;

  text-align: center;

  padding: 15px;

  font-weight: 600;

}



.contact .php-email-form .loading {

  display: none;

  background: #fff;

  text-align: center;

  padding: 15px;

}



.contact .php-email-form .loading:before {

  content: "";

  display: inline-block;

  border-radius: 50%;

  width: 24px;

  height: 24px;

  margin: 0 10px -6px 0;

  border: 3px solid #18d26e;

  border-top-color: #eee;

  animation: animate-loading 1s linear infinite;

}



.contact .php-email-form .form-group {

  margin-bottom: 20px;

}



.contact .php-email-form label {

  padding-bottom: 8px;

}



.contact .php-email-form input,

.contact .php-email-form textarea {

  border-radius: 0;

  box-shadow: none;

  font-size: 14px;

  border-radius: 4px;

}



.contact .php-email-form input:focus,

.contact .php-email-form textarea:focus {

  border-color: #47b2e4;

}



.contact .php-email-form input {

  height: 44px;

}



.contact .php-email-form textarea {

  padding: 10px 12px;

}



.contact .php-email-form button[type=submit] {

  background: #47b2e4;

  border: 0;

  padding: 12px 34px;

  color: #fff;

  transition: 0.4s;

  border-radius: 50px;

}



.contact .php-email-form button[type=submit]:hover {

  background: #209dd8;

}



@keyframes animate-loading {

  0% {

    transform: rotate(0deg);

  }



  100% {

    transform: rotate(360deg);

  }

}



/*--------------------------------------------------------------

# Breadcrumbs

--------------------------------------------------------------*/

.breadcrumbs {

  padding: 15px 0;

  background: #121212;

  min-height: 40px;

  margin-top: 72px;

}



@media (max-width: 992px) {

  .breadcrumbs {

    margin-top: 68px;

  }

}



.breadcrumbs h2 {

  font-size: 28px;

  font-weight: 600;

  color: #37517e;

}



.breadcrumbs ol {

  display: flex;

  flex-wrap: wrap;

  list-style: none;

  padding: 0 0 10px 0;

  margin: 0;

  font-size: 14px;

}



.breadcrumbs ol li+li {

  padding-left: 10px;

}



.breadcrumbs ol li+li::before {

  display: inline-block;

  padding-right: 10px;

  color: #4668a2;

  content: "/";

}



/*--------------------------------------------------------------

# Footer

--------------------------------------------------------------*/

#footer {

  font-size: 14px;

  background: #181818;

}



#footer .footer-newsletter {

  padding: 50px 0;

  background: #f3f5fa;

  text-align: center;

  font-size: 15px;

  color: #444444;

}



#footer .footer-newsletter h4 {

  font-size: 24px;

  margin: 0 0 20px 0;

  padding: 0;

  line-height: 1;

  font-weight: 600;

  color: #ffffff;

}



#footer .footer-newsletter form {

  margin-top: 30px;

  background: #fff;

  padding: 6px 10px;

  position: relative;

  border-radius: 50px;

  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.06);

  text-align: left;

}



#footer .footer-newsletter form input[type=email] {

  border: 0;

  padding: 4px 8px;

  width: calc(100% - 100px);

}



#footer .footer-newsletter form input[type=submit] {

  position: absolute;

  top: 0;

  right: 0;

  bottom: 0;

  border: 0;

  background: none;

  font-size: 16px;

  padding: 0 20px;

  background: #47b2e4;

  color: #fff;

  transition: 0.3s;

  border-radius: 50px;

  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);

}



#footer .footer-newsletter form input[type=submit]:hover {

  background: #209dd8;

}



#footer .footer-top {

  padding: 60px 0 30px 0;

  width: 100%;

  /* height: 195vh; */

  background: linear-gradient(to right, #141414f3 100%, transparent 0%);

  background-size: cover;

}



#footer .footer-top .footer-contact {

  margin-bottom: 30px;

}



#footer .footer-top .footer-contact h3 {

  font-size: 28px;

  margin: 0 0 10px 0;

  padding: 2px 0 2px 0;

  line-height: 1;

  text-transform: uppercase;

  font-weight: 600;

  color: #ffffff;

}



#footer .footer-top .footer-contact p {

  font-size: 14px;

  line-height: 24px;

  margin-bottom: 0;

  font-family: "Jost", sans-serif;

  color: #ffffff99;

}



#footer .footer-top h4 {

  font-size: 16px;

  font-weight: bold;

  color: #ffffff;

  position: relative;

  padding-bottom: 12px;

}



#footer .footer-top .footer-links {

  margin-bottom: 30px;

}



#footer .footer-top .footer-links ul {

  list-style: none;

  padding: 0;

  margin: 0;

}



#footer .footer-top .footer-links ul i {

  padding-right: 2px;

  color: #47b2e4;

  font-size: 18px;

  line-height: 1;

}



#footer .footer-top .footer-links ul li {

  padding: 10px 0;

  display: flex;

  align-items: center;

}



#footer .footer-top .footer-links ul li:first-child {

  padding-top: 0;

}



#footer .footer-top .footer-links ul a {

  color: #ffffff99;

  transition: 0.3s;

  display: inline-block;

  line-height: 1;

}



#footer .footer-top .footer-links ul a:hover {

  text-decoration: none;

  color: #47b2e4;

}



#footer .footer-top .social-links a {

  font-size: 18px;

  display: inline-block;

  background: #08a0ff13;

  color: #fff;

  line-height: 1;

  padding: 8px 0;

  margin-right: 4px;

  border-radius: 50%;
  border: 2px solid rgba(0, 111, 163, 0.548);

  text-align: center;

  width: 36px;

  height: 36px;

  transition: 0.3s;

  border-top-width: 1px;

}



#footer .footer-top .social-links a:hover {

  background: #209dd8;

  color: #fff;

  text-decoration: none;

}



#footer .footer-bottom {

  padding-top: 30px;

  padding-bottom: 30px;

  color: #fff;

}



#footer .copyright {

  float: left;

}





#footer .credits {

  float: right;

  font-size: 13px;

  margin-top: -25px;

}



#footer .copyright-wrap {

  border-top: 1px solid #e8e6f7;

}

#footer .credits a {

  transition: 0.3s;

}



@media (max-width: 768px) {

  #footer .footer-bottom {

    padding-top: 20px;

    padding-bottom: 20px;

  }



  #footer .copyright,

  #footer .credits {

    text-align: center;

    float: none;

  }



  #footer .credits {

    padding-top: 4px;

  }

}



body {

  background-color: #121212;

}

.random-image {

  position: relative;

  text-align: center;

  margin-bottom: 20px;

}

.random-image img {

  width: 100%;

  height: auto;

}

.overlay {

  content: '';

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 100%;

  background-color: rgba(0, 0, 0, 0.5);

  z-index: 1;

}

.carousel-caption {

  position: absolute;

  top: 50%;

  left: 50%;

  transform: translate(-50%, -50%);

  text-align: center;

  color: white;

  width: 100%;

}



.carousel-caption h1 {

  font-size: 2.5rem;

  margin-bottom: 5px;

}



.carousel-caption h2 {

  font-size: 1.5rem;

  margin-top: 2px;

}



@media (max-width: 768px) {

  .carousel-caption h2 {

    display: none;

  }

}

.carousel-caption .caption-text {

  font-size: 1.2rem;

  max-width: 700px;

  margin: 0 auto;

}



.caption-text {

  font-size: 1.2rem;

  max-width: 900px;

  margin: 0 auto;

}



@media (max-width: 768px) {

  .caption-text {

    display: none;

  }

}



.container {

  color: white;

  padding: 0 15px;

}

.vps-table {

  margin-top: 30px;

  color: white;

}

.order-btn {

  width: 100%;

}

.navbar {

  background-color: #12121200 !important;

}

.section {

  background-color: #121212;

  color: #fff;

  padding: 50px 0;

}

.footer {

  background-color: #121212;

  color: #fff;

  padding: 10px 0;

}

.navbar-text .btn-primary {

  background-color: #007bff;

  color: #fff;

  border: 2px solid #007bff;

  border-radius: 12px;

  padding: 8px 10px;

  transition: all 0.3s ease;

}

.navbar-text .btn-primary:hover {

  background-color: #0056b3;

  border-color: #0056b3;

}



.vps-table thead th,

.vps-table tbody td {

  border-color: #131520;

}



@media (max-width: 991.98px) {

  .navbar-text {

    position: absolute;

    top: 50%;

    right: 15px;

    transform: translateY(-50%);

  }

}



/*--------------------------------------------------------------

  # World

  --------------------------------------------------------------*/



.locations {

  background: #121212;

  /*margin-bottom: 80px;*/

}



.locations .map_content {

  position: relative;

}



.locations h2 {

  font-weight: 700;

  font-size: 30px;

  color: #fff;

  letter-spacing: -0.04rem;

}



.locations p {

  color: #ecf0f3;

}



.loc_mark {

  width: 14px;

  height: 14px;

  display: block;

  background-color: #006fa3;

  position: absolute;

  border-radius: 100%;
    box-shadow: 0 0 10px #4da6ff;
    animation: pulse 2s infinite;
    z-index: 2;

}

@keyframes pulse {
  0% {
      box-shadow: 0 0 0 0 rgba(77, 166, 255, 0.7);
  }
  70% {
      box-shadow: 0 0 0 10px rgba(77, 166, 255, 0);
  }
  100% {
      box-shadow: 0 0 0 0 rgba(77, 166, 255, 0);
  }
} 



.id_mark {

  left: 77.5%;

  top: 79%;

}





.sgp_mark {

  left: 75%;

  top: 65%;

}



p {

  margin-top: 0;

  margin-bottom: 1rem;

}

.orange {

  color: #089fff;

}

.blue {

  color: #089fff;

}



.arrow_bottom span {

  top: 18px;

  left: -3rem;

}



.arrow_top span {

  bottom: 18px;

  left: -3rem;

}



.loc_mark span {

  font-weight: 700;

  display: block;

  background: #08a0ff13;

  width: 140px;

  height: 38px;

  display: block;

  border-radius: 12px;
  
  border: 1px solid #006fa3;

  color: #fff;

  font-size: 12px;

  text-transform: uppercase;

  text-align: center;
  margin-bottom: 5px;
  margin-top: 5px;

  line-height: 34px;

  position: absolute;

  box-shadow: 19px 19px 27px 0 rgb(16, 18, 19 / 30%);

}



.container-custom {

  max-width: 1300px;

}

@media (min-width: 992px) {

  .col-lg-6 {

    flex: 0 0 auto;

    width: 50%;

  }

}



/*--------------------------------------------------------------

  # Tech Processor

  --------------------------------------------------------------*/

.tech_processor {

  background-image: linear-gradient(rgba(26, 28, 30, 0), rgba(26, 28, 30, 0)));

  background-color: #151515;

  background-attachment: fixed;

  background-position: center;

  background-repeat: no-repeat;

  background-size: cover;

}



.container-custom {

  max-width: 1300px;

}



@media (min-width: 768px) {

  .col-md-5 {

    flex: 0 0 auto;

    width: 41.66666667%;

  }

}



.tech_processor .tech_text h2 {

  color: #fff;

  font-weight: 600;

}



@media (min-width: 1200px) {

  .h2,
  h2 {

    font-size: 2rem;

  }

}



.tech_processor .tech_text p {

  color: #fff;

}



.server-list {

  max-width: 800px;

  margin: auto;

}



.server-item {

  display: flex;

  align-items: center;

  background-color: white;

  padding: 10px;

  margin-bottom: 10px;

  border: 1px solid #ddd;

  border-radius: 5px;

}



.flag img {

  width: 30px;

  height: 20px;

  margin-right: 10px;

}



.server-info {

  flex-grow: 1;

}



.server-name {

  font-weight: bold;

  margin-bottom: 5px;

}



.server-description {

  color: #888;

}



.server-status {

  margin-right: 20px;

}



.server-status.online {

  color: green;

  font-weight: bold;

}



.server-stats {

  color: #888;

}



/*--------------------------------------------------------------

# superiority Section

--------------------------------------------------------------*/

.superioritys .superior-item {

  background-color: #232323;

  border: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);

  height: 100%;

  padding: 25px;

  transition: 0.3s;

  border-radius: 10px;

  display: flex;

  align-items: center;
  /* Menyelaraskan elemen secara vertikal */

  justify-content: center;
  /* Menyelaraskan ikon dan teks secara horizontal */

}



.icon-text-wrapper {

  display: flex;

  align-items: center;

}



.superioritys .superior-item .icon {

  font-size: 32px;

  border-radius: 10px;

  display: inline-flex;

  align-items: center;

  justify-content: center;

  width: 72px;

  height: 72px;

  margin-right: 25px;

  flex-shrink: 0;

}



.text-content {

  display: flex;

  flex-direction: column;

  justify-content: center;
  /* Menyelaraskan teks secara vertikal */

}



.superioritys .superior-item h3 {

  color: color-mix(in srgb, var(--heading-color), transparent 25%);

  font-weight: 700;

  font-size: 22px;

  font-family: 'Poppins', sans-serif;
  /* Ubah font menjadi lebih modern */

  transition: 0.3s;

}



.superioritys .superior-item p {

  margin-bottom: 0;

  color: color-mix(in srgb, var(--default-color), transparent 40%);

  transition: 0.3s;

}





.superioritys .superior-item .read-more {

  display: inline-flex;

  align-items: center;

  margin-top: 10px;

  transition: 0.3s;

  font-size: 14px;

}



.superioritys .superior-item .read-more i {

  margin-left: 10px;

}



.superioritys .superior-item.item-cyan .icon {

  color: #0dcaf0;

  border: 1px solid #00bcf252;

  background: rgb(0 188 242 / 9%);

}



.superioritys .superior-item.item-orange .icon {

  color: #148ffd;

  border: 1px solid #e8122461;

  background: rgb(232 18 36 / 10%);

}



.superioritys .superior-item.item-teal .icon {

  color: #20c997;

  border: 1px solid #ffd4714f;

  background: rgb(255 211 106 / 12%);

}



.superioritys .superior-item.item-red .icon {

  color: #c6e06b;

  border: 1px solid #b6d7554d;

  background: rgb(186 217 89 / 13%);

}



.superioritys .superior-item.item-indigo .icon {

  color: #6610f2;

  border: 1px solid #f8904659;

  background: rgb(248 144 71 / 13%);

}



.superioritys .superior-item.item-pink .icon {

  color: #9c26f3;

  border: 1px solid #886ce47a;

  background: rgb(136 108 228 / 15%);

}



.superioritys .superior-item:hover {

  box-shadow: 0px 2px 25px rgba(0, 0, 0, 0.1);

}



.superioritys .superior-item:hover h3 {

  color: var(--heading-color);

}



.superioritys .superior-item:hover p {

  color: color-mix(in srgb, var(--default-color), transparent 10%);

}

        /* Server Configuration */
        .per-month {
          font-size: 1.1rem;
  color: #4da6ff;
  margin-left: -4px;
}

.server-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 10px;
  }

  .config-section {
    display: flex;
    flex-wrap: nowrap;
    gap: 2rem;
    margin-bottom: 3rem;
    align-items: flex-start;
  }

  .config-left {
    width: 60%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .config-right {
    width: 40%;
  }

  .config-subtitle {
    font-size: 22px;
    font-weight: 500;
    color: #ff8f3f;
  }

  .slider-container p {
    margin-bottom: 0.8rem;
  }

  .slider {
      width: 100%;
  margin-bottom: 0.5rem;
  height: 10px;
  border-radius: 5px;
  background: #2a3146;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #aaa;
    margin-top: 0.5rem;
  }

  .plan-description {
      background-color: #2c2c2cc2;
  padding: 1.5rem 1.1rem;
  border-radius: 8px;
  color: #ddd;
  font-size: 0.95rem;
  line-height: 1.5;
  /* margin-bottom: 20px; */
  /* border-top: 6px solid #006fa3; */
  }

  .features-table {
background: linear-gradient(145deg, #1e1e1e, #2b2b2b);
padding: 1.5rem;
border-radius: 15px;
color: #ddd;
width: 100%;
overflow-x: auto;
box-shadow: 0 0 15px rgba(0,0,0,0.4);
border: 1px solid #3a3a3a;
}

.features-table .modern-table {
width: 100%;
border-collapse: collapse;
font-family: 'Segoe UI', sans-serif;
}

.features-table .modern-table th {
background-color: #ee6605;
color: white;
text-align: left;
padding: 0.9rem;
font-size: 1rem;
/* border-radius: 8px  0 8px; */
border-bottom: 2px solid #ee6605;
}

.features-table .modern-table td {
background-color: rgba(255, 255, 255, 0.03);
padding: 0.9rem;
border-bottom: 1px solid #3a3a3a;
font-size: 0.95rem;
}

.features-table .modern-table tbody tr:hover {
background-color: rgba(77, 166, 255, 0.1);
transition: 0.2s ease-in-out;
}

.features-table i {
margin-right: 8px;
color: #ff8f3f;
}


  .selected-plan {
    background-color: #2b2a2ab8;
    padding: 1.5rem;
    border-radius: 10px;
    border-top: 6px solid #ff8f3f;
    position: relative;
  }

  .plan-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff8f3f;
  }

  .plan-original-price {
    text-decoration: line-through;
    color: #aaa;
  }

  .plan-price {
    color: #ff8f3f;
    font-size: 2rem; 
    margin-top: -11px;
  }

  .plan-discount {
    color: #ff4d4d;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 1500px;
  }

  .plan-available {
    color: #6eff8d;
    display: flex;
    align-items: center;
    margin: 10px 0;
  }

  .available-dot {
    width: 10px;
    height: 10px;
    background-color: #6eff8d;
    border-radius: 50%;
    margin-right: 6px;
  }

  .specs-list {
    margin: 1rem 0;
  }

  .spec-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.6rem;
  }

  .spec-icon {
    margin-right: 0.5rem;
    color: #ccc;
  }

  .location img {
    margin-right: 0.5rem;
  }

  .select-plan-btn {
    display: block;
    text-align: center;
    background-color: #0649680a;
    color: white;
    padding: 0.8rem;
    border: 3px solid #ee6605;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.3s;
  }

  .select-plan-btn:hover {
    background: #f98c3e;
    color: #fff;
  }
  .plan-decoration-right {
position: absolute;
right: 40px;
top: 190px;
transform: translateY(-50%);
width: 130px;
opacity: 0.95;
filter: drop-shadow(0 0 10px rgba(0, 111, 163, 0.6));
animation: float 3s ease-in-out infinite;
pointer-events: none;
}


/* Animasi mengambang */
@keyframes float {
0% { transform: translateY(0px); }
50% { transform: translateY(-12px); }
100% { transform: translateY(0px); }
}


  @media (max-width: 768px) {
    .config-section {
      flex-direction: column;
    }
    .config-left,
    .config-right {
      width: 100%;
    }
  }
      
      @media (max-width: 1024px) {
          .navbar {
              padding: 1rem 3%;
          }
          
          .game-header {
              padding: 2.5rem 0 1.5rem;
          }
          
          .game-title {
              font-size: 2.2rem;
          }
          
          .config-title {
              font-size: 1.8rem;
          }
          
          .plan-price {
              font-size: 2.2rem;
          }
      }

      @media (max-width: 768px) {
          
          
          .close-menu {
              display: block; /* Only display on mobile */
          }
          
          .game-header {
              padding: 2rem 0 1rem;
          }
          
          .game-logo {
              width: 70px;
              height: 70px;
          }
          
          .game-title {
              font-size: 1.8rem;
          }
          
          .game-subtitle {
              font-size: 1rem;
          }
          
          .server-container {
              padding: 1rem;
          }
          
          .config-title {
              font-size: 1.6rem;
          }
          
          .config-subtitle {
              font-size: 1rem;
          }
          
          .slider-labels {
              font-size: 0.8rem;
          }
          
          .plan-name {
              font-size: 1.8rem;
          }
          
          .plan-price {
              font-size: 2rem;
          }
      }

      @media (max-width: 480px) {
          .logo {
              font-size: 1.2rem;
          }
          
          .game-logo {
              width: 60px;
              height: 60px;
          }
          
          .game-title {
              font-size: 1.6rem;
          }
          
          .config-title {
              font-size: 1.4rem;
          }
          
          .config-right {
              padding: 1rem;
          }
          
          .slider-labels {
              font-size: 0.7rem;
          }
          
          .plan-name {
              font-size: 1.5rem;
          }
          
          .plan-price {
              font-size: 1.8rem;
              margin-bottom: 1rem;
          }
          
          .spec-item {
              font-size: 0.9rem;
          }
          
          .select-plan-btn {
              padding: 0.7rem;
              font-size: 1rem;
          }
      }

/*--------------------------------------------------------------
# Products
--------------------------------------------------------------*/
#products {
    width: 100%;
    padding: 60px 0;
    background: linear-gradient(rgba(2, 2, 2, 0.9), rgba(2, 2, 2, 0.9)), url(https://dopminer.com/Gambar/CnP_17012025_111023.png);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.product-card {
    position: relative;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(71, 178, 228, 0.1));
    pointer-events: none;
}

.product-card:hover {
    transform: translateY(-10px);
    border-color: #47b2e4;
    box-shadow: 0 10px 30px rgba(71, 178, 228, 0.2);
}

.product-type {
    position: absolute;
    top: 255px;
    right: 111px;
    background: rgba(71, 178, 228, 0.1);
    color: #47b2e4;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 25px;
    transition: all 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-title {
    color: #fff;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

/* .product-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: #47b2e4;
} */

.product-description {
    color: #aaa;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 25px;
    min-height: 70px;
    /* white-space: pre-wrap; */
    word-wrap: break-word;
}

.price-container {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
}

.price-discount {
    color: #47e461;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    order: 2;
}

.price-normal {
    color: #ff4c4c;
    font-size: 16px;
    text-decoration: line-through;
    opacity: 0.7;
    order: 1;
}

.product-details-btn {
    display: inline-block;
    padding: 10px 30px;
    background: transparent;
    color: #47b2e4;
    border: 2px solid #47b2e4;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-details-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.product-details-btn:hover {
    background: #47b2e4;
    color: #fff;
    box-shadow: 0 5px 15px rgba(71, 178, 228, 0.4);
}

.product-details-btn:hover::before {
    left: 100%;
}

/* Responsive Products */
@media (max-width: 1024px) {
    .product-card {
        margin-bottom: 20px;
    }
    
    .product-title {
        font-size: 20px;
    }
    
    .price-normal {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    #products {
        padding: 40px 0;
    }
    
    .product-card {
        padding: 20px;
    }
    
    .product-image {
        height: 180px;
    }
    
    .product-title {
        font-size: 18px;
    }
    
    .product-description {
        font-size: 13px;
        min-height: 60px;
    }
    
    .price-normal {
        font-size: 22px;
    }
    
    .price-discount {
        font-size: 14px;
    }
    
    .product-details-btn {
        padding: 8px 25px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .product-image {
        height: 160px;
    }
    
    .product-type {
        font-size: 10px;
        padding: 4px 12px;
    }
}

.product-detail-popup {
    border: 1px solid rgba(71, 178, 228, 0.2) !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.5s ease-in-out !important;
}

.swal2-backdrop-show {
    backdrop-filter: blur(5px);
    transition: all 0.5s ease-in-out !important;
}

.swal2-backdrop-hide {
    backdrop-filter: blur(0px);
    transition: all 0.5s ease-in-out !important;
}

.animate__slow {
    --animate-duration: 0.8s;
}

/* Override Sweetalert2 animations */
.swal2-show {
    animation: none !important;
}

.swal2-hide {
    animation: none !important;
}

/* Navbar Scroll Effect */
.navbar-scrolled {
    background-color: rgba(10, 14, 39, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}