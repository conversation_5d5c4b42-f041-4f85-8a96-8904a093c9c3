<!DOCTYPE html>

<html lang="id">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><PERSON><PERSON></title>

    <style>

        body {

            font-family: Arial, sans-serif;

            background-color: #f0f0f0;

            margin: 0;

            padding: 0;

        }

        .gallery {

            display: flex;

            flex-wrap: wrap;

            justify-content: center;

            gap: 10px;

            padding: 20px;

        }

        .gallery .image-container {
            position: relative;
            display: inline-block;
        }

        .gallery img {

            max-width: 200px;

            max-height: 200px;

            object-fit: cover;

            border: 2px solid #ddd;

            border-radius: 5px;

            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

            transition: transform 0.2s;

            cursor: pointer;

        }

        .gallery img:hover {

            transform: scale(1.05);

        }

        .image-name {
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #333;
            background: rgba(255, 255, 255, 0.7);
            padding: 2px 5px;
            border-radius: 3px;
        }

        .upload-form {

            text-align: center;

            margin: 20px;

        }

        .upload-form input[type="file"] {

            margin-bottom: 10px;

        }

        .context-menu {

            display: none;

            position: absolute;

            background: white;

            border: 1px solid #ddd;

            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

            z-index: 1000;

        }

        .context-menu button {

            background: none;

            border: none;

            padding: 10px;

            width: 100%;

            cursor: pointer;

        }

        .context-menu button:hover {

            background: #f0f0f0;

        }

    </style>

</head>

<body>

    <h1 style="text-align:center; padding: 20px;">Galeri Gambar</h1>

    

    <div class="upload-form">

        <h2>Unggah Gambar</h2>

        <form action="upload.php" method="post" enctype="multipart/form-data">

            <input type="file" name="gambar" accept="image/*" required>

            <button type="submit">Unggah Gambar</button>

        </form>

    </div>



    <div class="gallery">

        <?php

        // URL folder gambar dari web server

        $folderUrl = 'https://dopminer.com/Gambar/';

        // Folder lokal di server

        $folderPath = __DIR__ . '/';

        

        // Menampilkan gambar dari folder lokal

        $images = glob($folderPath . "*.{jpg,jpeg,png,gif}", GLOB_BRACE);

        

        foreach ($images as $imagePath) {

            $imageUrl = $folderUrl . basename($imagePath);
            $imageName = basename($imagePath);
            echo "<div class='image-container'>
                    <img src='$imageUrl' alt='$imageName' onclick='showContextMenu(event, \"$imageUrl\", \"local\")'>
                    <div class='image-name'>$imageName</div>
                  </div>";

        }

        ?>

    </div>



    <!-- Context Menu -->

    <div id="contextMenu" class="context-menu">

        <button onclick="copyLink()">Copy Link</button>

        <button onclick="deleteImage()">Delete Image</button>

    </div>



    <script>

        let currentImageUrl = '';

        let imageType = ''; // 'local' or 'remote'



        function showContextMenu(event, url, type) {

            event.preventDefault();

            event.stopPropagation(); // Prevent click event from bubbling up

            currentImageUrl = url;

            imageType = type;

            

            const contextMenu = document.getElementById('contextMenu');

            const imageRect = event.target.getBoundingClientRect();

            contextMenu.style.top = `${imageRect.top + window.scrollY}px`;

            contextMenu.style.left = `${imageRect.right + 10}px`;

            contextMenu.style.display = 'block';

        }



        function copyLink() {

            const el = document.createElement('textarea');

            el.value = currentImageUrl;

            document.body.appendChild(el);

            el.select();

            document.execCommand('copy');

            document.body.removeChild(el);

            alert('Link gambar disalin: ' + currentImageUrl);

            hideContextMenu();

        }



        function deleteImage() {

            if (confirm('Apakah Anda yakin ingin menghapus gambar ini?')) {

                const xhr = new XMLHttpRequest();

                xhr.open('POST', 'delete.php', true);

                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                xhr.onload = function() {

                    if (xhr.status === 200) {

                        alert('Gambar berhasil dihapus.');

                        window.location.reload(); // Reload halaman untuk memperbarui galeri

                    } else {

                        alert('Gagal menghapus gambar.');

                    }

                };

                xhr.send('image=' + encodeURIComponent(currentImageUrl) + '&type=' + imageType);

            }

        }



        function hideContextMenu() {

            const contextMenu = document.getElementById('contextMenu');

            contextMenu.style.display = 'none';

        }



        window.addEventListener('click', function(event) {

            // Check if the click was outside of the context menu

            const contextMenu = document.getElementById('contextMenu');

            if (event.target.closest('#contextMenu') === null) {

                hideContextMenu();

            }

        });

    </script>

</body>

</html>
