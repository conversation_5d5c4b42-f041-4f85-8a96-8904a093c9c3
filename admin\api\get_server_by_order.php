<?php
// Start output buffering to prevent header issues
ob_start();

// Initialize session safely
if (session_status() == PHP_SESSION_NONE) {
    @ini_set('session.cookie_httponly', 1);
    @ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    @ini_set('session.use_strict_mode', 1);
    @session_start();
}

// Include database connection
require_once __DIR__ . '/../../database/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Check if user is admin
$email = $_SESSION['email'];
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database prepare error: ' . $conn->error]);
    exit;
}

$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user || $user['group'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied - Admin only']);
    exit;
}

// Get order ID from query parameter
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Order ID is required']);
    exit;
}

$order_id = $_GET['order_id'];

try {
    // First, get the order details to get email and server name
    $order_stmt = $conn->prepare("SELECT email, nama_server, id_order FROM `order` WHERE id = ? OR id_order = ?");
    $order_stmt->bind_param("ss", $order_id, $order_id);
    $order_stmt->execute();
    $order_result = $order_stmt->get_result();
    $order = $order_result->fetch_assoc();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'Order not found']);
        exit;
    }
    
    // Get server details from data_client table
    // First try to get by id_order if it exists
    if (!empty($order['id_order'])) {
        $server_stmt = $conn->prepare("SELECT * FROM data_client WHERE id_order = ?");
        $server_stmt->bind_param("s", $order['id_order']);
        $server_stmt->execute();
        $server_result = $server_stmt->get_result();
        $server = $server_result->fetch_assoc();
    } else {
        // Fallback to email and server name combination
        $server_stmt = $conn->prepare("SELECT * FROM data_client WHERE email = ? AND namakota = ?");
        $server_stmt->bind_param("ss", $order['email'], $order['nama_server']);
        $server_stmt->execute();
        $server_result = $server_stmt->get_result();
        $server = $server_result->fetch_assoc();
    }
    
    if (!$server) {
        echo json_encode(['success' => false, 'message' => 'Server not found']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'server' => $server,
        'order_info' => [
            'email' => $order['email'],
            'nama_server' => $order['nama_server'],
            'id_order' => $order['id_order']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Get server by order error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>