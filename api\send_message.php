<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Turn off error reporting for clean JSON output
error_reporting(0);
ini_set('display_errors', 0);

// Clean any output that might have been sent
if (ob_get_level()) {
    ob_clean();
}

include '../database/database.php';

// Check database connection
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['message']) || !isset($input['idchat'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid input data']);
    exit;
}

$message = trim($input['message']);
$idchat = intval($input['idchat']);
$email = $_SESSION['email'];

// Validate message
if (empty($message)) {
    echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
    exit;
}

if (strlen($message) > 500) {
    echo json_encode(['success' => false, 'error' => 'Message too long (max 500 characters)']);
    exit;
}

// Validate idchat
if ($idchat <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid chat ID']);
    exit;
}

// Check if user is admin
$stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$is_admin = ($user && $user['group'] === 'ADMIN') ? 1 : 0;

// For non-admin users, ensure they can only send to their own chat
if (!$is_admin) {
    $stmt = $conn->prepare("SELECT email FROM chats WHERE idchat = ? LIMIT 1");
    $stmt->bind_param("i", $idchat);
    $stmt->execute();
    $result = $stmt->get_result();
    $chat = $result->fetch_assoc();
    
    // If chat doesn't exist, create it for this user
    if (!$chat) {
        // This is a new chat, allow it for the current user
    } else if ($chat['email'] !== $email) {
        echo json_encode(['success' => false, 'error' => 'Access denied to this chat']);
        exit;
    }
}

// Insert message
$stmt = $conn->prepare("INSERT INTO chats (idchat, email, message, is_admin, dibaca, created_at) VALUES (?, ?, ?, ?, 0, NOW())");
if (!$stmt) {
    echo json_encode(['success' => false, 'error' => 'Prepare statement failed: ' . $conn->error]);
    exit;
}

$stmt->bind_param("issi", $idchat, $email, $message, $is_admin);

if ($stmt->execute()) {
    echo json_encode([
        'success' => true, 
        'message' => 'Message sent successfully',
        'idchat' => $idchat
    ]);
} else {
    echo json_encode(['success' => false, 'error' => 'Failed to send message: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
?> 