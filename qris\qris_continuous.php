<?php
// Script untuk monitoring QRIS secara continuous
// Set unlimited execution time untuk continuous monitoring
set_time_limit(0);
ini_set('max_execution_time', 0);

// Ignore user abort (ji<PERSON> dija<PERSON>an via browser dan user close tab)
ignore_user_abort(true);

require_once 'qris_monitor.php';

// Konfigurasi untuk continuous monitoring
$max_iterations = 0; // 0 = unlimited, set angka untuk limit iterations

echo "=== QRIS Continuous Monitor Started ===\n";
echo "Check interval: {$check_interval} seconds\n";
echo "Press Ctrl+C to stop\n\n";

$iteration = 0;
$start_time = time();

while (true) {
    $iteration++;
    $current_time = time();
    $running_time = $current_time - $start_time;
    
    echo "--- Iteration #{$iteration} (Running: " . gmdate("H:i:s", $running_time) . ") ---\n";
    
    try {
        monitorQRIS($discord_webhook_url, $cache_file);
        
        // Clear any accumulated memory
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        
        // Jika error, tunggu lebih lama sebelum coba lagi
        echo "Waiting extra 10 seconds due to error...\n";
        sleep(10);
    }
    
    // Cek limit iterations
    if ($max_iterations > 0 && $iteration >= $max_iterations) {
        echo "Reached maximum iterations ({$max_iterations}), stopping...\n";
        break;
    }
    
    // Tampilkan memory usage
    echo "Memory usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB\n";
    echo "Waiting {$check_interval} seconds before next check...\n\n";
    
    sleep($check_interval);
}

echo "=== QRIS Continuous Monitor Stopped ===\n";
?> 