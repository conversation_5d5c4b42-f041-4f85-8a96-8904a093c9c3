<?php
// Error reporting disabled for production security
error_reporting(0);
ini_set('display_errors', 0);
ini_set('display_errors', 1);

// Load konfigurasi
require_once 'config_qris.php';
require_once 'qris_logger.php';

// Fungsi untuk mendapatkan data QRIS
function getQRISData() {
    global $qris_api_url, $api_timeout;
    
    $curl = curl_init();
    
    curl_setopt_array($curl, array(
        CURLOPT_URL => $qris_api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => $api_timeout,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
    ));
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    curl_close($curl);
    
    if ($http_code == 200 && $response) {
        return json_decode($response, true);
    }
    
    return false;
}

// Fungsi untuk mengirim ke Discord webhook
function sendToDiscord($webhook_url, $data) {
    global $discord_config;
    
    // Tentukan warna berdasarkan tipe transaksi
    $embed_color = $discord_config['color']; // Default green
    $embed_title = $discord_config['title'];
    
    if (isset($data['type'])) {
        if ($data['type'] === 'CR') {
            $embed_color = 65280;  // Green untuk credit (masuk)
            $embed_title = "💰 Pembayaran QRIS Masuk!";
        } else {
            $embed_color = ********; // Red untuk debit (keluar)
            $embed_title = "💸 Transaksi QRIS Keluar!";
        }
    }
    
    $embed = array(
        "title" => $embed_title,
        "color" => $embed_color,
        "fields" => array(),
        "timestamp" => date('c'),
        "footer" => array(
            "text" => $discord_config['footer_text'] . " • " . date('d/m/Y H:i:s')
        )
    );
    
    // Tambahkan thumbnail berdasarkan bank
    if (isset($data['brand_name'])) {
        $bank_thumbnails = array(
            'BCA' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5c/Bank_Central_Asia.svg/100px-Bank_Central_Asia.svg.png',
            'BRI' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2e/BRI_2020.svg/100px-BRI_2020.svg.png',
            'BNI' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/55/BNI_logo.svg/100px-BNI_logo.svg.png',
            'MANDIRI' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ad/Bank_Mandiri_logo_2016.svg/100px-Bank_Mandiri_logo_2016.svg.png'
        );
        
        if (isset($bank_thumbnails[$data['brand_name']])) {
            $embed['thumbnail'] = array(
                "url" => $bank_thumbnails[$data['brand_name']]
            );
        }
    }
    
    // Tambahkan field berdasarkan data yang tersedia
    if (isset($data['amount'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['amount'] . " Jumlah",
            "value" => "Rp " . number_format($data['amount'], 0, ',', '.'),
            "inline" => true
        );
    }
    
    if (isset($data['date'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['time'] . " Tanggal & Waktu",
            "value" => $data['date'],
            "inline" => true
        );
    }
    
    if (isset($data['brand_name'])) {
        $embed['fields'][] = array(
            "name" => "🏦 Bank",
            "value" => $data['brand_name'],
            "inline" => true
        );
    }
    
    if (isset($data['buyer_reff'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['sender'] . " Pengirim",
            "value" => $data['buyer_reff'],
            "inline" => false
        );
    }
    
    if (isset($data['issuer_reff'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['reference'] . " Referensi Bank",
            "value" => "`" . $data['issuer_reff'] . "`",
            "inline" => true
        );
    }
    
    if (isset($data['type'])) {
        $type_emoji = $data['type'] === 'CR' ? '💰' : '💸';
        $type_text = $data['type'] === 'CR' ? 'Credit (Masuk)' : 'Debit (Keluar)';
        $embed['fields'][] = array(
            "name" => $type_emoji . " Tipe Transaksi",
            "value" => $type_text,
            "inline" => true
        );
    }
    
    if (isset($data['qris'])) {
        $qris_emoji = $data['qris'] === 'static' ? '📋' : '🔄';
        $embed['fields'][] = array(
            "name" => $qris_emoji . " Tipe QRIS",
            "value" => ucfirst($data['qris']),
            "inline" => true
        );
    }
    
    if (isset($data['balance'])) {
        $embed['fields'][] = array(
            "name" => "💳 Saldo Akhir",
            "value" => "Rp " . number_format($data['balance'], 0, ',', '.'),
            "inline" => false
        );
    }
    
    // Backward compatibility untuk field lama
    if (isset($data['reference']) && !isset($data['issuer_reff'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['reference'] . " Referensi",
            "value" => $data['reference'],
            "inline" => true
        );
    }
    
    if (isset($data['timestamp']) && !isset($data['date'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['time'] . " Waktu",
            "value" => $data['timestamp'],
            "inline" => true
        );
    }
    
    if (isset($data['sender_name']) && !isset($data['buyer_reff'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['sender'] . " Pengirim",
            "value" => $data['sender_name'],
            "inline" => true
        );
    }
    
    // Jika tidak ada field spesifik, tampilkan raw data
    if (empty($embed['fields'])) {
        $embed['fields'][] = array(
            "name" => $discord_config['emoji']['data'] . " Data Pembayaran",
            "value" => "```json\n" . json_encode($data, JSON_PRETTY_PRINT) . "\n```",
            "inline" => false
        );
    }
    
    $payload = array(
        "embeds" => array($embed)
    );
    
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $webhook_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        CURLOPT_TIMEOUT => 10
    ));
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    return $http_code >= 200 && $http_code < 300;
}

// Fungsi untuk membaca cache
function readCache($cache_file) {
    if (file_exists($cache_file)) {
        $content = file_get_contents($cache_file);
        return json_decode($content, true);
    }
    return array();
}

// Fungsi untuk menulis cache
function writeCache($cache_file, $data) {
    file_put_contents($cache_file, json_encode($data, JSON_PRETTY_PRINT));
}

// Fungsi untuk membuat unique ID dari data pembayaran
function createPaymentId($payment_data) {
    // Buat ID unik berdasarkan kombinasi data yang tersedia
    $id_parts = array();
    
    // Prioritas field baru (dari API response)
    if (isset($payment_data['issuer_reff'])) {
        $id_parts[] = $payment_data['issuer_reff'];
    }
    if (isset($payment_data['date'])) {
        $id_parts[] = $payment_data['date'];
    }
    if (isset($payment_data['amount'])) {
        $id_parts[] = $payment_data['amount'];
    }
    if (isset($payment_data['buyer_reff'])) {
        $id_parts[] = $payment_data['buyer_reff'];
    }
    
    // Fallback untuk field lama (backward compatibility)
    if (empty($id_parts)) {
        if (isset($payment_data['reference'])) {
            $id_parts[] = $payment_data['reference'];
        }
        if (isset($payment_data['timestamp'])) {
            $id_parts[] = $payment_data['timestamp'];
        }
        if (isset($payment_data['amount'])) {
            $id_parts[] = $payment_data['amount'];
        }
    }
    
    // Jika tidak ada field spesifik, gunakan hash dari seluruh data
    if (empty($id_parts)) {
        return md5(json_encode($payment_data));
    }
    
    return md5(implode('|', $id_parts));
}

// Main monitoring function
function monitorQRIS($discord_webhook_url, $cache_file) {
    echo "[" . date('Y-m-d H:i:s') . "] Checking QRIS payments...\n";
    
    // Validasi webhook URL
    if ($discord_webhook_url === 'YOUR_DISCORD_WEBHOOK_URL_HERE') {
        echo "ERROR: Please configure Discord webhook URL in the script!\n";
        return false;
    }
    
    // Ambil data QRIS terbaru
    $qris_data = getQRISData();
    
    if (!$qris_data) {
        echo "ERROR: Failed to fetch QRIS data\n";
        return false;
    }
    
    echo "QRIS data fetched successfully\n";
    
    // Baca cache pembayaran yang sudah diproses
    $processed_payments = readCache($cache_file);
    
    $new_payments = array();
    
    // Cek apakah data berupa array dari payments atau single payment
    $payments = array();
    
    if (isset($qris_data['data']) && is_array($qris_data['data'])) {
        $payments = $qris_data['data'];
    } elseif (isset($qris_data['payments']) && is_array($qris_data['payments'])) {
        $payments = $qris_data['payments'];
    } elseif (is_array($qris_data)) {
        // Jika response langsung berupa array payments
        $payments = $qris_data;
    } else {
        // Jika response berupa single payment object
        $payments = array($qris_data);
    }
    
    foreach ($payments as $payment) {
        $payment_id = createPaymentId($payment);
        
        // Cek apakah pembayaran ini sudah pernah diproses
        if (!in_array($payment_id, $processed_payments)) {
            $new_payments[] = $payment;
            $processed_payments[] = $payment_id;
            echo "New payment found: " . $payment_id . "\n";
        }
    }
    
    // Kirim notifikasi untuk pembayaran baru
    if (!empty($new_payments)) {
        foreach ($new_payments as $payment) {
            $payment_id = createPaymentId($payment);
            
            // Log ke database terlebih dahulu
            $logged = logQRISTransaction($payment);
            if ($logged) {
                echo "Payment logged to database: " . $payment_id . "\n";
            } else {
                echo "Failed to log payment to database: " . $payment_id . "\n";
            }
            
            // Kirim ke Discord
            $success = sendToDiscord($discord_webhook_url, $payment);
            if ($success) {
                echo "Payment notification sent to Discord successfully\n";
                
                // Update status webhook di database
                if ($logged) {
                    updateQRISWebhookStatus($payment_id, true);
                }
            } else {
                echo "Failed to send payment notification to Discord\n";
                
                // Update status webhook sebagai gagal
                if ($logged) {
                    updateQRISWebhookStatus($payment_id, false);
                }
            }
            
            // Delay kecil untuk menghindari rate limit
            sleep(1);
        }
        
        // Update cache
        writeCache($cache_file, $processed_payments);
        echo "Cache updated with " . count($new_payments) . " new payments\n";
    } else {
        echo "No new payments found\n";
    }
    
    return true;
}

// Jika dijalankan langsung (bukan di-include)
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Mode command line atau single run
    if (php_sapi_name() === 'cli') {
        echo "=== QRIS Payment Monitor ===\n";
        monitorQRIS($discord_webhook_url, $cache_file);
    } else {
        // Mode web - tampilkan status
        header('Content-Type: application/json');
        
        $result = monitorQRIS($discord_webhook_url, $cache_file);
        
        echo json_encode(array(
            'success' => $result,
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $result ? 'Monitoring completed successfully' : 'Monitoring failed'
        ));
    }
}
?> 