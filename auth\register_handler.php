<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set proper headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Set error reporting for production security
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Rate limiting: Allow only 5 requests per minute per IP
    $ip = $_SERVER['REMOTE_ADDR'];
    $time = time();
    $limit = 5;
    $interval = 60; // 60 seconds

    if (!isset($_SESSION['requests'])) {
        $_SESSION['requests'] = [];
    }

    // Remove old requests
    $_SESSION['requests'] = array_filter($_SESSION['requests'], function($timestamp) use ($time, $interval) {
        return ($time - $timestamp) < $interval;
    });

    if (count($_SESSION['requests']) >= $limit) {
        echo json_encode(['error' => 'Too many requests, please try again later.']);
        exit;
    }

    // Add current request timestamp
    $_SESSION['requests'][] = $time;

    // Include database with error handling
    include '../database/database.php';
    
    // Include Discord webhook
    include '../includes/discord_webhook.php';
    
    // Initialize Discord webhook
    $discordWebhook = new DiscordWebhook('https://discordapp.com/api/webhooks/1390490529904591000/uOiNXwHt6lL66vS-Gx3wSC9jcg2yN3aH0PPJWRa0ZJtF8MSdYBeydMXg41E9_-6Iw9pd');
    
    // Check if database connection exists
    if (!isset($conn) || $conn->connect_error) {
        echo json_encode(['error' => 'Database connection failed. Please try again.']);
        exit;
    }

    // Validate input
    if (!isset($_POST['email'], $_POST['password'], $_POST['captcha'], $_POST['csrf_token'])) {
        echo json_encode(['error' => 'All fields are required!']);
        exit;
    }

    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        echo json_encode(['error' => 'Invalid CSRF token!']);
        exit;
    }

    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $captcha = $_POST['captcha'];

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['error' => 'Invalid email format!']);
        exit;
    }

    // Check password length
    if (strlen($password) < 6) {
        echo json_encode(['error' => 'Password must be at least 6 characters long!']);
        exit;
    }

    // Check if CAPTCHA session exists
    if (!isset($_SESSION['captcha'])) {
        echo json_encode(['error' => 'CAPTCHA session expired. Please refresh the page.']);
        exit;
    }

    // Check CAPTCHA
    if ($captcha != $_SESSION['captcha']) {
        echo json_encode(['error' => 'CAPTCHA salah!']);
        exit;
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT email FROM users WHERE email = ?");
    if (!$stmt) {
        echo json_encode(['error' => 'Database error. Please try again.']);
        exit;
    }

    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo json_encode(['error' => 'Email sudah terdaftar!']);
        exit;
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new user
    $group = 'USER';
    $stmt = $conn->prepare("INSERT INTO users (email, password, `group`) VALUES (?, ?, ?)");
    if (!$stmt) {
        echo json_encode(['error' => 'Database error. Please try again.']);
        exit;
    }

    $stmt->bind_param("sss", $email, $hashedPassword, $group);
    
    if ($stmt->execute()) {
        // Send successful registration log to Discord
        $discordWebhook->sendRegisterLog($email, true);
        echo json_encode(['success' => true, 'message' => 'Berhasil registrasi, Silahkan login']);
    } else {
        // Send failed registration log to Discord
        $discordWebhook->sendRegisterLog($email, false);
        echo json_encode(['error' => 'Terjadi kesalahan saat mendaftar: ' . $stmt->error]);
    }

} catch (Exception $e) {
    // Log error for debugging
    error_log('Registration error: ' . $e->getMessage());
    echo json_encode(['error' => 'An error occurred. Please try again.']);
    exit;
}
?>