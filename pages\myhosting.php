<?php
// Start session if not already started aweawe
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../auth/login');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Dopminer.com - MyHosting</title>
    <meta content="" name="description">
          <meta content="" name="keywords">

      <!-- Favicon -->
      <link href="https://dopminer.com/Gambar/Nobackgroundww-Photoroom.png" rel="icon">

      <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Jost:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="../assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="../assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="../assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="../assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        body {
            font-family: "Open Sans", sans-serif;
            background-color: #0a0e27;
            color: #ffffff;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: "Jost", sans-serif;
        }

        .myhosting {
            padding: 120px 0 2rem 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://dopminer.com/Gambar/1063438-free-google-data-center-wallpaper-3840x2160.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .section-title {
            color: #4fc3f7;
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 50px;
            text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
        }

        .section-subtitle {
            color: #b0b0b0;
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .hosting-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            margin-bottom: 25px;
            padding: 30px;
            text-align: left;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            color: white;
        }

        .hosting-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(79, 195, 247, 0.2);
            border-color: #4fc3f7;
        }

        .hosting-card h3 {
            margin-top: 10px;
            text-align: center;
            color: #4fc3f7;
            font-weight: 600;
            font-size: 1.4rem;
        }

        .hosting-card .btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            margin-top: 20px;
            width: 100%;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .hosting-card .btn:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
        }

        .hosting-card p {
            margin: 10px 0;
            font-size: 0.95rem;
            color: #e0e0e0;
        }

        .hosting-card .status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }

        .hosting-card .status span {
            margin-left: 8px;
            font-weight: 600;
        }

        .status-active {
            color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }

        .status-expired {
            color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }

        .status-soon {
            color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }

        .type-badge {
            display: inline-block;
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .price-info {
            background: rgba(79, 195, 247, 0.1);
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 4px solid #4fc3f7;
        }

        .expired-warning {
            background: rgba(244, 67, 54, 0.1);
            border-left: 4px solid #f44336;
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
        }

        .no-hosting {
            text-align: center;
            padding: 60px 40px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            border: 2px dashed #4fc3f7;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            margin: 0 auto;
        }

        .no-hosting .icon-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border-radius: 50%;
            margin: 0 auto 25px auto;
            box-shadow: 0 10px 30px rgba(79, 195, 247, 0.4);
            position: relative;
            overflow: hidden;
        }

        .no-hosting .icon-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .no-hosting .icon-container i {
            font-size: 40px;
            color: white;
            z-index: 2;
            position: relative;
        }

        .no-hosting h3 {
            color: #4fc3f7;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .no-hosting p {
            color: #b0b0b0;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .no-hosting .btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .no-hosting .btn:hover {
            background: linear-gradient(45deg, #3a9bc1, #1976d2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.5);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .myhosting {
                padding: 100px 0 1rem 0;
            }
            
            .hosting-card {
                margin: 0 1rem 2rem 1rem;
                padding: 20px;
            }
            
            .no-hosting {
                margin: 0 1rem;
                padding: 40px 20px;
            }
        }

        .no-hosting h3 {
            color: #46aedf;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .no-hosting p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .no-hosting .btn {
            background: linear-gradient(45deg, #46aedf, #5bc0de);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(70, 174, 223, 0.3);
        }

        .no-hosting .btn:hover {
            background: linear-gradient(45deg, #3a9bc1, #4a9bc9);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(70, 174, 223, 0.4);
            color: white;
            text-decoration: none;
        }

        .no-hosting .btn i {
            font-size: 18px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .no-hosting {
                padding: 60px 30px;
                margin: 0 15px;
            }

            .no-hosting .icon-container {
                width: 100px;
                height: 100px;
                margin-bottom: 25px;
            }

            .no-hosting .icon-container i {
                font-size: 40px;
            }

            .no-hosting h3 {
                font-size: 24px;
                margin-bottom: 15px;
            }

            .no-hosting p {
                font-size: 15px;
                margin-bottom: 25px;
            }

            .no-hosting .btn {
                padding: 12px 25px;
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .no-hosting {
                padding: 50px 20px;
            }

            .no-hosting .icon-container {
                width: 80px;
                height: 80px;
                margin-bottom: 20px;
            }

            .no-hosting .icon-container i {
                font-size: 32px;
            }

            .no-hosting h3 {
                font-size: 20px;
            }

            .no-hosting p {
                font-size: 14px;
            }

            .no-hosting .btn {
                padding: 10px 20px;
                font-size: 14px;
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>

<body>

    <?php include '../components/navbar.php'; ?>

    <!-- ======= MyHosting Section ======= -->
    <section id="myhosting" class="myhosting">
        <div class="container" data-aos="fade-up">

            <div class="section-title">
                MY HOSTING
            </div>
            <div class="section-subtitle">
                Kelola semua layanan hosting Anda dengan mudah
            </div>
            
            <div class="row justify-content-center">
                <?php
                include '../database/database.php';

                $email = $_SESSION['email'];

                // Fungsi untuk menghitung sisa waktu
                function timeLeft($expired)
                {
                    $now = new DateTime();
                    $expiry = new DateTime($expired);
                    $interval = $now->diff($expiry);
                    
                    if ($expiry < $now) {
                        return "Expired";
                    }
                    
                    $days = $interval->days;
                    if ($days > 30) {
                        return $interval->format('%m bulan %d hari');
                    } elseif ($days > 0) {
                        return $interval->format('%d hari %h jam');
                    } else {
                        return $interval->format('%h jam %i menit');
                    }
                }

                // Fungsi untuk mendapatkan status berdasarkan expired date
                function getStatus($expired)
                {
                    $now = new DateTime();
                    $expiry = new DateTime($expired);
                    $diff = $now->diff($expiry);
                    
                    if ($expiry < $now) {
                        return ['status' => '❌ Expired ❌', 'class' => 'status-expired', 'icon' => ''];
                    } elseif ($diff->days <= 7) {
                        return ['status' => '⚠️ Akan Berakhir ⚠️', 'class' => 'status-soon', 'icon' => ''];
                    } else {
                        return ['status' => '✅ Aktif ✅', 'class' => 'status-active', 'icon' => ''];
                    }
                }

                // Fungsi untuk mendapatkan gambar berdasarkan type hosting
                function getHostingImage($typehosting)
                {
                    $images = [
                        'FIVEM' => 'https://dopminer.com/Gambar/1410524073-gta-v-1968268344.jpg',
                        'SAMP' => 'https://dopminer.com/Gambar/download_1.jpeg',
                        'VPS' => 'https://dopminer.com/Gambar/CnP_05102024_072848.png',
                        'PMA' => 'https://dopminer.com/Gambar/CnP_05102024_073047.png',
                        'REDM' => 'https://dopminer.com/Gambar/Red-Dsdasdasdead-Redemption-2-Arthur-Wallpaper-4K-172829.jpg',
                        'BOT' => 'https://dopminer.com/Gambar/bot-hosting.png',
                        'APP' => 'https://dopminer.com/Gambar/app-hosting.png'
                    ];
                    
                    return isset($images[$typehosting]) ? $images[$typehosting] : 'https://dopminer.com/Gambar/default-hosting.png';
                }

                // Fungsi untuk menghilangkan emoji dan karakter khusus
                function cleanText($text) {
                    // Remove all 4-byte unicode characters (most emojis)
                    $text = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $text);
                    
                    // Remove common emoji ranges
                    $text = preg_replace('/[\x{1F000}-\x{1FFFF}]/u', '', $text);
                    $text = preg_replace('/[\x{2600}-\x{27BF}]/u', '', $text);
                    
                    // Remove question marks (invalid characters)
                    $text = str_replace('?', '', $text);
                    
                    // Remove other problematic characters
                    $text = preg_replace('/[^\p{L}\p{N}\s\-\.]/u', '', $text);
                    
                    // Clean up spaces
                    $text = preg_replace('/\s+/', ' ', $text);
                    $text = trim($text);
                    
                    return $text;
                }

                // Fungsi untuk mendapatkan logo berdasarkan type hosting
                function getHostingLogo($typehosting)
                {
                    $logos = [
                        'FIVEM' => '',
                        'SAMP' => '',
                        'REDM' => '',
                        'VPS' => '',
                        'PMA' => '',
                        'BOT' => '',
                        'APP' => ''
                    ];
                    
                    return isset($logos[$typehosting]) ? $logos[$typehosting] : '';
                }
                
                // Check if database connection exists
                if (!isset($conn) || !$conn) {
                    echo '<div class="col-12"><div class="alert alert-danger">Database connection error. Please try again later.</div></div>';
                } else {
                    try {
                        // Query data dari tabel data_client
                        $stmt = $conn->prepare("SELECT * FROM data_client WHERE email = ? ORDER BY expired ASC");
                        if (!$stmt) {
                            throw new Exception("Failed to prepare statement");
                        }
                        $stmt->bind_param("s", $email);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                            while ($hosting = $result->fetch_assoc()) {
                                $statusInfo = getStatus($hosting['expired']);
                                $timeRemaining = timeLeft($hosting['expired']);
                                $hostingImage = getHostingImage($hosting['typehosting']);
                                $hostingLogo = getHostingLogo($hosting['typehosting']);
                                ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="hosting-card">
                                        <div style="text-align: center; margin-bottom: 20px;">
                                            <div class="type-badge"><?php echo htmlspecialchars($hosting['typehosting']); ?></div>
                                        </div>
                                        
                                        <h3 style="text-align: center; margin-bottom: 20px;">
                                            <?php echo htmlspecialchars(cleanText($hosting['namakota'])); ?>
                                        </h3>

                                        <div class="status <?php echo $statusInfo['class']; ?>">
                                            <span><?php echo $statusInfo['icon']; ?></span>
                                            <span><?php echo $statusInfo['status']; ?></span>
                                        </div>

                                        <?php if ($statusInfo['status'] == 'Expired'): ?>
                                            <div class="expired-warning">
                                                <strong>⚠️ Hosting Telah Berakhir ⚠️</strong><br>
                                                <small>Silakan hubungi admin untuk perpanjangan</small>
                                            </div>
                                        <?php else: ?>
                                            <div class="price-info">
                                                <strong>💰 Harga Bulanan:</strong> Rp <?php echo number_format($hosting['hargabulanan'], 0, ',', '.'); ?><br>
                                                <strong>⏰ Sisa Waktu:</strong> <?php echo $timeRemaining; ?>
                                            </div>
                                        <?php endif; ?>

                                        <p><strong>📦 Paket Hosting:</strong> <?php 
                                            $paket = strtoupper($hosting['pakethosting']);
                                            $excludeTerms = ['FIVEM', 'SAMP', 'VPS', 'PMA', 'REDM'];
                                            foreach($excludeTerms as $term) {
                                                $paket = str_replace($term, '', $paket);
                                            }
                                            echo htmlspecialchars(trim($paket));
                                        ?></p>
                                        
                                        <?php if (!empty($hosting['iphosting'])): ?>
                                            <p><strong>🌐 IP Hosting:</strong> 
                                                <code style="background: rgba(255,255,255,0.1); padding: 2px 6px; border-radius: 4px;">
                                                    <?php echo htmlspecialchars($hosting['iphosting']); ?>
                                                </code>
                                            </p>
                                        <?php endif; ?>

                                        <a href="server-details?id=<?php echo $hosting['id']; ?>" class="btn">
                                            <i class="bi bi-gear"></i> Kelola Hosting
                                        </a>
                                    </div>
                                </div>
                                <?php
                            }
                        } else {
                            ?>
                            <div class="col-12">
                                <div class="no-hosting">
                                    <div class="icon-container">
                                        <i class="bi bi-server"></i>
                                    </div>
                                    <h3>Belum Ada Hosting</h3>
                                    <p>Anda belum memiliki layanan hosting aktif. Silakan pesan hosting untuk memulai dan nikmati layanan terbaik kami!</p>
                                    <a href="../pages/games" class="btn">
                                        <i class="bi bi-plus-circle"></i>
                                        <span>Pesan Hosting Sekarang</span>
                                    </a>
                                </div>
                            </div>
                            <?php
                        }

                        $stmt->close();
                        $conn->close();
                    } catch (Exception $e) {
                        echo '<div class="col-12"><div class="alert alert-danger">Error loading hosting data: ' . htmlspecialchars($e->getMessage()) . '</div></div>';
                    }
                }
                ?>
            </div>
        </div>
    </section>

    <?php include '../components/footer.php'; ?>



    <!-- Vendor JS Files -->
    <script src="../assets/vendor/aos/aos.js"></script>
    <script src="../assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="../assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="../assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="../assets/vendor/waypoints/noframework.waypoints.js"></script>
    <script src="../assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="../assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="../assets/js/main.js"></script>

</body>

</html>