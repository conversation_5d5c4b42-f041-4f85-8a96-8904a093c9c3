<?php
/**
 * Clear Rate Limit - Reset login attempts
 * Redirect back to login after clearing
 */

// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include security functions
require_once __DIR__ . '/../includes/security.php';

// Get client IP
$clientIP = $_SERVER['REMOTE_ADDR'];

// Clear rate limiting for this IP
$key = 'rate_limit_' . md5($clientIP);
if (isset($_SESSION[$key])) {
    unset($_SESSION[$key]);
    
    // Log the rate limit reset
    logSecurityEvent('rate_limit_reset', ['ip' => $clientIP]);
}

// Set success message for login page
$_SESSION['rate_limit_cleared'] = true;

// Redirect back to login page
header('Location: login');
exit;
?> 