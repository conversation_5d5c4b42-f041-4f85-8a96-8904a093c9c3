<?php
header('Content-Type: application/json');

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['url'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing URL parameter']);
    exit;
}

// URL Webhook Discord
$webhook_url = "https://discord.com/api/webhooks/1262808329126940683/SQvKXuJVf5_gtyvSrLBO4PNV6RtbbEZw0pDhhUn3onjpw1ULZONYfvQaV7I0Xv303M1I";

// Get visitor info
$user_ip = $_SERVER['REMOTE_ADDR'];
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// Get IP info asynchronously
$ip_info = [];
$ch = curl_init("http://ipinfo.io/{$user_ip}/json");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 2); // 2 second timeout
$response = curl_exec($ch);
curl_close($ch);

if ($response) {
    $ip_info = json_decode($response, true) ?: [];
}

// Prepare Discord embed
$log_data = [
    'embeds' => [
        [
            'title' => '📇 Visitor Logs',
            'description' => sprintf(
                "🌐 **IP:** %s\n🏢 **ISP:** %s\n🌍 **Region:** %s\n🔗 **URL:** %s",
                substr($user_ip, 0, strlen($user_ip)/2) . str_repeat('*', strlen($user_ip)/2),
                $ip_info['org'] ?? 'Unknown ISP',
                $ip_info['country'] ?? 'Unknown Region',
                $data['url']
            ),
            'color' => hexdec('429cff')
        ]
    ]
];

// Send to Discord webhook asynchronously
$ch = curl_init($webhook_url);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($log_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 2); // 2 second timeout
curl_exec($ch);
curl_close($ch);

// Return success
echo json_encode(['success' => true]); 