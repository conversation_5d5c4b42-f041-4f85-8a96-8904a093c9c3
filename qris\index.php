<?php
/**
 * QRIS Monitor Dashboard
 * Dashboard sederhana untuk mengakses semua fitur QRIS Monitor
 */

// Load konfigurasi
require_once 'config_qris.php';

// Cek apakah ada action yang diminta
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'config':
        header('Location: config_qris.php');
        exit;
        
    case 'monitor':
        header('Location: qris_monitor.php');
        exit;
        
    case 'continuous':
        header('Location: qris_continuous.php');
        exit;
        
    case 'run':
        header('Location: run_qris_monitor.php');
        exit;
        
    case 'readme':
        if (file_exists('README_QRIS_MONITOR.md')) {
            $readme = file_get_contents('README_QRIS_MONITOR.md');
            echo '<pre style="background:#f4f4f4;padding:20px;border-radius:5px;max-width:100%;overflow-x:auto;">';
            echo htmlspecialchars($readme);
            echo '</pre>';
        }
        exit;
        
    case 'clear_cache':
        if (file_exists($cache_file)) {
            unlink($cache_file);
            $message = "✅ Cache berhasil dihapus!";
        } else {
            $message = "ℹ️ Cache file tidak ditemukan.";
        }
        break;
        
    case 'view_cache':
        if (file_exists($cache_file)) {
            $cache_content = file_get_contents($cache_file);
            $cache_data = json_decode($cache_content, true);
            echo '<h3>📄 Cache Content:</h3>';
            echo '<pre style="background:#f4f4f4;padding:15px;border-radius:5px;">';
            echo json_encode($cache_data, JSON_PRETTY_PRINT);
            echo '</pre>';
            echo '<p><a href="?">← Kembali ke Dashboard</a></p>';
        } else {
            echo '<h3>📄 Cache Content:</h3>';
            echo '<p>Cache file belum ada atau kosong.</p>';
            echo '<p><a href="?">← Kembali ke Dashboard</a></p>';
        }
        exit;
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QRIS Monitor Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .status-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .status-card.success { border-left: 4px solid #28a745; }
        .status-card.warning { border-left: 4px solid #ffc107; }
        .status-card.error { border-left: 4px solid #dc3545; }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover { 
            background: #0056b3; 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #1e7e34; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.warning:hover { background: #e0a800; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        
        .info-section {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .actions {
                grid-template-columns: 1fr;
            }
            .status-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Sweetalert Custom CSS -->
    <link rel="stylesheet" href="../assets/css/sweetalert-custom.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 QRIS Monitor</h1>
            <p>Dashboard Monitoring Pembayaran QRIS</p>
        </div>
        
        <div class="content">
            <?php if (isset($message)): ?>
                <div class="message"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>
            
            <!-- Status Cards -->
            <div class="status-cards">
                <?php
                $config_errors = validateConfig();
                $cache_exists = file_exists($cache_file);
                $cache_size = $cache_exists ? count(json_decode(file_get_contents($cache_file), true) ?: []) : 0;
                ?>
                
                <div class="status-card <?php echo empty($config_errors) ? 'success' : 'error'; ?>">
                    <h3><?php echo empty($config_errors) ? '✅' : '❌'; ?> Konfigurasi</h3>
                    <p><?php echo empty($config_errors) ? 'Siap digunakan' : count($config_errors) . ' error ditemukan'; ?></p>
                </div>
                
                <div class="status-card <?php echo $cache_exists ? 'success' : 'warning'; ?>">
                    <h3><?php echo $cache_exists ? '📄' : '📝'; ?> Cache</h3>
                    <p><?php echo $cache_exists ? $cache_size . ' pembayaran tercache' : 'Belum ada cache'; ?></p>
                </div>
                
                <div class="status-card success">
                    <h3>🕐 Waktu</h3>
                    <p><?php echo date('d/m/Y H:i:s'); ?></p>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <h3>🚀 Aksi Monitoring</h3>
            <div class="actions">
                <a href="?action=monitor" class="btn success">▶️ Single Run</a>
                <a href="qris_web_monitor.php" class="btn warning">🔴 Live Monitor (Web)</a>
                <a href="?action=continuous" class="btn" style="background: #6c757d;">🔄 Continuous (CLI)</a>
                <a href="?action=config" class="btn">⚙️ Cek Konfigurasi</a>
            </div>
            
            <h3>🔧 Pengaturan & Utilitas</h3>
            <div class="actions">
                <a href="test_webhook.php" class="btn" style="background: #ff6b6b;">🎯 Preview Webhook</a>
                <a href="?action=view_cache" class="btn">👁️ Lihat Cache</a>
                <a href="?action=clear_cache" class="btn danger" onclick="return confirm('Yakin ingin hapus cache? Semua pembayaran akan dikirim ulang!')">🗑️ Hapus Cache</a>
                <a href="?action=readme" class="btn">📖 Dokumentasi</a>
            </div>
            
            <!-- Info Section -->
            <div class="info-section">
                <h4>ℹ️ Informasi Sistem</h4>
                <ul>
                    <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                    <li><strong>cURL:</strong> <?php echo function_exists('curl_init') ? '✅ Tersedia' : '❌ Tidak tersedia'; ?></li>
                    <li><strong>Cache File:</strong> <?php echo $cache_file; ?></li>
                    <li><strong>Check Interval:</strong> <?php echo $check_interval; ?> detik</li>
                    <li><strong>API Timeout:</strong> <?php echo $api_timeout; ?> detik</li>
                </ul>
                
                <?php if (!empty($config_errors)): ?>
                    <h4>❌ Error Konfigurasi:</h4>
                    <ul>
                        <?php foreach ($config_errors as $error): ?>
                            <li style="color: #dc3545;"><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <!-- Quick Commands -->
            <div class="info-section" style="margin-top: 20px;">
                <h4>⚡ Quick Commands</h4>
                <p><strong>Command Line:</strong></p>
                <code>php qris_monitor.php</code> - Single run<br>
                <code>php qris_continuous.php</code> - Continuous monitoring<br>
                <code>php config_qris.php</code> - Check configuration<br><br>
                
                <p><strong>Cron Job (setiap 1 menit):</strong></p>
                <code>* * * * * php <?php echo __DIR__ . '/run_qris_monitor.php'; ?></code>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Sweetalert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        function confirmClearCache(event) {
            event.preventDefault();
            
            Swal.fire({
                title: 'Hapus Cache?',
                text: 'Yakin ingin hapus cache? Semua pembayaran akan dikirim ulang!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus Cache!',
                cancelButtonText: 'Batal',
                background: 'rgba(0, 0, 0, 0.9)',
                color: '#fff'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '?action=clear_cache';
                }
            });
            
            return false;
        }
    </script>
</body>
</html> 