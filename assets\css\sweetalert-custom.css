/* Sweetalert Custom Dark Theme Styling */
.swal2-container {
    z-index: 9999 !important;
}

.swal2-popup {
    border-radius: 25px !important;
    background: rgb(12 19 31 / 58%) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 2em !important;
    position: relative !important;
    z-index: 10000 !important;
}

.swal2-close {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    width: 30px !important;
    height: 30px !important;
    padding: 0 !important;
    z-index: 10001 !important;
    color: #fff !important;
    background: transparent !important;
    border: none !important;
    outline: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    opacity: 1 !important;
}

.swal2-close:hover {
    color: #d33 !important;
    transform: rotate(90deg) !important;
}

.swal2-title {
    color: #fff !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-bottom: 0.5em !important;
    padding-right: 30px !important;
}

.swal2-html-container {
    color: #ccc !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    margin-top: 0 !important;
}

.swal2-icon {
    border-color: rgba(255, 255, 255, 0.3) !important;
    margin: 1.5em auto !important;
}

.swal2-actions {
    margin-top: 2em !important;
    z-index: 10001 !important;
}

.swal2-confirm,
.swal2-cancel {
    position: relative !important;
    z-index: 10001 !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    opacity: 1 !important;
}

.swal2-confirm {
    border-radius: 50px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 3px 16px rgba(211, 51, 51, 0.3) !important;
    transition: all 0.3s ease !important;
    background: #d33 !important;
    color: #fff !important;
}

.swal2-confirm:hover {
    background: #c22 !important;
    transform: translateY(-1px) !important;
}

.swal2-cancel {
    border-radius: 50px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.swal2-cancel:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* Backdrop styling */
.swal2-backdrop-show {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(5px) !important;
    z-index: 9998 !important;
}

/* Make sure all interactive elements are clickable */
.swal2-container,
.swal2-popup,
.swal2-header,
.swal2-content,
.swal2-actions,
.swal2-close {
    pointer-events: auto !important;
}

/* Animation */
.swal2-show {
    animation: swal2-show 0.3s ease-out !important;
}

.swal2-hide {
    animation: swal2-hide 0.3s ease-in !important;
}

@keyframes swal2-show {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes swal2-hide {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

/* Fix untuk z-index dan pointer events */
.swal2-container {
    z-index: 9999 !important;
}

.swal2-container, 
.swal2-popup,
.swal2-header,
.swal2-content,
.swal2-actions,
.swal2-close {
    pointer-events: auto !important;
} 