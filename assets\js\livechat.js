// Livechat Popup JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get configuration from PHP
    const config = window.livechatConfig || {};
    const isLoggedIn = config.isLoggedIn || false;
    let idchat = config.idchat || 0;
    const currentEmail = config.currentEmail || '';
    const isAdmin = config.isAdmin || false;
    const assetPath = config.assetPath || 'assets/';
    
    // API path detection
    let apiPath = '';
    const currentPath = window.location.pathname;
    if (currentPath.includes('/admin/')) {
        apiPath = '../api/livechat/';
    } else if (currentPath.includes('/pages/')) {
        apiPath = '../api/livechat/';
    } else {
        apiPath = 'api/livechat/';
    }
    
    // Global variables
    let isPopupOpen = false;
    let autoScrollEnabled = true;
    let lastUnreadCount = 0;
    let lastAdminUnreadCount = 0;
    let notificationInterval = null;
    let notificationAudio = null;
    let inboxView = true;
    let currentChatUser = null;
    let isLoadingChatUsers = false;
    
    // DOM elements
    const toggle = document.getElementById('livechat-toggle');
    const popup = document.getElementById('livechat-popup');
    const closeBtn = document.getElementById('close-chat');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    const chatMessages = document.getElementById('chat-messages');
    const unreadBadge = document.getElementById('unread-badge');
    const toggleUnreadBadge = document.getElementById('toggle-unread-badge');
    const backBtn = document.getElementById('back-to-inbox');
    const autoScrollToggle = document.getElementById('auto-scroll-toggle');
    
    // Initialize notification audio
    function initializeAudio() {
        try {
            notificationAudio = new Audio(assetPath + 'sounds/notification.mp3');
            notificationAudio.volume = 0.5;
        } catch (error) {
            console.log('Audio not available:', error);
        }
    }
    
    // Initialize livechat
    function initializeLivechat() {
        initializeAudio();
        
        // Set initial view for admin
        if (isAdmin) {
            showInboxView();
        }
        
        // Update auto scroll button
        updateAutoScrollButton();
        
        // Add scroll listener to chat messages
        if (chatMessages) {
            chatMessages.addEventListener('scroll', checkIfUserScrolledUp);
        }
        
        // Event listeners
        if (toggle) {
            toggle.addEventListener('click', togglePopup);
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', closePopup);
        }
        
        if (backBtn) {
            backBtn.addEventListener('click', function() {
                showInboxView();
                loadChatUsers();
            });
        }
        
        if (autoScrollToggle) {
            autoScrollToggle.addEventListener('click', toggleAutoScroll);
        }
        
        // Initialize based on login status
        if (!isLoggedIn) {
            // Show login required panel
            return;
        }
        
        // Load initial data
        if (isAdmin) {
            loadChatUsers();
        } else if (idchat) {
            loadMessages();
        }
    }
    
    // Notification functions
    function startNotificationLoop() {
        if (notificationInterval) return;
        
        let count = 0;
        notificationInterval = setInterval(() => {
            playNotificationSound();
            count++;
            if (count >= 3) {
                stopNotificationLoop();
            }
        }, 2000);
    }
    
    function stopNotificationLoop() {
        if (notificationInterval) {
            clearInterval(notificationInterval);
            notificationInterval = null;
        }
    }
    
    function playNotificationSound() {
        if (notificationAudio) {
            try {
                notificationAudio.currentTime = 0;
                notificationAudio.play().catch(e => {
                    console.log('Audio play failed:', e);
                });
            } catch (error) {
                console.log('Audio error:', error);
            }
        }
    }
    
    // Popup functions
    function togglePopup() {
        if (isPopupOpen) {
            closePopup();
        } else {
            openPopup();
        }
    }
    
    function openPopup() {
        if (!popup) return;
        
        isPopupOpen = true;
        popup.classList.add('show');
        
        // Stop notifications when popup opens
        stopNotificationLoop();
        
        // Hide badges when popup opens
        if (unreadBadge) unreadBadge.style.display = 'none';
        if (toggleUnreadBadge) toggleUnreadBadge.style.display = 'none';
        
        // Focus message input if not admin or not in inbox view
        if (messageInput && (!isAdmin || !inboxView)) {
            setTimeout(() => {
                messageInput.focus();
            }, 300);
        }
        
        // Mark messages as read if in chat view
        if (!isAdmin || !inboxView) {
            markMessagesAsRead();
        }
        
        // Load data based on user type
        if (isAdmin && inboxView) {
            loadChatUsers();
        } else if (idchat) {
            loadMessages();
        }
    }
    
    function closePopup() {
        if (!popup) return;
        
        isPopupOpen = false;
        popup.classList.remove('show');
        
        // Add bounce animation to toggle
        if (toggle) {
            toggle.classList.add('bounce');
            setTimeout(() => {
                toggle.classList.remove('bounce');
            }, 400);
        }
    }
    
    // Auto scroll functions
    function toggleAutoScroll() {
        autoScrollEnabled = !autoScrollEnabled;
        updateAutoScrollButton();
        
        if (autoScrollEnabled) {
            scrollToBottom();
        }
    }
    
    function updateAutoScrollButton() {
        const autoScrollToggle = document.getElementById('auto-scroll-toggle');
        if (!autoScrollToggle) return;
        
        const icon = autoScrollToggle.querySelector('i');
        if (autoScrollEnabled) {
            autoScrollToggle.classList.remove('disabled');
            autoScrollToggle.title = 'Auto Scroll: ON (Klik untuk OFF)';
            if (icon) {
                icon.className = 'bi bi-arrow-down-circle-fill';
            }
        } else {
            autoScrollToggle.classList.add('disabled');
            autoScrollToggle.title = 'Auto Scroll: OFF (Klik untuk ON)';
            if (icon) {
                icon.className = 'bi bi-arrow-down-circle';
            }
        }
    }
    
    function scrollToBottom() {
        if (!chatMessages || !autoScrollEnabled) return;
        
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }
    
    function checkIfUserScrolledUp() {
        if (!chatMessages) return;
        
        const scrollTop = chatMessages.scrollTop;
        const scrollHeight = chatMessages.scrollHeight;
        const clientHeight = chatMessages.clientHeight;
        
        // If user scrolled up more than 50px from bottom, disable auto scroll
        if (scrollHeight - scrollTop - clientHeight > 50) {
            if (autoScrollEnabled) {
                autoScrollEnabled = false;
                updateAutoScrollButton();
                
                // Add pulse animation to indicate auto scroll is disabled
                const autoScrollToggle = document.getElementById('auto-scroll-toggle');
                if (autoScrollToggle) {
                    autoScrollToggle.classList.add('pulse');
                    setTimeout(() => {
                        autoScrollToggle.classList.remove('pulse');
                    }, 3000);
                }
            }
        }
    }
    
    // Send message
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message === '') return;
        
        // Stop notifications when user sends a message
        stopNotificationLoop();
        
        // Disable send button temporarily
        if (sendBtn) {
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<i class="bi bi-clock"></i>';
        }
        
        // Use JSON for API request
        const messageData = {
            message: message,
            idchat: idchat
        };
        
        fetch(apiPath + 'send_message.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(messageData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // Re-enable send button
            if (sendBtn) {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="bi bi-send"></i>';
            }
            
            if (data.success) {
                messageInput.value = '';
                // Enable auto scroll when sending a message
                autoScrollEnabled = true;
                updateAutoScrollButton();
                
                // Load messages after a short delay to show the sent message
                setTimeout(() => {
                    loadMessages();
                }, 300);
            } else {
                console.error('Send message error:', data.error);
                alert('Error: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            
            // Re-enable send button
            if (sendBtn) {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="bi bi-send"></i>';
            }
            
            alert('Gagal mengirim pesan: ' + error.message);
        });
    }
    
    if (sendBtn) {
        sendBtn.addEventListener('click', sendMessage);
    }
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Stop notifications when user focuses on input
        messageInput.addEventListener('focus', function() {
            stopNotificationLoop();
        });
    }
    
    // Load messages with debouncing to prevent rapid reloads
    let isLoadingMessages = false;

    function loadMessages() {
        if (!idchat || idchat === 0) {
            return;
        }

        // Prevent multiple simultaneous requests
        if (isLoadingMessages) {
            return;
        }

        isLoadingMessages = true;

        // Store current scroll position to maintain it
        const scrollTop = chatMessages ? chatMessages.scrollTop : 0;
        const scrollHeight = chatMessages ? chatMessages.scrollHeight : 0;
        const wasAtBottom = chatMessages ? (scrollHeight - scrollTop - chatMessages.clientHeight < 50) : true;

        fetch(apiPath + 'get_messages.php?idchat=' + idchat + '&_=' + Date.now())
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response text:', text);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    displayMessages(data.messages);

                    // Restore scroll position or scroll to bottom if user was at bottom
                    if (chatMessages) {
                        if (wasAtBottom || autoScrollEnabled) {
                            setTimeout(() => {
                                chatMessages.scrollTop = chatMessages.scrollHeight;
                            }, 50);
                        }
                    }
                } else {
                    console.error('API Error:', data.error);
                }
            })
            .catch(error => {
                console.error('Error loading messages:', error);
            })
            .finally(() => {
                isLoadingMessages = false;
            });
    }

    // Add debouncing to prevent rapid message reloads
    let messageLoadTimeout = null;

    function displayMessages(messages) {
        if (!chatMessages) {
            console.error('chatMessages element not found');
            return;
        }

        // Add updating class to prevent animations during update
        chatMessages.classList.add('updating');

        // Store current scroll position
        const wasAtBottom = chatMessages.scrollHeight - chatMessages.scrollTop - chatMessages.clientHeight < 50;

        chatMessages.innerHTML = '';

        if (!messages || messages.length === 0) {
            chatMessages.innerHTML = `
                <div style="text-align: center; color: #999; padding: 40px 20px;">
                    <i class="bi bi-chat-dots" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                    <h4>Belum ada pesan</h4>
                    <p>Mulai percakapan dengan mengirim pesan!</p>
                </div>
            `;
            return;
        }

        messages.forEach(message => {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + (message.is_admin ? 'admin' : (message.email === currentEmail ? 'sent' : 'received'));

            const time = new Date(message.created_at).toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit'
            });

            const messageText = message.message || '[Pesan kosong]';

            messageDiv.innerHTML = `
                <div>${messageText}</div>
                <div class="message-time">${time}</div>
            `;

            chatMessages.appendChild(messageDiv);
        });

        // Remove updating class after a short delay
        setTimeout(() => {
            if (chatMessages) {
                chatMessages.classList.remove('updating');
            }
        }, 100);

        // Auto scroll to bottom if enabled or user was at bottom
        if (autoScrollEnabled || wasAtBottom) {
            setTimeout(() => {
                scrollToBottom();
            }, 150);
        }
    }

    // Check for new messages
    function checkNewMessages() {
        fetch(apiPath + 'cekpesan_livechat.php?v=' + Date.now())
            .then(response => {
                return response.json();
            })
            .then(data => {
                const unreadCount = data.unread || 0;

                if (unreadCount > 0 && !isPopupOpen) {
                    if (unreadBadge) {
                        unreadBadge.textContent = unreadCount;
                        unreadBadge.style.display = 'inline-block';
                    }
                    if (toggleUnreadBadge) {
                        toggleUnreadBadge.textContent = unreadCount;
                        toggleUnreadBadge.style.display = 'inline-block';
                    }

                    // If there are new unread messages (increased count), trigger notification
                    if (unreadCount > lastUnreadCount && !isPopupOpen) {
                        // Start notification loop
                        startNotificationLoop();

                        // Add shake animation to toggle button
                        if (toggle) {
                            toggle.classList.add('shake');
                            setTimeout(() => {
                                toggle.classList.remove('shake');
                            }, 600);
                        }
                    }

                    // Stop notification if no unread messages or popup is open
                    if (unreadCount === 0 || isPopupOpen) {
                        stopNotificationLoop();
                    }
                } else if (unreadCount === 0 || isPopupOpen) {
                    if (unreadBadge) {
                        unreadBadge.style.display = 'none';
                    }
                    if (toggleUnreadBadge) {
                        toggleUnreadBadge.style.display = 'none';
                    }
                }

                lastUnreadCount = unreadCount;

                // Only load messages if popup is open AND there are actually new messages
                // This prevents constant reloading that causes the jumping effect
                if (isPopupOpen && unreadCount > lastUnreadCount) {
                    // Use debouncing to prevent rapid successive calls
                    if (messageLoadTimeout) {
                        clearTimeout(messageLoadTimeout);
                    }
                    messageLoadTimeout = setTimeout(() => {
                        if (!isLoadingMessages) {
                            loadMessages();
                        }
                    }, 500); // Increased delay to reduce jumping
                }
            })
            .catch(error => {
                console.error('Error checking messages:', error);
                // Don't show alert for background checks, just log
            });
    }

    function markMessagesAsRead() {
        fetch(apiPath + 'mark_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                idchat: idchat
            })
        });
    }

    // Click outside to close popup
    document.addEventListener('click', function(event) {
        if (isPopupOpen && popup && toggle && !popup.contains(event.target) && !toggle.contains(event.target)) {
            closePopup();
        }
    });

    // Prevent popup from closing when clicking inside it
    if (popup) {
        popup.addEventListener('click', function(event) {
            event.stopPropagation();
        });

        // Prevent scroll events from propagating to body
        popup.addEventListener('wheel', function(e) {
            // Only prevent if the scroll is not within scrollable areas
            const target = e.target;
            const isScrollableArea = target.closest('.chat-messages') || target.closest('.chat-users-list');

            if (!isScrollableArea) {
                e.preventDefault();
                e.stopPropagation();
            }
        }, { passive: false });
    }

    // Admin inbox functions
    function loadChatUsers() {
        if (!isAdmin) {
            return;
        }

        // Prevent multiple simultaneous requests
        if (isLoadingChatUsers) {
            return;
        }

        isLoadingChatUsers = true;

        // Show loading state
        const chatUsersList = document.getElementById('chat-users-list');
        if (chatUsersList) {
            chatUsersList.innerHTML = `
                <div class="no-chats">
                    <i class="bi bi-clock"></i>
                    <h5>Loading...</h5>
                    <p>Memuat daftar chat...</p>
                </div>
            `;
        }

        fetch(apiPath + 'get_chat_users.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        const parsed = JSON.parse(text);
                        return parsed;
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response text:', text);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    displayChatUsers(data.chat_users);
                } else {
                    console.error('Error loading chat users:', data.error);
                    // Show error in UI
                    const chatUsersList = document.getElementById('chat-users-list');
                    if (chatUsersList) {
                        chatUsersList.innerHTML = `
                            <div class="no-chats">
                                <i class="bi bi-exclamation-triangle"></i>
                                <h5>Error Loading Chats</h5>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                }
            })
            .catch(error => {
                console.error('Error loading chat users:', error);
                // Show error in UI
                const chatUsersList = document.getElementById('chat-users-list');
                if (chatUsersList) {
                    chatUsersList.innerHTML = `
                        <div class="no-chats">
                            <i class="bi bi-exclamation-triangle"></i>
                            <h5>Connection Error</h5>
                            <p>Failed to load chat users: ${error.message}</p>
                        </div>
                    `;
                }
            })
            .finally(() => {
                // Always reset loading flag
                isLoadingChatUsers = false;
            });
    }

    function displayChatUsers(chatUsers) {
        const chatUsersList = document.getElementById('chat-users-list');
        if (!chatUsersList) {
            console.error('chat-users-list element not found');
            return;
        }

        if (!chatUsers || chatUsers.length === 0) {
            chatUsersList.innerHTML = `
                <div class="no-chats">
                    <i class="bi bi-chat-dots"></i>
                    <h5>Belum Ada Chat</h5>
                    <p>Belum ada user yang mengirim pesan</p>
                </div>
            `;

            // Hide badges if no chats
            updateAdminBadges(0);
            return;
        }

        chatUsersList.innerHTML = '';
        let totalUnreadCount = 0;

        chatUsers.forEach(user => {
            const userDiv = document.createElement('div');
            userDiv.className = 'chat-user-item';
            userDiv.dataset.idchat = user.idchat;
            userDiv.dataset.email = user.email;

            const timeAgo = getTimeAgo(user.last_message_time);

            // Count total unread messages
            if (user.unread_count > 0) {
                totalUnreadCount += parseInt(user.unread_count);
            }

            userDiv.innerHTML = `
                <div class="user-info">
                    <div>
                        <div class="user-name">${user.username}</div>
                        <div class="user-email">${user.email}</div>
                    </div>
                    <div class="message-time">${timeAgo}</div>
                </div>
                <div class="last-message">${user.last_message}</div>
                ${user.unread_count > 0 ? `<div class="unread-count">${user.unread_count}</div>` : ''}
            `;

            userDiv.addEventListener('click', function() {
                selectChatUser(user);
            });

            chatUsersList.appendChild(userDiv);
        });

        // Update badges and notifications for admin
        updateAdminBadges(totalUnreadCount);

        // Play notification sound if there are new messages and popup is closed
        if (totalUnreadCount > lastAdminUnreadCount && !isPopupOpen) {
            startNotificationLoop();

            // Shake toggle button
            if (toggle) {
                toggle.classList.add('shake');
                setTimeout(() => {
                    toggle.classList.remove('shake');
                }, 600);
            }
        }

        lastAdminUnreadCount = totalUnreadCount;
    }

    function updateAdminBadges(unreadCount) {
        if (unreadCount > 0) {
            // Show badge on toggle button
            if (toggleUnreadBadge) {
                toggleUnreadBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                toggleUnreadBadge.style.display = 'inline-block';
            }

            // Show badge on popup header
            if (unreadBadge) {
                unreadBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                unreadBadge.style.display = 'inline-block';
            }
        } else {
            // Hide badges
            if (toggleUnreadBadge) {
                toggleUnreadBadge.style.display = 'none';
            }
            if (unreadBadge) {
                unreadBadge.style.display = 'none';
            }

            // Stop notifications
            stopNotificationLoop();
        }
    }

    function selectChatUser(user) {
        currentChatUser = user;
        idchat = user.idchat;
        inboxView = false;

        // Update header
        const headerTitle = document.querySelector('.header-title');
        const headerSubtitle = document.querySelector('.header-subtitle');
        if (headerTitle) headerTitle.textContent = user.username;
        if (headerSubtitle) headerSubtitle.textContent = user.email;

        showChatView();
        loadMessages();
        markMessagesAsRead();
    }

    function showInboxView() {
        const adminInbox = document.getElementById('admin-inbox');
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.querySelector('.chat-input');
        const backBtn = document.getElementById('back-to-inbox');
        const headerTitle = document.querySelector('.header-title');
        const headerSubtitle = document.querySelector('.header-subtitle');

        // Use classes instead of inline styles for better CSS control
        if (adminInbox) {
            adminInbox.classList.remove('hidden');
            adminInbox.classList.add('visible');
            adminInbox.style.display = 'flex';
        }
        if (chatMessages) {
            chatMessages.classList.add('hidden');
            chatMessages.classList.remove('visible');
            chatMessages.style.display = 'none';
        }
        if (chatInput) {
            chatInput.classList.add('hidden');
            chatInput.classList.remove('visible');
            chatInput.style.display = 'none';
        }
        if (backBtn) backBtn.style.display = 'none';

        if (headerTitle) headerTitle.textContent = 'Admin Inbox';
        if (headerSubtitle) headerSubtitle.textContent = 'Kelola Chat Users';

        inboxView = true;
        currentChatUser = null;
    }

    function showChatView() {
        const adminInbox = document.getElementById('admin-inbox');
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.querySelector('.chat-input');
        const backBtn = document.getElementById('back-to-inbox');

        // Use classes instead of inline styles for better CSS control
        if (adminInbox) {
            adminInbox.classList.add('hidden');
            adminInbox.classList.remove('visible');
            adminInbox.style.display = 'none';
        }
        if (chatMessages) {
            chatMessages.classList.remove('hidden');
            chatMessages.classList.add('visible');
            chatMessages.style.display = 'block';
        }
        if (chatInput) {
            chatInput.classList.remove('hidden');
            chatInput.classList.add('visible');
            chatInput.style.display = 'flex';
        }
        if (backBtn) backBtn.style.display = 'flex';

        inboxView = false;
    }

    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Baru saja';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' menit lalu';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' jam lalu';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' hari lalu';

        return date.toLocaleDateString('id-ID');
    }

    // Start checking for new messages only if user is logged in
    if (isLoggedIn) {
        if (isAdmin) {
            // Load chat users immediately for admin
            loadChatUsers();

            // Single interval for admin - handles both inbox and chat views
            setInterval(() => {
                if (inboxView && !isLoadingChatUsers) {
                    loadChatUsers();
                } else if (!inboxView && idchat) {
                    // Only check for new messages in chat view, don't auto-reload
                    checkNewMessages();
                }
            }, 8000); // Further reduced frequency to prevent jumping
        } else {
            // Single interval for regular users
            setInterval(() => {
                if (!isPopupOpen) {
                    checkNewMessages(); // Only check when popup is closed
                }
            }, 8000); // Further increased interval to reduce jumping

            // Initial check
            checkNewMessages();
        }
    }

    // Initialize the livechat
    initializeLivechat();
});
