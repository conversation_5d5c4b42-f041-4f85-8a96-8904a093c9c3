<?php
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Don't start session if already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['error' => 'Not logged in']);
    exit;
}

// Use correct path for database include
$database_path = __DIR__ . '/../database/database.php';
if (file_exists($database_path)) {
    include $database_path;
} else {
    include '../database/database.php';
}

// Check if database connection exists
if (!isset($conn) || !$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$email = $_SESSION['email'];

try {
    // Check if user is admin
    $stmt = $conn->prepare("SELECT `group` FROM users WHERE email = ?");
    if (!$stmt) {
        throw new Exception("Failed to prepare admin check statement");
    }
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if (!$user || $user['group'] !== 'ADMIN') {
        echo json_encode(['error' => 'Access denied - Admin only']);
        exit;
    }

    // Get all unique chat users with their latest message and unread count
    $stmt = $conn->prepare("
        SELECT DISTINCT
            c.idchat,
            c.email
        FROM chats c
        WHERE c.email != ? AND c.is_admin = 0
        ORDER BY c.created_at DESC
    ");
    
    if (!$stmt) {
        throw new Exception("Failed to prepare chat users statement");
    }
    
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $chat_users = [];
    $debug_info = [
        'admin_email' => $email,
        'total_rows' => $result->num_rows,
        'query_executed' => true
    ];
    while ($row = $result->fetch_assoc()) {
        // Get user display name (use email username part)
        $username = explode('@', $row['email'])[0];
        
        // Check if user exists in users table
        $user_stmt = $conn->prepare("SELECT email FROM users WHERE email = ?");
        if ($user_stmt) {
            $user_stmt->bind_param("s", $row['email']);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();
            $user_data = $user_result->fetch_assoc();
            if (!$user_data) {
                $username = 'Unknown User';
            }
            $user_stmt->close();
        }
        
        // Get unread count for this chat (non-admin messages that are unread)
        $unread_stmt = $conn->prepare("SELECT COUNT(*) as unread_count FROM chats WHERE idchat = ? AND dibaca = 0 AND email = ?");
        if ($unread_stmt) {
            $unread_stmt->bind_param("is", $row['idchat'], $row['email']);
            $unread_stmt->execute();
            $unread_result = $unread_stmt->get_result();
            $unread_data = $unread_result->fetch_assoc();
            $unread_count = (int)$unread_data['unread_count'];
            $unread_stmt->close();
        } else {
            $unread_count = 0;
        }
        
        // Get last message for this chat
        $msg_stmt = $conn->prepare("SELECT message, created_at FROM chats WHERE idchat = ? ORDER BY created_at DESC LIMIT 1");
        if ($msg_stmt) {
            $msg_stmt->bind_param("i", $row['idchat']);
            $msg_stmt->execute();
            $msg_result = $msg_stmt->get_result();
            $msg_data = $msg_result->fetch_assoc();
            $msg_stmt->close();
        } else {
            $msg_data = null;
        }
        
        $chat_users[] = [
            'idchat' => $row['idchat'],
            'email' => $row['email'],
            'username' => $username,
            'unread_count' => $unread_count,
            'last_message' => $msg_data ? $msg_data['message'] : 'No messages',
            'last_message_time' => $msg_data ? $msg_data['created_at'] : null
        ];
        

    }
    
    echo json_encode([
        'success' => true, 
        'chat_users' => $chat_users,
        'debug' => $debug_info
    ]);
    
} catch (Exception $e) {
    echo json_encode(['error' => 'Failed to get chat users: ' . $e->getMessage()]);
} finally {
    if (isset($stmt) && $stmt !== false) {
        $stmt->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
?> 