<?php
// Setup Database untuk QRIS Logger
// Script ini akan membuat tabel log_qris jika belum ada

// Include database connection
require_once __DIR__ . '/../database/database.php';

echo "=== QRIS Database Setup ===\n";

try {
    // Create log_qris table
    $sql = "CREATE TABLE IF NOT EXISTS `log_qris` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `payment_id` varchar(255) NOT NULL UNIQUE,
        `amount` decimal(15,2) NOT NULL,
        `type` enum('CR','DR') NOT NULL COMMENT 'CR=Credit(Masuk), DR=Debit(Keluar)',
        `qris_type` enum('static','dynamic') DEFAULT NULL,
        `brand_name` varchar(100) DEFAULT NULL COMMENT 'Nama bank/provider',
        `buyer_reff` varchar(255) DEFAULT NULL COMMENT 'Referensi pengirim',
        `issuer_reff` varchar(255) DEFAULT NULL COMMENT 'Referensi bank',
        `balance` decimal(15,2) DEFAULT NULL COMMENT 'Saldo akhir setelah transaksi',
        `transaction_date` datetime NOT NULL,
        `webhook_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Status pengiriman webhook ke Discord',
        `raw_data` json DEFAULT NULL COMMENT 'Data mentah dari API QRIS',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_payment_id` (`payment_id`),
        KEY `idx_type` (`type`),
        KEY `idx_brand_name` (`brand_name`),
        KEY `idx_transaction_date` (`transaction_date`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_amount` (`amount`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ Table log_qris created successfully or already exists\n";
    } else {
        echo "❌ Error creating table: " . $conn->error . "\n";
        exit(1);
    }
    
    // Create additional indexes for better performance
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_log_qris_date_type ON log_qris(transaction_date, type)",
        "CREATE INDEX IF NOT EXISTS idx_log_qris_amount_date ON log_qris(amount, transaction_date)",
        "CREATE INDEX IF NOT EXISTS idx_log_qris_webhook_status ON log_qris(webhook_sent)"
    ];
    
    foreach ($indexes as $index_sql) {
        if ($conn->query($index_sql) === TRUE) {
            echo "✅ Index created successfully\n";
        } else {
            echo "⚠️ Index creation skipped (may already exist): " . $conn->error . "\n";
        }
    }
    
    // Insert sample data if table is empty (for testing)
    $count_result = $conn->query("SELECT COUNT(*) as count FROM log_qris");
    $count = $count_result->fetch_assoc()['count'];
    
    if ($count == 0) {
        echo "\n📝 Inserting sample data for testing...\n";
        
        $sample_data = [
            [
                'payment_id' => 'QRIS_SAMPLE_001',
                'amount' => 50000,
                'type' => 'CR',
                'qris_type' => 'static',
                'brand_name' => 'BCA',
                'buyer_reff' => 'John Doe',
                'issuer_reff' => 'REF001BCA',
                'balance' => 1500000,
                'transaction_date' => date('Y-m-d H:i:s', time() - 3600),
                'webhook_sent' => 1,
                'raw_data' => json_encode([
                    'amount' => 50000,
                    'type' => 'CR',
                    'brand_name' => 'BCA',
                    'buyer_reff' => 'John Doe',
                    'issuer_reff' => 'REF001BCA',
                    'date' => date('d/m/Y H:i:s', time() - 3600),
                    'balance' => 1500000
                ])
            ],
            [
                'payment_id' => 'QRIS_SAMPLE_002',
                'amount' => 25000,
                'type' => 'CR',
                'qris_type' => 'dynamic',
                'brand_name' => 'BRI',
                'buyer_reff' => 'Jane Smith',
                'issuer_reff' => 'REF002BRI',
                'balance' => 1525000,
                'transaction_date' => date('Y-m-d H:i:s', time() - 1800),
                'webhook_sent' => 1,
                'raw_data' => json_encode([
                    'amount' => 25000,
                    'type' => 'CR',
                    'brand_name' => 'BRI',
                    'buyer_reff' => 'Jane Smith',
                    'issuer_reff' => 'REF002BRI',
                    'date' => date('d/m/Y H:i:s', time() - 1800),
                    'balance' => 1525000
                ])
            ],
            [
                'payment_id' => 'QRIS_SAMPLE_003',
                'amount' => 15000,
                'type' => 'DR',
                'qris_type' => 'static',
                'brand_name' => 'MANDIRI',
                'buyer_reff' => 'Admin Transfer',
                'issuer_reff' => 'REF003MDR',
                'balance' => 1510000,
                'transaction_date' => date('Y-m-d H:i:s', time() - 600),
                'webhook_sent' => 0,
                'raw_data' => json_encode([
                    'amount' => 15000,
                    'type' => 'DR',
                    'brand_name' => 'MANDIRI',
                    'buyer_reff' => 'Admin Transfer',
                    'issuer_reff' => 'REF003MDR',
                    'date' => date('d/m/Y H:i:s', time() - 600),
                    'balance' => 1510000
                ])
            ]
        ];
        
        foreach ($sample_data as $data) {
            $stmt = $conn->prepare("
                INSERT INTO log_qris 
                (payment_id, amount, type, qris_type, brand_name, buyer_reff, issuer_reff, balance, transaction_date, webhook_sent, raw_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->bind_param(
                "sssssssssss",
                $data['payment_id'],
                $data['amount'],
                $data['type'],
                $data['qris_type'],
                $data['brand_name'],
                $data['buyer_reff'],
                $data['issuer_reff'],
                $data['balance'],
                $data['transaction_date'],
                $data['webhook_sent'],
                $data['raw_data']
            );
            
            if ($stmt->execute()) {
                echo "✅ Sample data inserted: " . $data['payment_id'] . "\n";
            } else {
                echo "❌ Error inserting sample data: " . $stmt->error . "\n";
            }
        }
    } else {
        echo "ℹ️ Table already contains data ($count records)\n";
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "📊 You can now view QRIS history in the admin panel.\n";
    
} catch (Exception $e) {
    echo "❌ Error during setup: " . $e->getMessage() . "\n";
    exit(1);
}

// Test the logger class
echo "\n🧪 Testing QRIS Logger...\n";
try {
    require_once 'qris_logger.php';
    $logger = new QRISLogger();
    $stats = $logger->getStatistics();
    
    echo "📈 Current Statistics:\n";
    echo "   - Total Transactions: " . ($stats['total_transactions'] ?? 0) . "\n";
    echo "   - Total Income: Rp " . number_format($stats['total_income'] ?? 0, 0, ',', '.') . "\n";
    echo "   - Total Expense: Rp " . number_format($stats['total_expense'] ?? 0, 0, ',', '.') . "\n";
    echo "   - Net Balance: Rp " . number_format(($stats['total_income'] ?? 0) - ($stats['total_expense'] ?? 0), 0, ',', '.') . "\n";
    
    echo "✅ QRIS Logger test completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Logger test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Setup Complete ===\n";

// If running via web, return JSON response
if (!php_sapi_name() === 'cli') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Database setup completed successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?> 